#!/usr/bin/env python3
"""
主题管理器 - 统一管理应用程序的主题样式

支持light和dark两种主题，提供完整的UI组件样式定义
"""

from typing import Dict, Any
import os
from PyQt6.QtGui import QPixmap
from ..utils.config import get_config


class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.config = get_config()
        self._current_theme = self.config.get('ui.theme', 'light')
        # 预加载图标以提升性能
        self._preload_icons()

    def _preload_icons(self):
        """预加载图标文件到内存中"""
        try:
            checkmark_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'checkmark.svg')
            if os.path.exists(checkmark_path):
                # 预加载SVG图标
                QPixmap(checkmark_path)
        except Exception:
            pass  # 忽略预加载错误

    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self._current_theme
    
    def set_current_theme(self, theme_name: str):
        """设置当前主题"""
        if theme_name in ['light', 'dark']:
            self._current_theme = theme_name
    
    def get_theme_styles(self, theme_name: str = None) -> Dict[str, str]:
        """获取指定主题的所有样式"""
        if theme_name is None:
            theme_name = self._current_theme
            
        if theme_name == 'dark':
            return self._get_dark_theme_styles()
        else:
            return self._get_light_theme_styles()
    
    def _get_light_theme_styles(self) -> Dict[str, str]:
        """获取light主题样式"""
        return {
            'main_window': """
                QMainWindow {
                    background-color: #ffffff;
                }
            """,
            
            'menu_bar': """
                QMenuBar {
                    background-color: #ffffff;
                    border-bottom: 1px solid #dadce0;
                    padding: 4px;
                    color: #202124;
                }
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                    color: #202124;
                }
                QMenuBar::item:selected {
                    background-color: #f8f9fa;
                }
                QMenu {
                    background-color: #ffffff;
                    border: 1px solid #dadce0;
                    border-radius: 4px;
                    padding: 4px;
                    color: #202124;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 6px 12px;
                    border-radius: 3px;
                    color: #202124;
                }
                QMenu::item:selected {
                    background-color: #f8f9fa;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #dadce0;
                    margin: 4px 8px;
                }
            """,
            
            'scroll_area': """
                QScrollArea {
                    border: none;
                    background-color: #f8f9fa;
                }
                QWidget {
                    background-color: #f8f9fa;
                }
            """,
            
            'frame_white': """
                QFrame {
                    background-color: white;
                    border-radius: 8px;
                    border: 1px solid #dadce0;
                }
            """,
            
            'frame_upload': """
                QFrame {
                    border: 2px dashed #dadce0;
                    border-radius: 8px;
                    background-color: #fafbfc;
                }
            """,
            

            
            'frame_status': """
                QFrame {
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    padding: 8px;
                    margin-top: 6px;
                }
            """,
            
            'combo_box': """
                QComboBox {
                    border: 1px solid #dadce0;
                    border-radius: 4px;
                    padding: 8px 12px;
                    font-size: 13px;
                    background-color: white;
                    color: #202124;
                    min-height: 18px;
                }
                QComboBox:hover {
                    border-color: #4285f4;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #5f6368;
                    margin-right: 5px;
                }
                QComboBox QAbstractItemView {
                    background-color: white;
                    border: 1px solid #dadce0;
                    selection-background-color: #e8f0fe;
                    color: #202124;
                }
            """,
            
            'button_primary': """
                QPushButton {
                    background-color: #4285f4;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #3367d6;
                }
                QPushButton:pressed {
                    background-color: #2851a3;
                }
                QPushButton:disabled {
                    background-color: #f1f3f4;
                    color: #9aa0a6;
                }
            """,
            
            'button_success': """
                QPushButton {
                    background-color: #34a853;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #2d8f47;
                }
                QPushButton:pressed {
                    background-color: #1e7e34;
                }
                QPushButton:disabled {
                    background-color: #f1f3f4;
                    color: #9aa0a6;
                }
            """,
            
            'button_danger': """
                QPushButton {
                    background-color: #ea4335;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #d33b2c;
                }
                QPushButton:pressed {
                    background-color: #b52d20;
                }
                QPushButton:disabled {
                    background-color: #f1f3f4;
                    color: #9aa0a6;
                }
            """,

            'frame_preview': """
                QFrame {
                    border: 1px solid #dadce0;
                    border-radius: 8px;
                    background-color: #fafbfc;
                }
            """,

            'label_placeholder': """
                QLabel {
                    color: #5f6368;
                    font-size: 16px;
                    line-height: 1.5;
                }
            """,

            'button_secondary': """
                QPushButton {
                    background-color: #f8f9fa;
                    color: #5f6368;
                    border: 1px solid #dadce0;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #e8f0fe;
                    border-color: #4285f4;
                }
                QPushButton:pressed {
                    background-color: #d2e3fc;
                }
                QPushButton:disabled {
                    background-color: #f1f3f4;
                    color: #9aa0a6;
                }
            """,

            'frame_button': """
                QFrame {
                    background-color: #f8f9fa;
                    border-top: 1px solid #dadce0;
                    padding: 12px;
                }
            """,
            
            'label_transparent': """
                QLabel {
                    border: none;
                    background-color: transparent;
                }
            """,
            
            'label_placeholder': """
                QLabel {
                    font-size: 14px;
                    color: #9aa0a6;
                    border: none;
                    background-color: transparent;
                    line-height: 1.4;
                }
            """,
            
            'label_info': """
                QLabel {
                    font-size: 11px;
                    color: #34a853;
                    margin-top: 6px;
                    padding: 4px 8px;
                    background-color: #e8f5e8;
                    border-radius: 4px;
                    border: none;
                }
            """,
            
            'label_status': """
                QLabel {
                    font-size: 12px;
                    color: #5f6368;
                    font-weight: 500;
                }
            """,
            
            'progress_bar': """
                QProgressBar {
                    border: none;
                    border-radius: 2px;
                    background-color: #e0e0e0;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #4285f4;
                    border-radius: 2px;
                }
            """,

            'message_box': """
                QMessageBox {
                    background-color: #ffffff;
                    color: #202124;
                }
                QMessageBox QLabel {
                    color: #202124;
                    background-color: transparent;
                }
                QMessageBox QPushButton {
                    background-color: #f8f9fa !important;
                    color: #5f6368 !important;
                    border: 1px solid #dadce0 !important;
                    border-radius: 4px !important;
                    padding: 8px 16px !important;
                    font-weight: 500 !important;
                    min-width: 64px !important;
                }
                QMessageBox QPushButton:hover {
                    background-color: #f1f3f4 !important;
                    border-color: #c4c7c5 !important;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #e8eaed !important;
                }
                QMessageBox QPushButton:default {
                    background-color: #4285f4 !important;
                    color: white !important;
                    border: none !important;
                }
                QMessageBox QPushButton:default:hover {
                    background-color: #3367d6 !important;
                }
                QMessageBox QPushButton:default:pressed {
                    background-color: #2a56c6 !important;
                }
            """,

            'progress_dialog': """
                QProgressDialog {
                    background-color: #ffffff;
                    color: #202124;
                }
                QProgressDialog QLabel {
                    color: #202124;
                    font-size: 13px;
                }
                QProgressDialog QPushButton {
                    background-color: #f8f9fa;
                    color: #5f6368;
                    border: 1px solid #dadce0;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 12px;
                }
                QProgressDialog QPushButton:hover {
                    background-color: #f1f3f4;
                }
            """,

            'status_bar': """
                QStatusBar {
                    background-color: #f8f9fa;
                    border-top: 1px solid #dadce0;
                    color: #5f6368;
                    font-size: 12px;
                    padding: 4px 8px;
                }
            """,
            
            'group_box': """
                QGroupBox {
                    font-size: 13px;
                    font-weight: 600;
                    color: #202124;
                    border: 1px solid #dadce0;
                    border-radius: 6px;
                    margin-top: 8px;
                    padding-top: 8px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
            """,
            
            'checkbox': """
                QCheckBox {
                    font-size: 12px;
                    color: #202124;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                    border-radius: 2px;
                    transition: background-color 0.1s ease, border-color 0.1s ease;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #dadce0;
                    background-color: white;
                }
                QCheckBox::indicator:unchecked:hover {
                    border: 1px solid #4285f4;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #4285f4;
                    background-color: #4285f4;
                    image: url(src/assets/checkmark.svg);
                }
            """,
            
            'line_edit': """
                QLineEdit {
                    border: 1px solid #dadce0;
                    border-radius: 4px;
                    padding: 8px 12px;
                    font-size: 13px;
                    background-color: white;
                    color: #202124;
                    min-height: 20px;
                }
                QLineEdit:hover {
                    border-color: #4285f4;
                }
                QLineEdit:focus {
                    border-color: #4285f4;
                    outline: none;
                }
                QLineEdit:disabled {
                    background-color: #f1f3f4;
                    color: #9aa0a6;
                    border-color: #e8eaed;
                }
            """,

            'spinbox': """
                QSpinBox, QDoubleSpinBox {
                    border: 1px solid #dadce0;
                    border-radius: 4px;
                    padding: 6px 8px;
                    font-size: 12px;
                    background-color: white;
                    color: #202124;
                    min-height: 18px;
                }
                QSpinBox:hover, QDoubleSpinBox:hover {
                    border-color: #4285f4;
                }
            """
        }
    
    def _get_dark_theme_styles(self) -> Dict[str, str]:
        """获取dark主题样式"""
        return {
            'main_window': """
                QMainWindow {
                    background-color: #2b2b2b;
                }
            """,
            
            'menu_bar': """
                QMenuBar {
                    background-color: #2b2b2b;
                    border-bottom: 1px solid #555555;
                    padding: 4px;
                    color: #ffffff;
                }
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                    color: #ffffff;
                }
                QMenuBar::item:selected {
                    background-color: #404040;
                }
                QMenu {
                    background-color: #2b2b2b;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 4px;
                    color: #ffffff;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 6px 12px;
                    border-radius: 3px;
                    color: #ffffff;
                }
                QMenu::item:selected {
                    background-color: #404040;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #555555;
                    margin: 4px 8px;
                }
            """,
            
            'scroll_area': """
                QScrollArea {
                    border: none;
                    background-color: #3c3c3c;
                }
                QWidget {
                    background-color: #3c3c3c;
                }
            """,
            
            'frame_white': """
                QFrame {
                    background-color: #404040;
                    border-radius: 8px;
                    border: 1px solid #555555;
                }
            """,
            
            'frame_upload': """
                QFrame {
                    border: 2px dashed #666666;
                    border-radius: 8px;
                    background-color: #353535;
                }
            """,
            
            'frame_preview': """
                QFrame {
                    border: 1px solid #555555;
                    border-radius: 8px;
                    background-color: #3c3c3c;
                }
            """,
            
            'frame_status': """
                QFrame {
                    background-color: #3c3c3c;
                    border-radius: 4px;
                    padding: 8px;
                    margin-top: 6px;
                }
            """,
            
            'combo_box': """
                QComboBox {
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 8px 12px;
                    font-size: 13px;
                    background-color: #404040;
                    color: #ffffff;
                    min-height: 18px;
                }
                QComboBox:hover {
                    border-color: #4285f4;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #cccccc;
                    margin-right: 5px;
                }
                QComboBox QAbstractItemView {
                    background-color: #404040;
                    border: 1px solid #555555;
                    selection-background-color: #4285f4;
                    color: #ffffff;
                }
            """,
            
            'button_primary': """
                QPushButton {
                    background-color: #4285f4;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #5a95f5;
                }
                QPushButton:pressed {
                    background-color: #3367d6;
                }
                QPushButton:disabled {
                    background-color: #555555;
                    color: #999999;
                }
            """,
            
            'button_success': """
                QPushButton {
                    background-color: #34a853;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #46b566;
                }
                QPushButton:pressed {
                    background-color: #2d8f47;
                }
                QPushButton:disabled {
                    background-color: #555555;
                    color: #999999;
                }
            """,
            
            'button_danger': """
                QPushButton {
                    background-color: #ea4335;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    font-weight: 500;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #ed5a4a;
                }
                QPushButton:pressed {
                    background-color: #d33b2c;
                }
                QPushButton:disabled {
                    background-color: #555555;
                    color: #999999;
                }
            """,



            'label_placeholder': """
                QLabel {
                    color: #cccccc;
                    font-size: 16px;
                    line-height: 1.5;
                }
            """,

            'button_secondary': """
                QPushButton {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 13px;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #4285f4;
                    border-color: #4285f4;
                }
                QPushButton:pressed {
                    background-color: #3367d6;
                }
                QPushButton:disabled {
                    background-color: #555555;
                    color: #999999;
                }
            """,

            'frame_button': """
                QFrame {
                    background-color: #3c3c3c;
                    border-top: 1px solid #555555;
                    padding: 12px;
                }
            """,
            
            'label_transparent': """
                QLabel {
                    border: none;
                    background-color: transparent;
                    color: #ffffff;
                }
            """,
            

            
            'label_info': """
                QLabel {
                    font-size: 11px;
                    color: #34a853;
                    margin-top: 6px;
                    padding: 4px 8px;
                    background-color: #2d4a32;
                    border-radius: 4px;
                    border: none;
                }
            """,
            
            'label_status': """
                QLabel {
                    font-size: 12px;
                    color: #cccccc;
                    font-weight: 500;
                }
            """,
            
            'progress_bar': """
                QProgressBar {
                    border: none;
                    border-radius: 2px;
                    background-color: #555555;
                    text-align: center;
                    color: #ffffff;
                }
                QProgressBar::chunk {
                    background-color: #4285f4;
                    border-radius: 2px;
                }
            """,

            'message_box': """
                QMessageBox {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QMessageBox QLabel {
                    color: #ffffff;
                    background-color: transparent;
                }
                QMessageBox QPushButton {
                    background-color: #404040 !important;
                    color: #cccccc !important;
                    border: 1px solid #555555 !important;
                    border-radius: 4px !important;
                    padding: 8px 16px !important;
                    font-weight: 500 !important;
                    min-width: 64px !important;
                }
                QMessageBox QPushButton:hover {
                    background-color: #4a4a4a !important;
                    border-color: #666666 !important;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #353535 !important;
                }
                QMessageBox QPushButton:default {
                    background-color: #4285f4 !important;
                    color: white !important;
                    border: none !important;
                }
                QMessageBox QPushButton:default:hover {
                    background-color: #3367d6 !important;
                }
                QMessageBox QPushButton:default:pressed {
                    background-color: #2a56c6 !important;
                }
            """,

            'progress_dialog': """
                QProgressDialog {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QProgressDialog QLabel {
                    color: #ffffff;
                    font-size: 13px;
                }
                QProgressDialog QPushButton {
                    background-color: #404040;
                    color: #cccccc;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 12px;
                }
                QProgressDialog QPushButton:hover {
                    background-color: #4a4a4a;
                }
            """,

            'status_bar': """
                QStatusBar {
                    background-color: #3c3c3c;
                    border-top: 1px solid #555555;
                    color: #cccccc;
                    font-size: 12px;
                    padding: 4px 8px;
                }
            """,
            
            'group_box': """
                QGroupBox {
                    font-size: 13px;
                    font-weight: 600;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 6px;
                    margin-top: 8px;
                    padding-top: 8px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
            """,
            
            'checkbox': """
                QCheckBox {
                    font-size: 12px;
                    color: #ffffff;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                    border-radius: 2px;
                    transition: background-color 0.1s ease, border-color 0.1s ease;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #555555;
                    background-color: #404040;
                }
                QCheckBox::indicator:unchecked:hover {
                    border: 1px solid #4285f4;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #4285f4;
                    background-color: #4285f4;
                    image: url(src/assets/checkmark.svg);
                }
            """,
            
            'line_edit': """
                QLineEdit {
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 8px 12px;
                    font-size: 13px;
                    background-color: #404040;
                    color: #ffffff;
                    min-height: 20px;
                }
                QLineEdit:hover {
                    border-color: #4285f4;
                }
                QLineEdit:focus {
                    border-color: #4285f4;
                    outline: none;
                }
                QLineEdit:disabled {
                    background-color: #2d2d2d;
                    color: #666666;
                    border-color: #444444;
                }
            """,

            'spinbox': """
                QSpinBox, QDoubleSpinBox {
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 6px 8px;
                    font-size: 12px;
                    background-color: #404040;
                    color: #ffffff;
                    min-height: 18px;
                }
                QSpinBox:hover, QDoubleSpinBox:hover {
                    border-color: #4285f4;
                }
            """
        }


# 全局主题管理器实例
_theme_manager = None


def get_theme_manager() -> ThemeManager:
    """获取全局主题管理器实例"""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager
