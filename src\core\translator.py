#!/usr/bin/env python3
"""
翻译引擎模块 - 基于NLLB-200的多语言翻译

提供多语言文本翻译功能，支持200+种语言
"""

import os
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

try:
    import torch
    from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import get_config
from ..utils.device_manager import get_device_manager
from ..utils.language_mapping import NLLB_LANGUAGES


@dataclass
class TranslationResult:
    """翻译结果数据类"""
    source_text: str           # 原文
    translated_text: str       # 译文
    source_language: str       # 源语言代码
    target_language: str       # 目标语言代码
    confidence: float = 0.0    # 翻译置信度
    processing_time: float = 0.0  # 处理时间


class Translator:
    """翻译引擎类"""
    
    def __init__(self, model_name: str = None):
        """
        初始化翻译引擎

        Args:
            model_name: 模型名称，如果为None则从配置文件获取
        """
        self.logger = get_logger()
        self.config = get_config()

        # 从配置文件获取模型名称
        if model_name is None:
            model_name = self.config.get('models.translation.default_model', "facebook/nllb-200-distilled-600M")
        self.model_name = model_name

        # 从配置文件获取模型存储路径
        self.model_cache_dir = self.config.get('models.translation.path', 'models/nllb-200')

        # 确保模型缓存目录存在
        os.makedirs(self.model_cache_dir, exist_ok=True)

        # 设备管理器
        self.device_manager = get_device_manager()
        self.device = self.device_manager.get_device_for_model('translation')
        self.dtype = self.device_manager.get_optimal_dtype()

        # 模型和分词器
        self.model = None
        self.tokenizer = None
        
        # 不再需要语言映射器，直接使用NLLB语言代码

        self.logger.info(f"翻译引擎初始化完成，模型: {model_name}，设备: {self.device}，数据类型: {self.dtype}")
    
    def _load_model(self):
        """加载翻译模型"""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers库未安装，请先安装: pip install transformers torch")
        
        if self.model is None:
            try:
                self.logger.info(f"正在加载翻译模型: {self.model_name}")
                start_time = time.time()

                # 检查GPU内存（如果使用GPU）
                if self.device.type != 'cpu':
                    required_memory = 2000  # 估计需要2GB内存
                    if not self.device_manager.check_memory_available(required_memory):
                        self.logger.warning("GPU内存不足，回退到CPU")
                        self.device = torch.device('cpu')
                        self.dtype = torch.float32

                # 加载分词器
                self.logger.info(f"📁 模型缓存路径: {os.path.abspath(self.model_cache_dir)}")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    cache_dir=self.model_cache_dir
                )

                # 加载模型
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    self.model_name,
                    cache_dir=self.model_cache_dir,
                    torch_dtype=self.dtype
                )

                # 移动到指定设备
                self.model = self.model.to(self.device)

                # 设置为评估模式
                self.model.eval()

                load_time = time.time() - start_time
                self.logger.info(f"翻译模型加载成功，设备: {self.device}，耗时: {load_time:.2f}秒")
                
            except Exception as e:
                self.logger.error(f"翻译模型加载失败: {e}")
                raise
    
    def _normalize_language_code(self, lang_code: str) -> str:
        """
        标准化语言代码为NLLB格式

        Args:
            lang_code: 输入的语言代码

        Returns:
            NLLB格式的语言代码
        """
        # 直接检查是否为NLLB代码
        if lang_code in NLLB_LANGUAGES:
            return lang_code

        # 如果已经是NLLB格式，直接返回
        if "_" in lang_code and len(lang_code.split("_")) == 2:
            return lang_code

        # 默认返回英语
        self.logger.warning(f"未知语言代码: {lang_code}，使用默认英语")
        return "eng_Latn"

    def _calculate_translation_confidence(self, inputs, generated_tokens, translated_text: str) -> float:
        """
        计算翻译置信度

        Args:
            inputs: 输入tokens
            generated_tokens: 生成的tokens
            translated_text: 翻译后的文本

        Returns:
            置信度分数 (0.0-1.0)
        """
        try:
            if not TRANSFORMERS_AVAILABLE or self.model is None:
                return 0.8  # 默认置信度

            # 基于文本长度的基础置信度
            base_confidence = 0.7

            # 处理不同类型的inputs
            if hasattr(inputs, 'input_ids'):
                source_len = len(inputs.input_ids[0])
            elif isinstance(inputs, dict) and 'input_ids' in inputs:
                source_len = len(inputs['input_ids'][0])
            else:
                # 如果无法获取输入长度，使用文本长度估算
                source_len = len(translated_text) // 2  # 粗略估算

            # 处理生成的tokens
            if hasattr(generated_tokens, 'shape'):
                target_len = generated_tokens.shape[1] - source_len
            elif isinstance(generated_tokens, (list, tuple)) and len(generated_tokens) > 0:
                if hasattr(generated_tokens[0], '__len__'):
                    target_len = len(generated_tokens[0]) - source_len
                else:
                    target_len = len(generated_tokens) - source_len
            else:
                target_len = len(translated_text)  # 使用翻译文本长度

            if target_len > 0:
                length_ratio = target_len / source_len
                # 理想的长度比例在0.5-2.0之间
                if 0.5 <= length_ratio <= 2.0:
                    length_factor = 0.1
                else:
                    length_factor = -0.1 * abs(length_ratio - 1.0)
            else:
                length_factor = -0.2

            # 文本质量因子：检查是否包含特殊字符或重复
            quality_factor = 0.0
            if translated_text and len(translated_text.strip()) > 0:
                # 检查重复字符
                if not self._has_excessive_repetition(translated_text):
                    quality_factor += 0.1

                # 检查是否包含合理的字符
                if self._has_reasonable_characters(translated_text):
                    quality_factor += 0.1
            else:
                quality_factor = -0.3

            # 计算最终置信度
            confidence = base_confidence + length_factor + quality_factor

            # 确保置信度在合理范围内
            confidence = max(0.1, min(1.0, confidence))

            return confidence

        except Exception as e:
            self.logger.debug(f"置信度计算失败: {e}")
            return 0.7  # 默认置信度

    def _has_excessive_repetition(self, text: str) -> bool:
        """检查文本是否有过度重复"""
        if len(text) < 10:
            return False

        # 检查连续重复的字符
        for i in range(len(text) - 3):
            if text[i] == text[i+1] == text[i+2] == text[i+3]:
                return True

        # 检查重复的短语
        words = text.split()
        if len(words) >= 4:
            for i in range(len(words) - 3):
                if words[i] == words[i+2] and words[i+1] == words[i+3]:
                    return True

        return False

    def _has_reasonable_characters(self, text: str) -> bool:
        """检查文本是否包含合理的字符"""
        if not text:
            return False

        # 检查是否主要由字母、数字、常见标点和中文字符组成
        reasonable_chars = 0
        total_chars = len(text)

        for char in text:
            if (char.isalnum() or
                char in '.,!?;:()[]{}"\'-—–' or
                '\u4e00' <= char <= '\u9fff' or  # 中文字符
                '\u3040' <= char <= '\u309f' or  # 平假名
                '\u30a0' <= char <= '\u30ff' or  # 片假名
                '\uac00' <= char <= '\ud7af'):   # 韩文字符
                reasonable_chars += 1

        return reasonable_chars / total_chars > 0.8
    
    def translate(self, text: str, source_lang: str, target_lang: str) -> TranslationResult:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            翻译结果
        """
        start_time = time.time()
        
        try:
            # 加载模型
            self._load_model()
            
            # 标准化语言代码
            src_lang = self._normalize_language_code(source_lang)
            tgt_lang = self._normalize_language_code(target_lang)
            
            self.logger.debug(f"翻译: '{text}' ({src_lang} -> {tgt_lang})")
            
            # 设置分词器的源语言
            self.tokenizer.src_lang = src_lang
            
            # 编码输入文本
            inputs = self.tokenizer(text, return_tensors="pt", padding=True, truncation=True)

            # 将输入张量移动到模型设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 生成翻译
            with torch.no_grad():
                # 获取目标语言的token ID
                try:
                    # 尝试获取目标语言ID
                    if hasattr(self.tokenizer, 'convert_tokens_to_ids'):
                        tgt_lang_id = self.tokenizer.convert_tokens_to_ids(tgt_lang)

                        # 如果获取到有效的ID，确保它在正确的设备上
                        if tgt_lang_id is not None and tgt_lang_id != self.tokenizer.unk_token_id:
                            # 将整数ID转换为张量并移动到正确设备
                            if isinstance(tgt_lang_id, int):
                                tgt_lang_id = torch.tensor(tgt_lang_id, device=self.device)
                            elif isinstance(tgt_lang_id, torch.Tensor):
                                tgt_lang_id = tgt_lang_id.to(self.device)
                        else:
                            tgt_lang_id = None
                    else:
                        # 使用默认方式
                        tgt_lang_id = None

                    generate_kwargs = {
                        **inputs,
                        'max_length': 512,
                        'num_beams': 5,
                        'early_stopping': True
                    }

                    # 如果有目标语言ID，添加forced_bos_token_id
                    if tgt_lang_id is not None:
                        generate_kwargs['forced_bos_token_id'] = tgt_lang_id

                    generated_tokens = self.model.generate(**generate_kwargs)

                except Exception as e:
                    self.logger.warning(f"使用目标语言ID失败，使用默认生成: {e}")
                    generated_tokens = self.model.generate(
                        **inputs,
                        max_length=512,
                        num_beams=5,
                        early_stopping=True
                    )
            
            # 解码结果
            translated_text = self.tokenizer.batch_decode(
                generated_tokens, 
                skip_special_tokens=True
            )[0]
            
            processing_time = time.time() - start_time
            
            # 计算置信度（基于生成概率）
            try:
                confidence = self._calculate_translation_confidence(
                    inputs, generated_tokens, translated_text
                )
            except Exception as e:
                self.logger.debug(f"置信度计算失败: {e}")
                confidence = 0.8  # 使用默认置信度

            # 创建翻译结果
            result = TranslationResult(
                source_text=text,
                translated_text=translated_text,
                source_language=src_lang,
                target_language=tgt_lang,
                confidence=confidence,
                processing_time=processing_time
            )
            
            self.logger.debug(f"翻译完成: '{translated_text}' (耗时: {processing_time:.3f}s)")
            return result
            
        except Exception as e:
            self.logger.error(f"翻译失败: {e}")
            # 返回失败结果
            return TranslationResult(
                source_text=text,
                translated_text=text,  # 翻译失败时返回原文
                source_language=source_lang,
                target_language=target_lang,
                confidence=0.0,
                processing_time=time.time() - start_time
            )
    
    def translate_batch(self, texts: List[str], source_lang: str, target_lang: str) -> List[TranslationResult]:
        """
        批量翻译文本
        
        Args:
            texts: 要翻译的文本列表
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            翻译结果列表
        """
        results = []
        
        self.logger.info(f"开始批量翻译 {len(texts)} 个文本")
        
        for i, text in enumerate(texts):
            if text.strip():  # 跳过空文本
                result = self.translate(text, source_lang, target_lang)
                results.append(result)
                self.logger.debug(f"批量翻译进度: {i+1}/{len(texts)}")
            else:
                # 空文本直接返回空结果
                results.append(TranslationResult(
                    source_text=text,
                    translated_text=text,
                    source_language=source_lang,
                    target_language=target_lang
                ))
        
        self.logger.info(f"批量翻译完成，共处理 {len(results)} 个文本")
        return results
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "zho_Hans": "中文 (简体)",
            "zho_Hant": "中文 (繁體)",
            "eng_Latn": "English",
            "jpn_Jpan": "日本語",
            "kor_Hang": "한국어",
            "fra_Latn": "Français",
            "deu_Latn": "Deutsch",
            "spa_Latn": "Español",
            "rus_Cyrl": "Русский",
            "arb_Arab": "العربية",
            "hin_Deva": "हिन्दी",
            "por_Latn": "Português",
            "ita_Latn": "Italiano",
            "tha_Thai": "ไทย",
            "vie_Latn": "Tiếng Việt"
        }
    
    def is_available(self) -> bool:
        """检查翻译引擎是否可用"""
        return TRANSFORMERS_AVAILABLE
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "loaded": self.model is not None,
            "available": self.is_available()
        }


# 全局翻译引擎实例
_translator = None


def get_translator(model_name: str = "facebook/nllb-200-distilled-600M") -> Translator:
    """获取全局翻译引擎实例"""
    global _translator
    if _translator is None:
        _translator = Translator(model_name)
    return _translator
