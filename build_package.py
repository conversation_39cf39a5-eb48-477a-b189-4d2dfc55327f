#!/usr/bin/env python3
"""
跨平台打包脚本

支持Windows和macOS（包括M2-M4芯片）的自动化打包
严格遵守uv.lock中的依赖版本，支持外部配置文件和数据目录
"""

import os
import sys
import platform
import subprocess
import shutil
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import tempfile
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('build_package.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class PackageBuilder:
    """跨平台打包构建器"""
    
    def __init__(self, project_root: Optional[Path] = None):
        self.project_root = project_root or Path(__file__).parent
        self.platform_name = self._detect_platform()
        self.temp_dir = None
        
        logger.info(f"初始化打包构建器 - 平台: {self.platform_name}")
        logger.info(f"项目根目录: {self.project_root}")
    
    def _detect_platform(self) -> str:
        """检测当前平台"""
        system = platform.system().lower()
        if system == 'windows':
            return 'windows'
        elif system == 'darwin':
            return 'macos'
        elif system == 'linux':
            return 'linux'
        else:
            raise ValueError(f"不支持的平台: {system}")
    
    def validate_environment(self) -> bool:
        """验证构建环境"""
        logger.info("验证构建环境...")
        
        # 检查必要文件
        required_files = [
            'pyproject.toml',
            'uv.lock',
            'config.yaml',
            'src/main.py'
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                logger.error(f"缺少必要文件: {file_path}")
                return False
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 11):
            logger.error(f"Python版本过低: {python_version}, 需要 >= 3.11")
            return False
        
        # 检查uv是否可用
        try:
            result = subprocess.run(['uv', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"UV版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("UV未安装或不可用")
            return False
        
        logger.info("环境验证通过")
        return True
    
    def parse_uv_lock(self) -> Dict:
        """解析uv.lock文件获取依赖信息"""
        logger.info("解析uv.lock文件...")
        
        lock_file = self.project_root / 'uv.lock'
        
        # 这里简化处理，实际应该解析TOML格式
        # 由于uv.lock格式复杂，我们使用pyproject.toml作为依赖源
        pyproject_file = self.project_root / 'pyproject.toml'
        
        try:
            import tomllib
        except ImportError:
            try:
                import tomli as tomllib
            except ImportError:
                # 如果没有tomli，使用简单的文本解析
                logger.warning("未找到TOML解析库，使用简化解析")
                return self._parse_pyproject_simple(pyproject_file)
        
        with open(pyproject_file, 'rb') as f:
            pyproject_data = tomllib.load(f)
        
        dependencies = pyproject_data.get('project', {}).get('dependencies', [])
        logger.info(f"找到 {len(dependencies)} 个依赖")
        
        return {
            'dependencies': dependencies,
            'platform_sources': pyproject_data.get('tool', {}).get('uv', {}).get('sources', {})
        }

    def _parse_pyproject_simple(self, _: Path) -> Dict:
        """简化的pyproject.toml解析（当没有TOML库时）"""
        logger.info("使用简化解析模式...")

        # 从uv.lock中提取基本依赖信息
        lock_file = self.project_root / 'uv.lock'
        dependencies = []

        if lock_file.exists():
            with open(lock_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 简单提取包名和版本（这是一个简化的实现）
            import re
            pattern = r'name = "([^"]+)"\nversion = "([^"]+)"'
            matches = re.findall(pattern, content)

            for name, version in matches:
                if name not in ['python']:  # 排除Python本身
                    dependencies.append(f"{name}=={version}")

        return {
            'dependencies': dependencies,
            'platform_sources': {}
        }
    
    def create_requirements_file(self, dependencies: List[str]) -> Path:
        """创建临时requirements.txt文件"""
        logger.info("创建requirements.txt文件...")
        
        # 创建临时目录
        if not self.temp_dir:
            self.temp_dir = Path(tempfile.mkdtemp(prefix='package_build_'))
        
        requirements_file = self.temp_dir / 'requirements.txt'
        
        # 根据平台调整PyTorch依赖
        adjusted_deps = []
        pytorch_packages = ['torch==', 'torchvision==', 'torchaudio==']

        for dep in dependencies:
            is_pytorch = any(dep.startswith(pkg) for pkg in pytorch_packages)

            if is_pytorch:
                adjusted_deps.append(self._adjust_pytorch_dependency(dep))
            else:
                adjusted_deps.append(dep)
        
        # 添加PyInstaller
        adjusted_deps.append('pyinstaller>=6.0.0')
        
        with open(requirements_file, 'w', encoding='utf-8') as f:
            for dep in adjusted_deps:
                f.write(f"{dep}\n")
        
        logger.info(f"Requirements文件创建完成: {requirements_file}")
        return requirements_file

    def _adjust_pytorch_dependency(self, dep: str) -> str:
        """根据平台调整PyTorch依赖"""
        if self.platform_name == 'macos':
            return f"{dep} --index-url https://download.pytorch.org/whl/cpu"
        elif self.platform_name == 'windows':
            return f"{dep} --index-url https://download.pytorch.org/whl/cu128"
        else:
            return dep
    
    def install_dependencies(self, requirements_file: Path) -> bool:
        """安装打包依赖"""
        logger.info("安装打包依赖...")
        
        try:
            # 使用pip安装依赖（因为需要处理PyTorch的特殊索引）
            cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)]
            
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("依赖安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"依赖安装失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return False
    
    # 注意：现在使用直接命令行打包，不再需要spec文件生成函数

    def run_pyinstaller_direct(self) -> bool:
        """直接使用命令行参数运行PyInstaller（不使用spec文件）"""
        logger.info("开始PyInstaller打包...")

        try:
            main_py = str(self.project_root / "src" / "main.py")
            assets_path = str(self.project_root / "src" / "assets")

            cmd = [
                sys.executable, '-m', 'PyInstaller',
                main_py,
                '--name', 'ImageTextTranslator',
                '--onedir',  # 创建目录而不是单文件
                '--clean',
                '--noconfirm',  # 自动确认覆盖
                # 数据文件（跨平台路径分隔符）
                '--add-data', f'{assets_path}{";" if self.platform == "windows" else ":"}src/assets',
                # Paddle相关收集
                '--collect-all', 'paddlex',
                '--collect-all', 'paddleocr',
                '--collect-data', 'paddlex',
                '--collect-data', 'paddle',
                '--collect-data', 'paddleocr',
                # PDF和OpenCV相关收集
                '--collect-all', 'pypdfium2',
                '--collect-data', 'pypdfium2',
                '--collect-all', 'cv2',
                '--collect-data', 'cv2',
                # 收集包元数据（关键！）
                '--copy-metadata', 'paddlex',
                '--copy-metadata', 'paddleocr',
                '--copy-metadata', 'paddlepaddle',
                '--copy-metadata', 'pypdfium2',
                '--copy-metadata', 'opencv-contrib-python',
                '--hidden-import', 'paddlex',
                '--hidden-import', 'paddleocr._pipelines',
                '--hidden-import', 'paddleocr._pipelines.ocr',
                '--hidden-import', 'paddleocr._pipelines.base',
                '--hidden-import', 'pypdfium2',
                '--hidden-import', 'cv2',
                # 其他隐藏导入
                '--hidden-import', 'PyQt6.QtCore',
                '--hidden-import', 'PyQt6.QtGui',
                '--hidden-import', 'PyQt6.QtWidgets',
                '--hidden-import', 'paddleocr',
                '--hidden-import', 'paddle',
                '--hidden-import', 'torch',
                '--hidden-import', 'transformers',
            ]

            # 添加 PaddlePaddle 动态库收集（跨平台）
            logger.info("添加 PaddlePaddle 动态库收集...")
            try:
                import paddle
                paddle_path = paddle.__file__.replace('__init__.py', '')

                # 查找 MKL 相关的动态库文件
                import glob
                import os

                # 根据平台选择动态库扩展名和分隔符
                if self.platform == 'windows':
                    lib_ext = 'dll'
                    path_sep = ';'
                    lib_patterns = [
                        f'mklml*.{lib_ext}',
                        f'libiomp5md.{lib_ext}',
                        f'mkldnn.{lib_ext}',
                        f'paddle_inference.{lib_ext}',
                        f'common.{lib_ext}',
                    ]
                else:  # macOS
                    lib_ext = 'dylib'
                    path_sep = ':'
                    lib_patterns = [
                        f'libmklml*.{lib_ext}',
                        f'libiomp5.{lib_ext}',
                        f'libmkldnn*.{lib_ext}',
                        f'libpaddle_inference.{lib_ext}',
                        f'libcommon.{lib_ext}',
                    ]

                # 搜索动态库文件
                for lib_pattern in lib_patterns:
                    full_pattern = os.path.join(paddle_path, '**', lib_pattern)
                    lib_files = glob.glob(full_pattern, recursive=True)
                    for lib_file in lib_files:
                        cmd.extend(['--add-binary', f'{lib_file}{path_sep}.'])
                        logger.info(f"添加动态库: {os.path.basename(lib_file)}")

            except Exception as e:
                logger.warning(f"动态库收集失败: {e}")

            # 控制台设置
            if self.platform == 'macos':
                cmd.append('--noconsole')
            else:
                cmd.append('--console')

            logger.info(f"执行命令: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            logger.info("PyInstaller打包完成")
            logger.debug(f"输出: {result.stdout}")

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"PyInstaller打包失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return False

    def copy_external_files(self) -> bool:
        """创建完整的外部文件目录结构（基于config.yaml配置）"""
        logger.info("创建外部文件目录结构...")

        # 确定目标目录
        if self.platform_name == 'macos':
            dist_dir = self.project_root / 'dist'
            target_dir = dist_dir.parent  # 与.app同级
        else:
            dist_dir = self.project_root / 'dist' / 'ImageTextTranslator'
            target_dir = dist_dir.parent  # dist目录

        try:
            # 复制配置文件到可执行文件目录
            config_source = self.project_root / 'config.yaml'
            config_target = dist_dir / 'config.yaml'  # 注意：复制到exe同目录
            if config_source.exists():
                shutil.copy2(config_source, config_target)
                logger.info("复制配置文件到可执行文件目录: config.yaml")

            # 基于config.yaml创建完整的目录结构
            directories_to_create = [
                # 主要目录
                'models',
                'models/compression',
                'models/translate',
                'fonts',
                'cache',
                'cache/fonts',
                'cache/paddleocr',      # PaddleOCR缓存目录
                'cache/paddlex',        # PaddleX缓存目录
                'cache/test_outputs',
                'output',
                'temp'
            ]

            for dir_name in directories_to_create:
                target_path = dist_dir / dir_name  # 修改：在exe目录下创建
                target_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录结构: {dir_name}")

            logger.info("外部文件目录结构创建完成")
            logger.info("配置文件已复制到可执行文件目录，程序可独立运行")
            return True

        except Exception as e:
            logger.error(f"创建目录结构失败: {e}")
            return False

    def create_launcher_script(self) -> bool:
        """创建启动脚本（可选）"""
        logger.info("创建启动脚本...")

        try:
            if self.platform_name == 'windows':
                # Windows批处理脚本
                script_content = '''@echo off
cd /d "%~dp0"
start "" "ImageTextTranslator\\ImageTextTranslator.exe"
'''
                script_file = self.project_root / 'dist' / 'start.bat'

            elif self.platform_name == 'macos':
                # macOS shell脚本
                script_content = '''#!/bin/bash
cd "$(dirname "$0")"
open ImageTextTranslator.app
'''
                script_file = self.project_root / 'dist' / 'start.sh'
            else:
                return True  # Linux暂不创建启动脚本

            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script_content)

            # 设置执行权限（macOS/Linux）
            if self.platform_name != 'windows':
                os.chmod(script_file, 0o755)

            logger.info(f"启动脚本创建完成: {script_file}")
            return True

        except Exception as e:
            logger.error(f"创建启动脚本失败: {e}")
            return False

    def validate_package(self) -> bool:
        """验证打包结果"""
        logger.info("验证打包结果...")

        try:
            # 检查可执行文件
            if self.platform_name == 'windows':
                exe_path = self.project_root / 'dist' / 'ImageTextTranslator' / 'ImageTextTranslator.exe'
            elif self.platform_name == 'macos':
                exe_path = self.project_root / 'dist' / 'ImageTextTranslator.app'
            else:
                exe_path = self.project_root / 'dist' / 'ImageTextTranslator' / 'ImageTextTranslator'

            if not exe_path.exists():
                logger.error(f"可执行文件不存在: {exe_path}")
                return False

            # 检查外部文件
            base_dir = self.project_root / 'dist'

            required_external = ['config.yaml']
            for item in required_external:
                item_path = base_dir / item
                if not item_path.exists():
                    logger.warning(f"外部文件缺失: {item}")

            logger.info("打包结果验证完成")
            return True

        except Exception as e:
            logger.error(f"验证打包结果失败: {e}")
            return False

    def cleanup(self):
        """清理临时文件"""
        logger.info("清理临时文件...")

        try:
            # 清理临时目录
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info("临时目录清理完成")

            # 清理生成的spec文件
            spec_file = self.project_root / f'ImageTextTranslator_{self.platform_name}.spec'
            if spec_file.exists():
                spec_file.unlink()
                logger.info("Spec文件清理完成")

            # 清理build目录
            build_dir = self.project_root / 'build'
            if build_dir.exists():
                shutil.rmtree(build_dir)
                logger.info("Build目录清理完成")

        except Exception as e:
            logger.warning(f"清理过程中出现错误: {e}")

    def build(self, clean: bool = True, skip_deps: bool = False) -> bool:
        """执行完整的打包流程"""
        logger.info("开始打包流程...")

        try:
            # 1. 验证环境
            if not self.validate_environment():
                return False

            # 2. 解析依赖
            lock_data = self.parse_uv_lock()

            # 3. 安装依赖（如果需要）
            if not skip_deps:
                requirements_file = self.create_requirements_file(lock_data['dependencies'])
                if not self.install_dependencies(requirements_file):
                    return False

            # 4. 直接运行PyInstaller（不使用spec文件）
            if not self.run_pyinstaller_direct():
                return False

            # 6. 复制外部文件
            if not self.copy_external_files():
                return False

            # 7. 创建启动脚本
            self.create_launcher_script()

            # 8. 验证结果
            if not self.validate_package():
                return False

            logger.info("打包流程完成！")

            # 显示结果信息
            dist_dir = self.project_root / 'dist'
            logger.info(f"打包结果位于: {dist_dir}")

            return True

        except Exception as e:
            logger.error(f"打包流程失败: {e}")
            return False

        finally:
            if clean:
                self.cleanup()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Image Text Translator 跨平台打包工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python build_package.py                    # 自动检测平台并打包
  python build_package.py --platform windows # 指定Windows平台打包
  python build_package.py --platform macos   # 指定macOS平台打包
  python build_package.py --skip-deps        # 跳过依赖安装
  python build_package.py --no-clean         # 不清理临时文件
        '''
    )

    parser.add_argument(
        '--platform',
        choices=['windows', 'macos', 'linux'],
        help='指定目标平台（默认自动检测）'
    )

    parser.add_argument(
        '--skip-deps',
        action='store_true',
        help='跳过依赖安装（假设已安装）'
    )

    parser.add_argument(
        '--no-clean',
        action='store_true',
        help='不清理临时文件（用于调试）'
    )

    parser.add_argument(
        '--project-root',
        type=Path,
        help='指定项目根目录（默认为脚本所在目录）'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 创建构建器
        builder = PackageBuilder(args.project_root)

        # 如果指定了平台，覆盖自动检测的结果
        if args.platform:
            builder.platform_name = args.platform
            logger.info(f"使用指定平台: {args.platform}")

        # 执行打包
        success = builder.build(
            clean=not args.no_clean,
            skip_deps=args.skip_deps
        )

        if success:
            print("\n🎉 打包成功！")
            print(f"📦 打包结果位于: {builder.project_root / 'dist'}")
            print("\n📋 使用说明:")
            print("1. 将整个dist目录复制到目标机器")
            print("2. 确保config.yaml、models/、fonts/等目录与可执行文件在同一级别")
            print("3. 运行可执行文件或使用提供的启动脚本")

            if builder.platform_name == 'macos':
                print("\n🍎 macOS特别说明:")
                print("- 首次运行可能需要在系统偏好设置中允许运行")
                print("- 可以使用 'open ImageTextTranslator.app' 命令启动")

            return 0
        else:
            print("\n❌ 打包失败！请查看日志了解详细错误信息。")
            return 1

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断打包过程")
        return 1
    except Exception as e:
        logger.error(f"打包过程中发生未预期的错误: {e}")
        print(f"\n💥 发生错误: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
