import os
import json
from typing import List, Dict, Tuple, Optional
from paddleocr import PaddleOCR

from .device_manager import get_device_manager
from .language_mapping import PADDLEOCR_LANGUAGES


class OCRResult:
    """OCR识别结果类"""
    
    def __init__(self, texts: List[str], boxes: List[List[List[int]]]):
        self.texts = texts  # 识别出的文本内容列表
        self.boxes = boxes  # 文本框顶点坐标列表
    
    def __str__(self):
        return f"OCRResult(texts={len(self.texts)} items, boxes={len(self.boxes)} items)"
    
    def __repr__(self):
        return self.__str__()
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'texts': self.texts,
            'boxes': self.boxes
        }


class PaddleOCRTool:
    """PaddleOCR工具类，提供便捷的OCR识别功能"""
    
    def __init__(self,
                 lang: str = "unknown",
                 ocr_version: str = "PP-OCRv5",
                 device: str = "cpu",
                 use_doc_orientation_classify: bool = False,
                 use_doc_unwarping: bool = False,
                 use_textline_orientation: bool = False):
        """
        初始化PaddleOCR工具

        Args:
            lang: 语言代码，必须是PaddleOCR支持的标准语言代码，如 'ch', 'en', 'korean', 'japan' 等
            ocr_version: OCR版本，如 'PP-OCRv5'
            device: 设备类型，统一使用 'cpu' 以确保跨平台兼容性
            use_doc_orientation_classify: 是否使用文档方向分类模型
            use_doc_unwarping: 是否使用文本图像矫正模型
            use_textline_orientation: 是否使用文本行方向分类模型

        Raises:
            ValueError: 当语言代码不受支持时
        """
        # 验证语言代码
        if lang == "unknown":
            raise ValueError("必须指定有效的语言代码，不能使用 'unknown'")

        if lang not in PADDLEOCR_LANGUAGES:
            supported_langs = list(PADDLEOCR_LANGUAGES.keys())
            raise ValueError(f"不支持的语言代码: {lang}。支持的语言代码: {supported_langs}")
        self.lang = lang
        self.ocr_version = ocr_version

        # PaddleOCR统一使用CPU以确保跨平台兼容性
        # 因为PaddleOCR在Mac上不支持GPU加速
        self.device = "cpu"
        
        # 初始化OCR模型
        self.ocr = PaddleOCR(
            lang=lang,
            ocr_version=ocr_version,
            device=device,
            use_doc_orientation_classify=use_doc_orientation_classify,
            use_doc_unwarping=use_doc_unwarping,
            use_textline_orientation=use_textline_orientation
        )
    
    def recognize(self, image_path: str, save_result: bool = True, output_dir: str = "output") -> OCRResult:
        """
        识别图像中的文字
        
        Args:
            image_path: 图像文件路径
            save_result: 是否保存结果到文件
            output_dir: 输出目录
            
        Returns:
            OCRResult: 包含识别文本和位置信息的结果对象
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        
        # 执行OCR识别
        result = self.ocr.predict(image_path)
        
        # 如果需要保存结果
        if save_result:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存图像和JSON结果
            for res in result:
                res.save_to_img(output_dir)
                res.save_to_json(output_dir)
        
        # 提取识别结果
        texts = []
        boxes = []
        
        for res in result:
            # res是字典格式，直接通过键访问
            if 'rec_texts' in res:
                texts.extend(res['rec_texts'])
            
            # 从结果中提取位置信息（顶点坐标）
            if 'rec_polys' in res:
                # rec_polys中的每个元素是numpy数组，需要转换为列表
                polys = res['rec_polys']
                for poly in polys:
                    # 将numpy数组转换为列表格式
                    box = poly.tolist()
                    boxes.append(box)
        
        return OCRResult(texts, boxes)
    
    def recognize_batch(self, image_paths: List[str], save_result: bool = True, output_dir: str = "output") -> Dict[str, OCRResult]:
        """
        批量识别多个图像
        
        Args:
            image_paths: 图像文件路径列表
            save_result: 是否保存结果到文件
            output_dir: 输出目录
            
        Returns:
            Dict[str, OCRResult]: 以图像路径为key，OCRResult为value的字典
        """
        results = {}
        
        for image_path in image_paths:
            try:
                result = self.recognize(image_path, save_result, output_dir)
                results[image_path] = result
            except Exception as e:
                print(f"识别失败 {image_path}: {e}")
                results[image_path] = None
        
        return results
    
    def get_text_only(self, image_path: str) -> List[str]:
        """
        只获取识别的文本内容，不包含位置信息
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            List[str]: 识别出的文本内容列表
        """
        result = self.recognize(image_path, save_result=False)
        return result.texts
    
    def get_text_with_boxes(self, image_path: str) -> List[Tuple[str, List[List[int]]]]:
        """
        获取文本内容和对应的位置信息
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            List[Tuple[str, List[List[int]]]]: 包含文本和位置信息的元组列表
        """
        result = self.recognize(image_path, save_result=False)
        return list(zip(result.texts, result.boxes))


# 使用示例
if __name__ == "__main__":
    # 创建OCR工具实例
    ocr_tool = PaddleOCRTool(
        lang="korean",  # 英文识别
        device="cpu"
    )
    
    # 识别单个图像
    try:
        result = ocr_tool.recognize("./korean_1_res.jpg")
        print(f"识别出 {len(result.texts)} 个文本:")
        
        for i, (text, box) in enumerate(zip(result.texts, result.boxes)):
            print(f"{i+1}. 文本: '{text}'")
            print(f"   位置: {box}")
            print()
        
        # 只获取文本内容
        texts = ocr_tool.get_text_only("./korean_1_res.jpg")
        print("所有文本内容:", texts)
        
        # 获取文本和位置信息
        text_with_boxes = ocr_tool.get_text_with_boxes("./korean_1_res.jpg")
        print("文本和位置信息:", text_with_boxes)
        
    except Exception as e:
        print(f"识别失败: {e}")