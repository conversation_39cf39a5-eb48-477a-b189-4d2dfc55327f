"""
Google Fonts API 字体下载器

使用Google Fonts API下载Noto字体，集成到字体管理系统中
"""

import os
import requests
import json
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List, Tuple
from dataclasses import dataclass
import time
import threading
from concurrent.futures import ThreadPoolExecutor, Future

from ...utils.logger import get_logger
from ...utils.config import get_config
from .noto_font_registry import NotoFontInfo

@dataclass
class DownloadProgress:
    """下载进度信息"""
    font_name: str
    total_size: int
    downloaded_size: int
    speed: float  # bytes/second
    eta: float    # seconds
    status: str   # 'downloading', 'completed', 'failed', 'paused'

class GoogleFontsDownloader:
    """Google Fonts API 字体下载器"""
    
    def __init__(self, cache_dir: Optional[Path] = None, max_concurrent_downloads: int = 3):
        self.logger = get_logger()
        self.config = get_config()
        
        # 配置参数
        google_fonts_config = self.config.get_google_fonts_config()
        self.api_key = google_fonts_config.get('api_key')
        self.api_base_url = google_fonts_config.get('api_base_url', 'https://www.googleapis.com/webfonts/v1/webfonts')
        self.download_timeout = google_fonts_config.get('download_timeout', 120)

        # 验证API密钥
        if not self.api_key:
            self.logger.warning("Google Fonts API密钥未配置，请在config.yaml中设置google_fonts.api_key")
            raise ValueError("Google Fonts API密钥未配置")

        self.logger.info(f"Google Fonts API密钥已配置: {self.api_key[:10]}...")
        
        # 缓存目录
        if cache_dir is None:
            cache_dir = Path(google_fonts_config.get('cache_dir', 'cache/fonts'))
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 下载管理
        self.max_concurrent_downloads = max_concurrent_downloads
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_downloads)
        
        # 下载状态管理
        self._active_downloads: Dict[str, Future] = {}
        self._download_progress: Dict[str, DownloadProgress] = {}
        self._download_callbacks: Dict[str, Callable[[DownloadProgress], None]] = {}
        
        # 语言到Google Fonts字体名称的映射（与字体注册表一致）
        self.language_to_font = {
            "eng_Latn": "Noto Sans",
            "zho_Hans": "Noto Sans SC",
            "zho_Hant": "Noto Sans TC",
            "jpn_Jpan": "Noto Sans JP",
            "kor_Hang": "Noto Sans KR",
            "ara_Arab": "Noto Sans Arabic",
            "hin_Deva": "Noto Sans Devanagari",
            "ben_Beng": "Noto Sans Bengali",
            "tam_Taml": "Noto Sans Tamil",
            "tha_Thai": "Noto Sans Thai",
            "heb_Hebr": "Noto Sans Hebrew",
            "rus_Cyrl": "Noto Sans",  # 基础Noto Sans支持西里尔文
            "ell_Grek": "Noto Sans",  # 基础Noto Sans支持希腊文
        }
        
        self.logger.info("Google Fonts API 字体下载器初始化完成")

    def validate_api_key(self, api_key: str) -> Tuple[bool, str]:
        """验证Google Fonts API密钥是否有效

        Args:
            api_key: 要验证的API密钥

        Returns:
            (是否有效, 错误信息或成功信息)
        """
        try:
            # 使用一个简单的API请求来测试密钥
            params = {
                'family': 'Noto Sans',  # 使用常见字体进行测试
                'key': api_key
            }

            self.logger.debug(f"验证Google Fonts API密钥: {api_key[:10]}...")

            response = requests.get(self.api_base_url, params=params, timeout=10)
            response.raise_for_status()

            # 检查响应格式
            data = response.json()
            if 'items' in data and len(data['items']) > 0:
                self.logger.info("Google Fonts API密钥验证成功")
                return True, "API密钥验证成功"
            else:
                return False, "API响应格式异常，请检查密钥"

        except requests.exceptions.HTTPError as e:
            if hasattr(e, 'response') and e.response is not None:
                if e.response.status_code == 403:
                    return False, "API密钥无效或权限不足"
                elif e.response.status_code == 429:
                    return False, "API请求频率过高，请稍后重试"
                else:
                    return False, f"API请求失败: HTTP {e.response.status_code}"
            else:
                return False, f"HTTP错误: {str(e)}"
        except requests.exceptions.Timeout:
            return False, "API请求超时，请检查网络连接"
        except requests.exceptions.ConnectionError:
            return False, "无法连接到Google Fonts API，请检查网络连接"
        except json.JSONDecodeError:
            return False, "API响应格式错误"
        except Exception as e:
            return False, f"验证失败: {str(e)}"

    @classmethod
    def test_api_key(cls, api_key: str) -> Tuple[bool, str]:
        """静态方法：测试API密钥（无需实例化）

        Args:
            api_key: 要测试的API密钥

        Returns:
            (是否有效, 错误信息或成功信息)
        """
        try:
            # 临时创建一个最小配置的实例来测试API
            temp_instance = cls.__new__(cls)
            temp_instance.api_key = api_key
            temp_instance.api_base_url = 'https://www.googleapis.com/webfonts/v1/webfonts'
            temp_instance.logger = get_logger()

            return temp_instance.validate_api_key(api_key)
        except Exception as e:
            return False, f"测试失败: {str(e)}"

    def get_font_download_url(self, font_family: str) -> Optional[str]:
        """从Google Fonts API获取字体下载URL"""
        try:
            # 构建API请求参数
            params = {
                'family': font_family,
                'sort': 'popularity'
            }

            if self.api_key:
                params['key'] = self.api_key

            self.logger.debug(f"查询Google Fonts API: {font_family}")

            # 请求API
            try:
                response = requests.get(self.api_base_url, params=params, timeout=30)
                response.raise_for_status()
            except requests.exceptions.Timeout:
                self.logger.error("Google Fonts API请求超时")
                return None
            except requests.exceptions.ConnectionError:
                self.logger.error("无法连接到Google Fonts API，请检查网络连接")
                return None
            except requests.exceptions.HTTPError as e:
                if response.status_code == 403:
                    self.logger.error("Google Fonts API访问被拒绝，请检查API密钥")
                elif response.status_code == 429:
                    self.logger.error("Google Fonts API请求频率过高，请稍后重试")
                else:
                    self.logger.error(f"Google Fonts API HTTP错误: {e}")
                return None
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Google Fonts API请求失败: {e}")
                return None

            # 解析响应
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                self.logger.error(f"Google Fonts API响应格式错误: {e}")
                return None

            # 检查是否找到字体
            if 'items' not in data or len(data['items']) == 0:
                self.logger.warning(f"API中未找到字体: {font_family}")
                return None

            # 获取第一个匹配的字体
            font_info = data['items'][0]

            # 检查是否有regular样式的文件
            if 'files' not in font_info or 'regular' not in font_info['files']:
                self.logger.warning(f"字体没有regular样式: {font_family}")
                return None

            download_url = font_info['files']['regular']
            version = font_info.get('version', 'unknown')

            self.logger.debug(f"找到下载URL: {font_family} (版本: {version})")
            return download_url

        except Exception as e:
            self.logger.error(f"获取字体下载URL时发生未知错误: {e}")
            return None
    
    def validate_font_file(self, file_path: Path, min_size_kb: int = 10) -> bool:
        """验证字体文件是否有效"""
        try:
            if not file_path.exists():
                return False
            
            file_size = file_path.stat().st_size
            
            # 检查文件大小
            if file_size < min_size_kb * 1024:
                self.logger.debug(f"文件太小: {file_size / 1024:.1f}KB < {min_size_kb}KB")
                return False
            
            # 检查文件头，确保是TTF文件
            with open(file_path, 'rb') as f:
                header = f.read(4)
                
            # TTF文件的魔数
            ttf_signatures = [
                b'\x00\x01\x00\x00',  # TTF
                b'OTTO',              # OTF
                b'true',              # TTF (Mac)
                b'typ1'               # Type1
            ]
            
            if header in ttf_signatures:
                self.logger.debug(f"字体文件验证通过: {file_size / 1024:.1f}KB")
                return True
            else:
                self.logger.warning(f"不是有效的字体文件，文件头: {header}")
                return False
                
        except Exception as e:
            self.logger.error(f"文件验证失败: {e}")
            return False
    
    def download_font_file(self, download_url: str, font_name: str, 
                          progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Tuple[bool, str, Optional[Path]]:
        """下载字体文件"""
        try:
            # 生成文件名
            filename = f"{font_name.replace(' ', '')}-Regular.ttf"
            file_path = self.cache_dir / filename
            
            # 检查文件是否已存在且有效
            if file_path.exists() and self.validate_font_file(file_path):
                return True, f"字体已存在: {file_path}", file_path
            
            self.logger.info(f"开始下载: {font_name}")
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 下载文件
            response = requests.get(download_url, headers=headers, stream=True, timeout=self.download_timeout)
            response.raise_for_status()
            
            # 检查Content-Type，确保不是HTML页面
            content_type = response.headers.get('content-type', '').lower()
            if 'html' in content_type or 'text' in content_type:
                return False, f"下载的是HTML页面，不是字体文件 (Content-Type: {content_type})", None
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            start_time = time.time()
            
            # 初始化进度信息
            progress = DownloadProgress(
                font_name=font_name,
                total_size=total_size,
                downloaded_size=0,
                speed=0.0,
                eta=0.0,
                status='downloading'
            )
            
            # 写入文件
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 更新进度
                        elapsed_time = time.time() - start_time
                        if elapsed_time > 0:
                            progress.downloaded_size = downloaded_size
                            progress.speed = downloaded_size / elapsed_time
                            if progress.speed > 0 and total_size > 0:
                                remaining_bytes = total_size - downloaded_size
                                progress.eta = remaining_bytes / progress.speed
                            
                            # 调用进度回调
                            if progress_callback:
                                progress_callback(progress)
            
            # 验证下载的文件
            if self.validate_font_file(file_path):
                progress.status = 'completed'
                if progress_callback:
                    progress_callback(progress)
                return True, f"下载成功: {file_path}", file_path
            else:
                # 删除无效文件
                if file_path.exists():
                    file_path.unlink()
                return False, "下载的文件无效", None
                
        except requests.exceptions.RequestException as e:
            return False, f"下载失败: {str(e)}", None
        except Exception as e:
            return False, f"下载异常: {str(e)}", None
    
    def download_font_for_language(self, language_code: str,
                                  progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Tuple[bool, str, Optional[Path]]:
        """为指定语言下载字体"""
        if language_code not in self.language_to_font:
            return False, f"不支持的语言代码: {language_code}", None
        
        font_name = self.language_to_font[language_code]
        
        # 1. 从API获取下载URL
        download_url = self.get_font_download_url(font_name)
        if not download_url:
            return False, f"无法获取 {font_name} 的下载URL", None
        
        # 2. 下载字体文件
        return self.download_font_file(download_url, font_name, progress_callback)
    
    def download_font_async(self, font_info: NotoFontInfo, 
                           progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Future:
        """异步下载字体（兼容现有接口）"""
        if font_info.name in self._active_downloads:
            self.logger.warning(f"字体 {font_info.name} 正在下载中")
            return self._active_downloads[font_info.name]
        
        if progress_callback:
            self._download_callbacks[font_info.name] = progress_callback
        
        # 根据字体名称确定语言代码
        language_code = None
        for lang, font_name in self.language_to_font.items():
            if font_name == font_info.name:
                language_code = lang
                break
        
        if not language_code:
            # 如果找不到对应的语言代码，使用默认的下载方式
            future = self.executor.submit(self._download_font_legacy, font_info, progress_callback)
        else:
            future = self.executor.submit(self._download_font_with_api, language_code, progress_callback)
        
        self._active_downloads[font_info.name] = future
        
        # 下载完成后清理
        def cleanup(fut):
            self._active_downloads.pop(font_info.name, None)
            self._download_progress.pop(font_info.name, None)
            self._download_callbacks.pop(font_info.name, None)
        
        future.add_done_callback(cleanup)
        return future
    
    def _download_font_with_api(self, language_code: str, 
                               progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Optional[Path]:
        """使用API下载字体"""
        success, message, font_path = self.download_font_for_language(language_code, progress_callback)
        if success:
            return font_path
        else:
            self.logger.error(f"API下载失败: {message}")
            return None
    
    def _download_font_legacy(self, font_info: NotoFontInfo,
                             progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Optional[Path]:
        """使用传统方式下载字体（回退方案）"""
        # 这里可以实现传统的下载方式作为回退
        self.logger.warning(f"使用传统方式下载字体: {font_info.name}")
        return None
    
    def is_downloading(self, font_name: str) -> bool:
        """检查是否正在下载"""
        return font_name in self._active_downloads
    
    def cancel_download(self, font_name: str) -> bool:
        """取消下载"""
        future = self._active_downloads.get(font_name)
        if future:
            return future.cancel()
        return False
    
    def cleanup(self):
        """清理资源"""
        # 取消所有活动下载
        for future in self._active_downloads.values():
            future.cancel()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("Google Fonts字体下载器已清理")

# 全局Google Fonts下载器实例
_google_fonts_downloader = None

def get_google_fonts_downloader(cache_dir: Optional[Path] = None) -> GoogleFontsDownloader:
    """获取全局Google Fonts下载器实例"""
    global _google_fonts_downloader
    if _google_fonts_downloader is None:
        _google_fonts_downloader = GoogleFontsDownloader(cache_dir)
    return _google_fonts_downloader
