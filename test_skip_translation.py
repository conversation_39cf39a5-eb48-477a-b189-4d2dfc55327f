#!/usr/bin/env python3
"""
测试跳过本地翻译功能的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.utils.config import init_config, get_config
from src.utils.logger import get_logger
from src.core.translation_pipeline import TranslationPipeline, TranslationPipelineConfig
from src.core.text_region import TextRegion


def test_skip_translation_feature():
    """测试跳过本地翻译功能"""
    
    # 初始化配置
    init_config()
    logger = get_logger()
    config = get_config()
    
    print("=" * 60)
    print("🧪 测试跳过本地翻译功能")
    print("=" * 60)
    
    # 创建测试文本区域
    test_regions = [
        TextRegion(
            text="Hello World",
            bbox=[100, 100, 200, 120],
            confidence=0.95
        ),
        TextRegion(
            text="Good Morning",
            bbox=[100, 130, 220, 150],
            confidence=0.92
        )
    ]
    
    # 测试1: 检查配置是否正确加载
    print("\n📋 测试1: 检查配置")
    skip_local = config.get('translation.skip_local_translation', False)
    print(f"   skip_local_translation 配置值: {skip_local}")
    
    # 测试2: 测试默认模式（不跳过本地翻译）
    print("\n🔄 测试2: 默认模式（本地翻译 + LLM优化）")
    try:
        pipeline_config = TranslationPipelineConfig(
            source_language='eng_Latn',
            target_language='zho_Hans',
            compression_enabled=True,
            skip_local_translation=False  # 明确设置为False
        )
        
        pipeline = TranslationPipeline(config=pipeline_config)
        print(f"   翻译管道创建成功")
        print(f"   skip_local_translation: {getattr(pipeline.config, 'skip_local_translation', '未设置')}")
        
    except Exception as e:
        print(f"   ❌ 默认模式测试失败: {e}")
    
    # 测试3: 测试跳过本地翻译模式
    print("\n🚀 测试3: 跳过本地翻译模式（直接LLM翻译）")
    try:
        pipeline_config = TranslationPipelineConfig(
            source_language='eng_Latn',
            target_language='zho_Hans',
            compression_enabled=True,
            skip_local_translation=True  # 启用跳过本地翻译
        )
        
        pipeline = TranslationPipeline(config=pipeline_config)
        print(f"   翻译管道创建成功")
        print(f"   skip_local_translation: {getattr(pipeline.config, 'skip_local_translation', '未设置')}")
        
        # 测试批量翻译逻辑
        texts = [region.text for region in test_regions]
        print(f"   测试文本: {texts}")
        
        # 注意：这里只测试逻辑，不实际调用API
        print("   ✅ 跳过本地翻译模式配置正确")
        
    except Exception as e:
        print(f"   ❌ 跳过本地翻译模式测试失败: {e}")
    
    # 测试4: 检查LLM提示词
    print("\n📝 测试4: 检查LLM提示词")
    llm_prompt = config.get('llm_prompt', {})
    system_prompt = llm_prompt.get('system_prompt', '')
    
    if '模式1' in system_prompt and '模式2' in system_prompt:
        print("   ✅ LLM提示词已更新，支持双模式")
        print("   📋 系统提示词包含:")
        print("      - 模式1: 翻译优化模式")
        print("      - 模式2: 直接翻译模式")
    else:
        print("   ❌ LLM提示词未正确更新")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)
    
    # 输出使用说明
    print("\n📖 使用说明:")
    print("1. 在config.yaml中设置 translation.skip_local_translation: true")
    print("2. 确保models.compression.type设置为'api'")
    print("3. 配置正确的OpenAI API密钥")
    print("4. 运行图片翻译，系统将跳过本地NLLB翻译，直接使用OpenAI进行翻译")


if __name__ == "__main__":
    test_skip_translation_feature()
