"""
语言到文字系统映射器

将NLLB-200语言代码映射到对应的文字系统和Noto字体
"""

from typing import Dict, List, Optional, Set
from .noto_font_registry import ScriptType, get_font_registry, NotoFontInfo
import re

class LanguageScriptMapper:
    """语言到文字系统映射器"""
    
    def __init__(self):
        self.font_registry = get_font_registry()
        self._language_script_map = self._build_language_script_map()
        self._script_detection_patterns = self._build_script_detection_patterns()
    
    def _build_language_script_map(self) -> Dict[str, ScriptType]:
        """构建语言代码到文字系统的映射表"""
        return {
            # 拉丁文字系统
            "eng_Latn": ScriptType.LATIN, "fra_Latn": ScriptType.LATIN, "deu_Latn": ScriptType.LATIN,
            "spa_Latn": ScriptType.LATIN, "ita_Latn": ScriptType.LATIN, "por_Latn": ScriptType.LATIN,
            "nld_Latn": ScriptType.LATIN, "pol_Latn": ScriptType.LATIN, "ces_Latn": ScriptType.LATIN,
            "hun_Latn": ScriptType.LATIN, "ron_Latn": ScriptType.LATIN, "hrv_Latn": ScriptType.LATIN,
            "slv_Latn": ScriptType.LATIN, "est_Latn": ScriptType.LATIN, "lav_Latn": ScriptType.LATIN,
            "lit_Latn": ScriptType.LATIN, "fin_Latn": ScriptType.LATIN, "swe_Latn": ScriptType.LATIN,
            "nor_Latn": ScriptType.LATIN, "dan_Latn": ScriptType.LATIN, "isl_Latn": ScriptType.LATIN,
            "tur_Latn": ScriptType.LATIN, "ind_Latn": ScriptType.LATIN, "msa_Latn": ScriptType.LATIN,
            "vie_Latn": ScriptType.LATIN, "fil_Latn": ScriptType.LATIN, "swh_Latn": ScriptType.LATIN,
            "afr_Latn": ScriptType.LATIN, "cat_Latn": ScriptType.LATIN, "eus_Latn": ScriptType.LATIN,
            "glg_Latn": ScriptType.LATIN, "ast_Latn": ScriptType.LATIN, "oci_Latn": ScriptType.LATIN,
            
            # 中日韩文字系统
            "zho_Hans": ScriptType.CJK, "zho_Hant": ScriptType.CJK,
            "jpn_Jpan": ScriptType.CJK, "kor_Hang": ScriptType.CJK,
            
            # 阿拉伯文字系统
            "ara_Arab": ScriptType.ARABIC, "arb_Arab": ScriptType.ARABIC, "arz_Arab": ScriptType.ARABIC,
            "apc_Arab": ScriptType.ARABIC, "acm_Arab": ScriptType.ARABIC, "ajp_Arab": ScriptType.ARABIC,
            "aeb_Arab": ScriptType.ARABIC, "ary_Arab": ScriptType.ARABIC, "acq_Arab": ScriptType.ARABIC,
            "fas_Arab": ScriptType.ARABIC, "urd_Arab": ScriptType.ARABIC, "pus_Arab": ScriptType.ARABIC,
            "ckb_Arab": ScriptType.ARABIC, "kmr_Arab": ScriptType.ARABIC, "azb_Arab": ScriptType.ARABIC,
            
            # 天城文字系统
            "hin_Deva": ScriptType.DEVANAGARI, "nep_Deva": ScriptType.DEVANAGARI, "mar_Deva": ScriptType.DEVANAGARI,
            "mai_Deva": ScriptType.DEVANAGARI, "bho_Deva": ScriptType.DEVANAGARI, "mag_Deva": ScriptType.DEVANAGARI,
            "sck_Deva": ScriptType.DEVANAGARI, "new_Deva": ScriptType.DEVANAGARI, "gom_Deva": ScriptType.DEVANAGARI,
            
            # 西里尔文字系统
            "rus_Cyrl": ScriptType.CYRILLIC, "ukr_Cyrl": ScriptType.CYRILLIC, "bel_Cyrl": ScriptType.CYRILLIC,
            "bul_Cyrl": ScriptType.CYRILLIC, "mkd_Cyrl": ScriptType.CYRILLIC, "srp_Cyrl": ScriptType.CYRILLIC,
            "mon_Cyrl": ScriptType.CYRILLIC, "kaz_Cyrl": ScriptType.CYRILLIC, "kir_Cyrl": ScriptType.CYRILLIC,
            "tgk_Cyrl": ScriptType.CYRILLIC, "uzn_Cyrl": ScriptType.CYRILLIC, "aze_Cyrl": ScriptType.CYRILLIC,
            
            # 泰文字系统
            "tha_Thai": ScriptType.THAI,
            
            # 希伯来文字系统
            "heb_Hebr": ScriptType.HEBREW,
            
            # 孟加拉文字系统
            "ben_Beng": ScriptType.BENGALI, "asm_Beng": ScriptType.BENGALI,
            
            # 泰米尔文字系统
            "tam_Taml": ScriptType.TAMIL,

            # 希腊文字系统
            "ell_Grek": ScriptType.GREEK,
            
            # 古吉拉特文字系统
            "guj_Gujr": ScriptType.GUJARATI,
            
            # 卡纳达文字系统
            "kan_Knda": ScriptType.KANNADA,
            
            # 马拉雅拉姆文字系统
            "mal_Mlym": ScriptType.MALAYALAM,
            
            # 奥里亚文字系统
            "ori_Orya": ScriptType.ORIYA,
            
            # 旁遮普文字系统
            "pan_Guru": ScriptType.PUNJABI,
            
            # 泰卢固文字系统
            "tel_Telu": ScriptType.TELUGU,
            
            # 僧伽罗文字系统
            "sin_Sinh": ScriptType.SINHALA,
            
            # 缅甸文字系统
            "mya_Mymr": ScriptType.MYANMAR,
            
            # 高棉文字系统
            "khm_Khmr": ScriptType.KHMER,
            
            # 老挝文字系统
            "lao_Laoo": ScriptType.LAO,
            
            # 藏文字系统
            "bod_Tibt": ScriptType.TIBETAN,
            
            # 格鲁吉亚文字系统
            "kat_Geor": ScriptType.GEORGIAN,
            
            # 亚美尼亚文字系统
            "hye_Armn": ScriptType.ARMENIAN,
            
            # 埃塞俄比亚文字系统
            "amh_Ethi": ScriptType.ETHIOPIC,
        }
    
    def _build_script_detection_patterns(self) -> Dict[ScriptType, str]:
        """构建文字系统检测的正则表达式模式"""
        return {
            ScriptType.LATIN: r'[A-Za-z]',
            ScriptType.CJK: r'[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]',
            ScriptType.ARABIC: r'[\u0600-\u06ff\u0750-\u077f\u08a0-\u08ff\ufb50-\ufdff\ufe70-\ufeff]',
            ScriptType.DEVANAGARI: r'[\u0900-\u097f]',
            ScriptType.CYRILLIC: r'[\u0400-\u04ff\u0500-\u052f\u2de0-\u2dff\ua640-\ua69f]',
            ScriptType.THAI: r'[\u0e00-\u0e7f]',
            ScriptType.HEBREW: r'[\u0590-\u05ff\ufb1d-\ufb4f]',
            ScriptType.BENGALI: r'[\u0980-\u09ff]',
            ScriptType.TAMIL: r'[\u0b80-\u0bff]',
            ScriptType.GUJARATI: r'[\u0a80-\u0aff]',
            ScriptType.KANNADA: r'[\u0c80-\u0cff]',
            ScriptType.MALAYALAM: r'[\u0d00-\u0d7f]',
            ScriptType.ORIYA: r'[\u0b00-\u0b7f]',
            ScriptType.PUNJABI: r'[\u0a00-\u0a7f]',
            ScriptType.TELUGU: r'[\u0c00-\u0c7f]',
            ScriptType.SINHALA: r'[\u0d80-\u0dff]',
            ScriptType.MYANMAR: r'[\u1000-\u109f\uaa60-\uaa7f]',
            ScriptType.KHMER: r'[\u1780-\u17ff\u19e0-\u19ff]',
            ScriptType.LAO: r'[\u0e80-\u0eff]',
            ScriptType.TIBETAN: r'[\u0f00-\u0fff]',
            ScriptType.GEORGIAN: r'[\u10a0-\u10ff\u2d00-\u2d2f]',
            ScriptType.ARMENIAN: r'[\u0530-\u058f\ufb13-\ufb17]',
            ScriptType.ETHIOPIC: r'[\u1200-\u137f\u1380-\u139f\u2d80-\u2ddf]',
            ScriptType.GREEK: r'[\u0370-\u03ff\u1f00-\u1fff]',
        }
    
    def get_script_for_language(self, language_code: str) -> Optional[ScriptType]:
        """获取语言对应的文字系统"""
        return self._language_script_map.get(language_code)
    
    def detect_script_from_text(self, text: str) -> List[ScriptType]:
        """从文本内容检测文字系统"""
        detected_scripts = []
        
        for script, pattern in self._script_detection_patterns.items():
            if re.search(pattern, text):
                detected_scripts.append(script)
        
        return detected_scripts
    
    def get_fonts_for_language(self, language_code: str) -> List[NotoFontInfo]:
        """获取支持指定语言的Noto字体"""
        return self.font_registry.get_fonts_for_language(language_code)
    
    def get_fonts_for_script(self, script: ScriptType) -> List[NotoFontInfo]:
        """获取支持指定文字系统的Noto字体"""
        return self.font_registry.get_fonts_for_script(script)
    
    def get_best_font_for_language(self, language_code: str) -> Optional[NotoFontInfo]:
        """获取指定语言的最佳字体"""
        fonts = self.get_fonts_for_language(language_code)
        if fonts:
            # 优先选择文件大小适中的字体
            return min(fonts, key=lambda f: f.file_size)
        
        # 如果没有直接支持的字体，尝试通过文字系统查找
        script = self.get_script_for_language(language_code)
        if script:
            fonts = self.get_fonts_for_script(script)
            if fonts:
                return min(fonts, key=lambda f: f.file_size)
        
        return None
    
    def get_fallback_fonts_for_language(self, language_code: str) -> List[NotoFontInfo]:
        """获取指定语言的回退字体链"""
        fallback_fonts = []
        
        # 1. 直接支持的字体
        direct_fonts = self.get_fonts_for_language(language_code)
        fallback_fonts.extend(direct_fonts)
        
        # 2. 同文字系统的其他字体
        script = self.get_script_for_language(language_code)
        if script:
            script_fonts = self.get_fonts_for_script(script)
            for font in script_fonts:
                if font not in fallback_fonts:
                    fallback_fonts.append(font)
        
        # 3. 通用回退字体
        universal_scripts = [ScriptType.LATIN, ScriptType.CJK]
        for universal_script in universal_scripts:
            universal_fonts = self.get_fonts_for_script(universal_script)
            for font in universal_fonts:
                if font not in fallback_fonts:
                    fallback_fonts.append(font)
        
        return fallback_fonts
    
    def is_language_supported(self, language_code: str) -> bool:
        """检查是否支持指定语言"""
        return self.font_registry.is_language_supported(language_code)
    
    def get_supported_languages(self) -> Set[str]:
        """获取所有支持的语言代码"""
        return self.font_registry.get_supported_languages()

# 全局语言映射器实例
_language_mapper = None

def get_language_script_mapper() -> LanguageScriptMapper:
    """获取全局语言映射器实例"""
    global _language_mapper
    if _language_mapper is None:
        _language_mapper = LanguageScriptMapper()
    return _language_mapper
