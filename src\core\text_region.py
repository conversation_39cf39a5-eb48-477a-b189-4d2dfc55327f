#!/usr/bin/env python3
"""
文本区域数据类 - 核心数据结构定义
"""

from typing import List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class TextRegion:
    """文本区域数据类"""
    text: str                    # 识别的文本内容
    confidence: float            # 置信度
    bbox: List[List[int]]       # 边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
    language: Optional[str] = None  # 检测到的语言
    
    @property
    def center(self) -> Tuple[int, int]:
        """获取文本区域中心点"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        return center_x, center_y
    
    @property
    def width(self) -> int:
        """获取文本区域宽度"""
        x_coords = [point[0] for point in self.bbox]
        return max(x_coords) - min(x_coords)
    
    @property
    def height(self) -> int:
        """获取文本区域高度"""
        y_coords = [point[1] for point in self.bbox]
        return max(y_coords) - min(y_coords)
    
    @property
    def area(self) -> float:
        """获取文本区域面积（近似）"""
        return self.width * self.height
    
    def __str__(self) -> str:
        return f"TextRegion(text='{self.text}', confidence={self.confidence:.3f}, bbox={self.bbox})"
