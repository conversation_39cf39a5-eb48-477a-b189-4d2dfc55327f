#!/usr/bin/env python3
"""
图像处理模块 - 图像加载、预处理和文本区域定位

提供图像处理管道，包括加载、预处理、坐标转换等功能
"""

import os
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from pathlib import Path

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import get_config
from .text_region import TextRegion


@dataclass
class ImageInfo:
    """图像信息数据类"""
    width: int                  # 图像宽度
    height: int                 # 图像高度
    channels: int               # 通道数
    dtype: str                  # 数据类型
    file_path: Optional[str] = None  # 文件路径
    file_size: Optional[int] = None  # 文件大小


@dataclass
class ProcessedRegion:
    """处理后的文本区域"""
    original_region: TextRegion  # 原始OCR区域
    translated_text: str         # 翻译后文本
    compressed_text: Optional[str] = None  # 压缩后文本
    font_info: Optional[Dict] = None  # 字体信息
    render_bbox: Optional[List[List[int]]] = None  # 渲染边界框


class ImageProcessor:
    """图像处理器类"""
    
    def __init__(self):
        """初始化图像处理器"""
        self.logger = get_logger()
        self.config = get_config()

        # 从配置文件加载图像处理参数
        image_config = self.config.get_image_processing_config()

        # 支持的图像格式
        self.supported_formats = set(image_config.get('supported_formats', ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']))

        # 图像处理参数
        max_size = image_config.get('max_image_size', [4096, 4096])
        min_size = image_config.get('min_image_size', [100, 100])
        self.max_image_size = tuple(max_size)
        self.min_image_size = tuple(min_size)

        # 渲染配置
        self.render_config = image_config.get('render', {})

        self.logger.info("图像处理器初始化完成")
        self.logger.debug(f"图像尺寸限制: {self.min_image_size} - {self.max_image_size}")
        self.logger.debug(f"支持格式: {sorted(self.supported_formats)}")
    
    def load_image(self, image_path: str) -> Tuple[np.ndarray, ImageInfo]:
        """
        加载图像文件
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像数组和图像信息
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")

            # 检查文件大小
            file_size = os.path.getsize(image_path)
            if file_size == 0:
                raise ValueError(f"图像文件为空: {image_path}")

            # 检查文件格式
            file_ext = Path(image_path).suffix.lower()
            if file_ext not in self.supported_formats:
                supported_list = ', '.join(sorted(self.supported_formats))
                raise ValueError(f"不支持的图像格式: {file_ext}。支持的格式: {supported_list}")

            # 使用OpenCV加载图像
            try:
                image = cv2.imread(image_path)
                if image is None:
                    raise ValueError(f"OpenCV无法读取图像文件，可能文件已损坏: {image_path}")
            except Exception as e:
                raise ValueError(f"图像文件读取失败: {e}")

            # 验证图像尺寸
            height, width = image.shape[:2]
            if height <= 0 or width <= 0:
                raise ValueError(f"图像尺寸无效: {width}x{height}")

            # 检查图像尺寸限制
            if width < self.min_image_size[0] or height < self.min_image_size[1]:
                raise ValueError(f"图像尺寸太小: {width}x{height}，最小尺寸: {self.min_image_size}")

            # 转换为RGB格式（OpenCV默认是BGR）
            try:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            except Exception as e:
                raise ValueError(f"图像颜色空间转换失败: {e}")

            # 获取图像信息
            height, width, channels = image_rgb.shape

            image_info = ImageInfo(
                width=width,
                height=height,
                channels=channels,
                dtype=str(image_rgb.dtype),
                file_path=image_path,
                file_size=file_size
            )

            self.logger.info(f"成功加载图像: {image_path} ({width}x{height}, {file_size/1024:.1f}KB)")
            return image_rgb, image_info

        except FileNotFoundError:
            raise
        except ValueError:
            raise
        except Exception as e:
            self.logger.error(f"加载图像时发生未知错误: {e}")
            raise RuntimeError(f"图像加载失败: {e}")
    
    def preprocess_image(self, image: np.ndarray, enhance_text: bool = True) -> np.ndarray:
        """
        预处理图像以提高OCR效果
        
        Args:
            image: 输入图像
            enhance_text: 是否增强文本
            
        Returns:
            预处理后的图像
        """
        try:
            processed = image.copy()
            
            # 检查图像尺寸
            height, width = processed.shape[:2]
            
            # 如果图像太大，进行缩放
            if width > self.max_image_size[0] or height > self.max_image_size[1]:
                scale = min(self.max_image_size[0] / width, self.max_image_size[1] / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                processed = cv2.resize(processed, (new_width, new_height), interpolation=cv2.INTER_AREA)
                self.logger.info(f"图像缩放: {width}x{height} -> {new_width}x{new_height}")
            
            # 如果需要增强文本
            if enhance_text:
                # 转换为灰度图像
                gray = cv2.cvtColor(processed, cv2.COLOR_RGB2GRAY)
                
                # 应用高斯模糊去噪
                blurred = cv2.GaussianBlur(gray, (3, 3), 0)
                
                # 应用锐化滤波器
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                sharpened = cv2.filter2D(blurred, -1, kernel)
                
                # 转换回RGB
                processed = cv2.cvtColor(sharpened, cv2.COLOR_GRAY2RGB)
                
                self.logger.debug("应用文本增强处理")
            
            return processed
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            return image
    
    def extract_text_regions(self, image: np.ndarray, text_regions: List[TextRegion]) -> List[np.ndarray]:
        """
        从图像中提取文本区域
        
        Args:
            image: 输入图像
            text_regions: 文本区域列表
            
        Returns:
            文本区域图像列表
        """
        region_images = []
        
        for i, region in enumerate(text_regions):
            try:
                # 获取边界框坐标
                bbox = region.bbox
                if len(bbox) != 4:
                    self.logger.warning(f"区域 {i} 边界框格式错误")
                    continue
                
                # 计算矩形边界
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                
                x_min, x_max = min(x_coords), max(x_coords)
                y_min, y_max = min(y_coords), max(y_coords)
                
                # 确保坐标在图像范围内
                height, width = image.shape[:2]
                x_min = max(0, min(x_min, width - 1))
                x_max = max(0, min(x_max, width - 1))
                y_min = max(0, min(y_min, height - 1))
                y_max = max(0, min(y_max, height - 1))
                
                # 提取区域
                if x_max > x_min and y_max > y_min:
                    region_image = image[y_min:y_max, x_min:x_max]
                    region_images.append(region_image)
                    self.logger.debug(f"提取文本区域 {i}: ({x_min},{y_min}) -> ({x_max},{y_max})")
                else:
                    self.logger.warning(f"区域 {i} 坐标无效")
                    
            except Exception as e:
                self.logger.error(f"提取文本区域 {i} 失败: {e}")
                continue
        
        self.logger.info(f"成功提取 {len(region_images)} 个文本区域")
        return region_images
    
    def calculate_text_metrics(self, text_regions: List[TextRegion]) -> Dict[str, Any]:
        """
        计算文本区域的度量信息
        
        Args:
            text_regions: 文本区域列表
            
        Returns:
            度量信息字典
        """
        if not text_regions:
            return {}
        
        # 计算各种度量
        areas = [region.area for region in text_regions]
        widths = [region.width for region in text_regions]
        heights = [region.height for region in text_regions]
        confidences = [region.confidence for region in text_regions]
        
        metrics = {
            'total_regions': len(text_regions),
            'avg_area': np.mean(areas),
            'avg_width': np.mean(widths),
            'avg_height': np.mean(heights),
            'avg_confidence': np.mean(confidences),
            'min_confidence': min(confidences),
            'max_confidence': max(confidences),
            'total_area': sum(areas),
            'area_coverage': 0.0  # 需要图像尺寸来计算
        }
        
        self.logger.debug(f"文本度量: {metrics}")
        return metrics
    
    def create_region_mask(self, image_shape: Tuple[int, int], text_regions: List[TextRegion]) -> np.ndarray:
        """
        创建文本区域掩码
        
        Args:
            image_shape: 图像形状 (height, width)
            text_regions: 文本区域列表
            
        Returns:
            掩码数组
        """
        height, width = image_shape
        mask = np.zeros((height, width), dtype=np.uint8)
        
        for region in text_regions:
            try:
                # 获取边界框坐标
                bbox = region.bbox
                if len(bbox) != 4:
                    continue
                
                # 创建多边形点
                points = np.array(bbox, dtype=np.int32)
                
                # 填充多边形区域
                cv2.fillPoly(mask, [points], 255)
                
            except Exception as e:
                self.logger.error(f"创建区域掩码失败: {e}")
                continue
        
        return mask
    
    def optimize_region_layout(self, text_regions: List[TextRegion], 
                             image_shape: Tuple[int, int]) -> List[TextRegion]:
        """
        优化文本区域布局
        
        Args:
            text_regions: 文本区域列表
            image_shape: 图像形状
            
        Returns:
            优化后的文本区域列表
        """
        if not text_regions:
            return text_regions
        
        optimized_regions = []
        
        # 按Y坐标排序（从上到下）
        sorted_regions = sorted(text_regions, key=lambda r: r.center[1])
        
        # 检测行
        lines = []
        current_line = []
        line_threshold = 20  # 行间距阈值
        
        for region in sorted_regions:
            if not current_line:
                current_line.append(region)
            else:
                # 检查是否在同一行
                last_region = current_line[-1]
                y_diff = abs(region.center[1] - last_region.center[1])
                
                if y_diff <= line_threshold:
                    current_line.append(region)
                else:
                    # 新行
                    lines.append(current_line)
                    current_line = [region]
        
        if current_line:
            lines.append(current_line)
        
        # 对每行内的区域按X坐标排序
        for line in lines:
            line.sort(key=lambda r: r.center[0])
            optimized_regions.extend(line)
        
        self.logger.info(f"布局优化: {len(text_regions)} 个区域分为 {len(lines)} 行")
        return optimized_regions
    
    def get_supported_formats(self) -> set:
        """获取支持的图像格式"""
        return self.supported_formats.copy()
    
    def is_available(self) -> bool:
        """检查图像处理器是否可用"""
        return True  # OpenCV和numpy总是可用的
    
    def get_render_config(self, key: str = None, default=None):
        """
        获取渲染配置

        Args:
            key: 配置键名，如果为None则返回整个渲染配置
            default: 默认值

        Returns:
            配置值或整个配置字典
        """
        if key is None:
            return self.render_config
        return self.render_config.get(key, default)

    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            "opencv_available": True,
            "pil_available": PIL_AVAILABLE,
            "supported_formats": list(self.supported_formats),
            "max_image_size": self.max_image_size,
            "min_image_size": self.min_image_size,
            "render_config": self.render_config,
            "available": self.is_available()
        }


# 全局图像处理器实例
_image_processor = None


def get_image_processor() -> ImageProcessor:
    """获取全局图像处理器实例"""
    global _image_processor
    if _image_processor is None:
        _image_processor = ImageProcessor()
    return _image_processor
