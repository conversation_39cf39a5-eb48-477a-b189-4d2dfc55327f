# 跳过本地翻译功能使用指南

## 🎯 功能概述

新增的"跳过本地翻译"功能允许你绕过本地NLLB机器翻译模型，直接使用OpenAI等LLM进行翻译和优化，从而获得更高质量的翻译结果。

## 🔄 翻译流程对比

### 原有流程（默认）
```
OCR识别 → 本地NLLB机翻 → OpenAI优化 → 渲染结果
```

### 新流程（跳过本地翻译）
```
OCR识别 → OpenAI直接翻译 → 渲染结果
```

## ⚙️ 配置方法

### 1. 修改配置文件

在 `config.yaml` 中设置：

```yaml
translation:
  skip_local_translation: true  # 启用跳过本地翻译
  enable_compression: true      # 确保启用LLM处理
```

### 2. 确保API配置正确

```yaml
models:
  compression:
    type: api  # 使用API模式
    api:
      provider: openai
      model: gpt-4o
      api_key: your_openai_api_key
      base_url: https://api.openai.com/v1
```

## 🚀 使用方法

### 方法1：通过配置文件（推荐）

1. 编辑 `config.yaml`
2. 设置 `translation.skip_local_translation: true`
3. 重启应用程序
4. 正常使用图片翻译功能

### 方法2：通过代码

```python
from src.core.translation_pipeline import TranslationPipeline, TranslationPipelineConfig

# 创建配置，启用跳过本地翻译
config = TranslationPipelineConfig(
    source_language='eng_Latn',
    target_language='zho_Hans',
    skip_local_translation=True,  # 关键设置
    compression_enabled=True
)

# 创建翻译管道
pipeline = TranslationPipeline(config=config)

# 使用翻译管道
texts = ["Hello World", "Good Morning"]
results = pipeline.translate_batch(texts)
```

## 🎨 LLM提示词说明

系统会自动检测翻译模式：

- **模式1（优化模式）**：当输入包含"原文"和"译文"时，LLM优化已有翻译
- **模式2（直接翻译模式）**：当输入只包含"原文"时，LLM直接翻译

## ✅ 优势

1. **更高质量**：直接使用GPT-4等先进模型翻译
2. **更好理解**：LLM能更好理解上下文和语境
3. **减少错误传播**：避免本地翻译错误影响最终结果
4. **节省资源**：不需要加载大型本地翻译模型

## ⚠️ 注意事项

1. **API成本**：使用OpenAI API会产生费用
2. **网络依赖**：需要稳定的网络连接
3. **速度影响**：API调用可能比本地翻译慢
4. **配置要求**：必须正确配置API密钥

## 🔧 故障排除

### 问题1：功能未生效
- 检查 `config.yaml` 中的设置
- 确认 `models.compression.type` 设置为 `api`
- 重启应用程序

### 问题2：API调用失败
- 检查API密钥是否正确
- 确认网络连接正常
- 查看日志中的错误信息

### 问题3：翻译质量不佳
- 尝试使用更先进的模型（如gpt-4o）
- 调整系统提示词
- 检查源语言识别是否准确

## 📝 测试验证

运行测试脚本验证功能：

```bash
python test_skip_translation.py
```

测试脚本会验证：
- 配置是否正确加载
- 翻译管道是否正确初始化
- LLM提示词是否更新
- 功能是否正常工作

## 🎉 总结

跳过本地翻译功能为你提供了更灵活的翻译选择。通过简单的配置修改，你可以：

- 获得更高质量的翻译结果
- 减少翻译流程的复杂性
- 充分利用先进的LLM能力

根据你的需求和资源情况，可以灵活选择使用本地翻译+LLM优化，或直接使用LLM翻译的模式。
