#!/usr/bin/env python3
"""
设备管理器模块 - 跨平台GPU加速支持

提供智能的设备检测和管理，支持CUDA、MPS和CPU
"""

import torch
import platform
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass

from .logger import get_logger
from .config import get_config


@dataclass
class DeviceInfo:
    """设备信息数据类"""
    device: torch.device
    device_type: str  # 'cuda', 'mps', 'cpu'
    device_name: str
    memory_total: Optional[int] = None  # 总内存 (MB)
    memory_available: Optional[int] = None  # 可用内存 (MB)
    compute_capability: Optional[str] = None  # 计算能力
    is_available: bool = True


class DeviceManager:
    """设备管理器类"""
    
    def __init__(self):
        """初始化设备管理器"""
        self.logger = get_logger()
        self.config = get_config()
        
        # 设备信息缓存
        self._device_info_cache: Optional[DeviceInfo] = None
        self._optimal_device: Optional[torch.device] = None
        
        # 初始化设备
        self._initialize_device()
        
        self.logger.info(f"设备管理器初始化完成，使用设备: {self.get_device()}")
    
    def _initialize_device(self):
        """初始化设备"""
        # 获取配置中的设备模式
        device_mode = self.config.get('device.mode', 'auto')
        
        if device_mode == 'auto':
            self._optimal_device = self._detect_optimal_device()
        else:
            # 手动指定设备
            try:
                self._optimal_device = torch.device(device_mode)
                if not self._is_device_available(self._optimal_device):
                    self.logger.warning(f"指定设备 {device_mode} 不可用，回退到自动检测")
                    self._optimal_device = self._detect_optimal_device()
            except Exception as e:
                self.logger.error(f"无效的设备配置 {device_mode}: {e}")
                self._optimal_device = self._detect_optimal_device()
        
        # 缓存设备信息
        self._device_info_cache = self._get_device_info(self._optimal_device)
        
        # 记录设备信息
        self._log_device_info()
    
    def _detect_optimal_device(self) -> torch.device:
        """自动检测最佳设备"""
        # 检查CUDA (NVIDIA GPU)
        if torch.cuda.is_available():
            device = torch.device('cuda')
            if self._is_device_usable(device):
                self.logger.info("检测到NVIDIA GPU，使用CUDA加速")
                return device
            else:
                self.logger.warning("CUDA可用但不可用，可能是内存不足")
        
        # 检查MPS (Apple Silicon)
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = torch.device('mps')
            if self._is_device_usable(device):
                self.logger.info("检测到Apple Silicon，使用MPS加速")
                return device
            else:
                self.logger.warning("MPS可用但不可用")
        
        # 回退到CPU
        self.logger.info("使用CPU进行计算")
        return torch.device('cpu')
    
    def _is_device_available(self, device: torch.device) -> bool:
        """检查设备是否可用"""
        try:
            if device.type == 'cuda':
                return torch.cuda.is_available()
            elif device.type == 'mps':
                return hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
            elif device.type == 'cpu':
                return True
            else:
                return False
        except Exception:
            return False
    
    def _is_device_usable(self, device: torch.device) -> bool:
        """检查设备是否可用（通过创建测试张量）"""
        try:
            # 创建测试张量
            test_tensor = torch.randn(10, 10, device=device)
            # 执行简单运算
            result = test_tensor @ test_tensor.T
            del test_tensor, result
            return True
        except Exception as e:
            self.logger.debug(f"设备 {device} 测试失败: {e}")
            return False
    
    def _get_device_info(self, device: torch.device) -> DeviceInfo:
        """获取设备详细信息"""
        device_type = device.type
        
        if device_type == 'cuda':
            return self._get_cuda_info(device)
        elif device_type == 'mps':
            return self._get_mps_info(device)
        else:
            return self._get_cpu_info(device)
    
    def _get_cuda_info(self, device: torch.device) -> DeviceInfo:
        """获取CUDA设备信息"""
        try:
            device_idx = device.index or 0
            props = torch.cuda.get_device_properties(device_idx)
            
            # 获取内存信息 (转换为MB)
            memory_total = props.total_memory // (1024 * 1024)
            memory_allocated = torch.cuda.memory_allocated(device_idx) // (1024 * 1024)
            memory_available = memory_total - memory_allocated
            
            return DeviceInfo(
                device=device,
                device_type='cuda',
                device_name=props.name,
                memory_total=memory_total,
                memory_available=memory_available,
                compute_capability=f"{props.major}.{props.minor}",
                is_available=True
            )
        except Exception as e:
            self.logger.error(f"获取CUDA设备信息失败: {e}")
            return DeviceInfo(
                device=device,
                device_type='cuda',
                device_name='Unknown CUDA Device',
                is_available=False
            )
    
    def _get_mps_info(self, device: torch.device) -> DeviceInfo:
        """获取MPS设备信息"""
        try:
            # MPS设备信息相对有限
            system_info = platform.uname()
            device_name = f"Apple {system_info.machine}"
            
            return DeviceInfo(
                device=device,
                device_type='mps',
                device_name=device_name,
                is_available=True
            )
        except Exception as e:
            self.logger.error(f"获取MPS设备信息失败: {e}")
            return DeviceInfo(
                device=device,
                device_type='mps',
                device_name='Unknown MPS Device',
                is_available=False
            )
    
    def _get_cpu_info(self, device: torch.device) -> DeviceInfo:
        """获取CPU设备信息"""
        try:
            system_info = platform.uname()
            device_name = f"{system_info.processor} ({system_info.machine})"
            
            return DeviceInfo(
                device=device,
                device_type='cpu',
                device_name=device_name,
                is_available=True
            )
        except Exception:
            return DeviceInfo(
                device=device,
                device_type='cpu',
                device_name='Unknown CPU',
                is_available=True
            )
    
    def _log_device_info(self):
        """记录设备信息"""
        if not self._device_info_cache:
            return
        
        info = self._device_info_cache
        self.logger.info(f"🖥️  设备信息:")
        self.logger.info(f"   类型: {info.device_type.upper()}")
        self.logger.info(f"   名称: {info.device_name}")
        
        if info.memory_total:
            self.logger.info(f"   内存: {info.memory_available}MB / {info.memory_total}MB 可用")
        
        if info.compute_capability:
            self.logger.info(f"   计算能力: {info.compute_capability}")
    
    def get_device(self) -> torch.device:
        """获取当前设备"""
        return self._optimal_device
    
    def get_device_info(self) -> DeviceInfo:
        """获取设备信息"""
        return self._device_info_cache
    
    def get_optimal_dtype(self) -> torch.dtype:
        """获取最佳数据类型"""
        device_type = self._optimal_device.type
        
        # 根据设备类型选择最佳数据类型
        if device_type == 'cuda':
            # CUDA支持float16加速
            if self.config.get('device.enable_mixed_precision', True):
                return torch.float16
            else:
                return torch.float32
        elif device_type == 'mps':
            # MPS建议使用float32（更稳定）
            return torch.float32
        else:
            # CPU使用float32
            return torch.float32
    
    def get_device_for_model(self, model_type: str) -> torch.device:
        """
        为特定模型类型获取设备

        Args:
            model_type: 模型类型 ('ocr', 'translation', 'compression')

        Returns:
            适合该模型的设备
        """
        # OCR模型（PaddleOCR）统一使用CPU以确保跨平台兼容性
        if model_type == 'ocr':
            return torch.device('cpu')

        # 检查是否强制某些模型使用CPU
        if model_type == 'ocr' and self.config.get('device.force_cpu_for_ocr', False):
            return torch.device('cpu')

        # 其他模型使用最佳设备
        return self._optimal_device
    
    def check_memory_available(self, required_mb: int) -> bool:
        """
        检查是否有足够的GPU内存
        
        Args:
            required_mb: 需要的内存量 (MB)
            
        Returns:
            是否有足够内存
        """
        if not self._device_info_cache or not self._device_info_cache.memory_available:
            return True  # 无法检测时假设可用
        
        return self._device_info_cache.memory_available >= required_mb
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        if not self._device_info_cache:
            return {}
        
        info = self._device_info_cache
        result = {
            'device_type': info.device_type,
            'memory_total': info.memory_total,
            'memory_available': info.memory_available
        }
        
        # 对于CUDA设备，获取实时内存信息
        if info.device_type == 'cuda':
            try:
                device_idx = self._optimal_device.index or 0
                allocated = torch.cuda.memory_allocated(device_idx) // (1024 * 1024)
                cached = torch.cuda.memory_reserved(device_idx) // (1024 * 1024)
                result.update({
                    'memory_allocated': allocated,
                    'memory_cached': cached,
                    'memory_free': info.memory_total - allocated
                })
            except Exception:
                pass
        
        return result


# 全局设备管理器实例
_device_manager = None


def get_device_manager() -> DeviceManager:
    """获取全局设备管理器实例"""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager()
    return _device_manager


def get_optimal_device() -> torch.device:
    """获取最佳设备（便捷函数）"""
    return get_device_manager().get_device()


def get_optimal_dtype() -> torch.dtype:
    """获取最佳数据类型（便捷函数）"""
    return get_device_manager().get_optimal_dtype()
