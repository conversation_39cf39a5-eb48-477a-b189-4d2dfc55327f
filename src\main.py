#!/usr/bin/env python3
"""
Image Text Translator - 主程序入口

这是图像文本翻译工具的主程序入口文件
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 修复PaddleOCR在打包环境中的路径问题
from src.utils.runtime_path import get_runtime_base_path, is_packaged_environment

# 确保 PyInstaller 检测到 paddlex 依赖（用于 PaddleOCR 内部调用）
import paddlex  # noqa: F401

# 修复 paddlex 在打包环境中的依赖检查问题
def patch_paddlex_deps():
    """修复 paddlex 在打包环境中的依赖检查问题"""
    try:
        import paddlex.utils.deps as deps

        # 保存原始函数
        original_is_extra_available = deps.is_extra_available
        original_require_extra = deps.require_extra

        # 创建补丁函数
        def patched_is_extra_available(extra):
            if extra == 'ocr':
                return True  # 在打包环境中总是返回 True
            return original_is_extra_available(extra)

        def patched_require_extra(extra, *, obj_name=None):
            if extra == 'ocr':
                return  # 在打包环境中跳过 OCR 依赖检查
            return original_require_extra(extra, obj_name=obj_name)

        # 应用补丁
        deps.is_extra_available = patched_is_extra_available
        deps.require_extra = patched_require_extra

        print("✅ PaddleX 依赖检查补丁已应用")

    except Exception as e:
        print(f"⚠️ 应用 PaddleX 补丁时出错: {e}")

# 应用补丁
patch_paddlex_deps()

# Mac 平台特殊设置
def setup_mac_compatibility():
    """Mac 平台兼容性设置"""
    import platform
    if platform.system() == 'Darwin':  # macOS
        print("🍎 检测到 macOS，应用兼容性设置...")

        # 强制 PaddleOCR 使用 CPU（Mac 不支持 GPU 加速）
        import os
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

        # 设置 PaddlePaddle 使用 CPU
        try:
            import paddle
            paddle.set_device('cpu')
            print("✅ 已强制设置 PaddlePaddle 使用 CPU")
        except Exception as e:
            print(f"⚠️ PaddlePaddle CPU 设置失败: {e}")

        # 验证 PyTorch 版本（应该是 CPU 版本）
        try:
            import torch
            if torch.cuda.is_available():
                print("⚠️ 检测到 CUDA 版本的 PyTorch，但在 Mac 上应该使用 CPU 版本")
            else:
                print("✅ 正确使用 CPU 版本的 PyTorch")
            print(f"📦 PyTorch 版本: {torch.__version__}")
        except Exception as e:
            print(f"⚠️ PyTorch 版本检查失败: {e}")

# 应用 Mac 兼容性设置
setup_mac_compatibility()

if is_packaged_environment():
    # 在打包环境中，设置PaddleOCR相关的环境变量
    base_path = get_runtime_base_path()

    # 设置PaddleOCR的缓存目录到我们的cache目录
    paddle_cache_dir = base_path / "cache" / "paddleocr"
    paddle_cache_dir.mkdir(parents=True, exist_ok=True)

    # 设置环境变量
    os.environ['PADDLE_HOME'] = str(paddle_cache_dir)
    os.environ['PADDLEOCR_HOME'] = str(paddle_cache_dir)

    # 设置PaddleX的路径（如果需要）
    paddlex_cache_dir = base_path / "cache" / "paddlex"
    paddlex_cache_dir.mkdir(parents=True, exist_ok=True)
    os.environ['PADDLEX_HOME'] = str(paddlex_cache_dir)

def main():
    """主函数"""
    try:
        print("启动 Image Text Translator...")

        # 初始化配置系统
        print("初始化配置系统...")
        from src.utils.config import init_config, get_config
        from src.utils.logger import get_logger
        from src.utils.device_manager import get_device_manager

        init_config()
        logger = get_logger()
        logger.info("应用程序启动")

        # 初始化设备管理器
        device_manager = get_device_manager()
        logger.info(f"使用设备: {device_manager.get_device()}")

        # 创建QApplication实例
        app = QApplication(sys.argv)

        # 获取配置
        config = get_config()
        app.setApplicationName(config.get('app.name', 'Image Text Translator'))
        app.setApplicationVersion(config.get('app.version', '1.0.0'))
        app.setOrganizationName("ImageTextTranslator")

        # 设置应用程序字体和样式
        font = QFont()
        font.setFamily("Segoe UI, Microsoft YaHei, sans-serif")
        font.setPointSize(9)
        app.setFont(font)

        app.setStyleSheet("""
            QApplication {
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
            }
        """)

        print("创建用户界面...")

        # 导入并创建主窗口
        from src.ui.main_window import MainWindow

        # 创建主窗口
        main_window = MainWindow()

        # 设置窗口大小和位置
        window_size = config.get('ui.window_size', [1000, 700])
        main_window.resize(window_size[0], window_size[1])

        # 居中显示
        screen = app.primaryScreen()
        screen_geometry = screen.availableGeometry()
        window_geometry = main_window.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        main_window.move(window_geometry.topLeft())

        # 显示主窗口
        main_window.show()
        main_window.raise_()
        main_window.activateWindow()

        print("Image Text Translator 启动成功!")
        logger.info("应用程序界面启动完成")

        # 运行应用程序
        return app.exec()

    except Exception as e:
        error_msg = f"应用程序启动失败: {str(e)}"
        print(f"错误: {error_msg}")

        # 尝试记录错误
        try:
            if 'logger' in locals():
                logger.error(error_msg)
        except:
            pass

        # 显示错误对话框
        if 'app' in locals():
            msg = QMessageBox()
            msg.setWindowTitle("启动错误")
            msg.setText(f"应用程序启动失败:\n\n{str(e)}")
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.exec()

        return 1

if __name__ == "__main__":
    sys.exit(main())
