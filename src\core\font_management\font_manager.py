"""
核心字体管理器

统一管理NLLB-200所有语言的字体支持
提供智能字体选择、下载、缓存和回退机制
"""

import os
from pathlib import Path
from typing import Optional, List, Dict, Callable, Any
from dataclasses import dataclass
import threading
from concurrent.futures import Future

from ...utils.logger import get_logger
from ...utils.config import get_config
from .noto_font_registry import NotoFontInfo, get_font_registry
from .language_script_mapper import get_language_script_mapper
from .font_downloader import get_font_downloader, DownloadProgress
from .font_validator import get_font_validator

@dataclass
class FontInfo:
    """字体信息（兼容现有接口）"""
    name: str
    path: str
    family: str = ""
    style: str = "normal"
    size: int = 24
    weight: str = "normal"
    is_monospace: bool = False
    supports_unicode: bool = True

class FontManager:
    """核心字体管理器"""

    # 常量定义
    DEFAULT_CACHE_DIR = Path("cache/fonts")
    SYSTEM_FONTS = {
        'ARIAL': 'C:/Windows/Fonts/arial.ttf',
        'SIMHEI': 'C:/Windows/Fonts/simhei.ttf',
        'DEJAVU_SANS': '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
        'HELVETICA': '/System/Library/Fonts/Helvetica.ttc'
    }

    def __init__(self, cache_dir: Optional[Path] = None):
        self.logger = get_logger()
        self.config = get_config()

        # 设置缓存目录
        self.cache_dir = cache_dir or self.DEFAULT_CACHE_DIR

        # 初始化组件
        self.font_registry = get_font_registry()
        self.language_mapper = get_language_script_mapper()
        self.font_downloader = get_font_downloader(self.cache_dir)
        self.font_validator = get_font_validator()

        # 字体缓存
        self._font_cache: Dict[str, FontInfo] = {}
        self._cache_lock = threading.RLock()

        # 下载状态
        self._download_futures: Dict[str, Future] = {}

        self.logger.info("字体管理器初始化完成")
    
    def get_font_for_language(self, language_code: str,
                             auto_download: bool = True,
                             progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Optional[FontInfo]:
        """
        获取支持指定语言的Noto字体 (严格Noto字体依赖)

        Args:
            language_code: NLLB语言代码 (如 'zho_Hans', 'ara_Arab')
            auto_download: 是否自动下载缺失的字体
            progress_callback: 下载进度回调

        Returns:
            Noto字体信息，如果无法获取则返回None
        """
        try:
            with self._cache_lock:
                # 检查缓存
                if language_code in self._font_cache:
                    return self._font_cache[language_code]

            self.logger.debug(f"为语言 {language_code} 查找Noto字体")

            # 1. 尝试获取最佳Noto字体
            best_font = self.language_mapper.get_best_font_for_language(language_code)
            if best_font:
                try:
                    font_path = self._get_or_download_noto_font(best_font, auto_download, progress_callback)
                    if font_path:
                        font_info = self._create_font_info(best_font, font_path)
                        with self._cache_lock:
                            self._font_cache[language_code] = font_info
                        return font_info
                except Exception as e:
                    self.logger.warning(f"无法获取主要Noto字体 {best_font.name}: {e}")
                    # 继续尝试回退字体

            # 2. 尝试Noto字体回退链
            fallback_fonts = self.language_mapper.get_fallback_fonts_for_language(language_code)
            if fallback_fonts:
                for font in fallback_fonts:
                    try:
                        font_path = self._get_or_download_noto_font(font, auto_download, progress_callback)
                        if font_path:
                            font_info = self._create_font_info(font, font_path)
                            with self._cache_lock:
                                self._font_cache[language_code] = font_info
                            self.logger.info(f"使用Noto回退字体 {font.name} 支持语言 {language_code}")
                            return font_info
                    except Exception as e:
                        self.logger.warning(f"无法获取回退Noto字体 {font.name}: {e}")
                        continue

                # 所有Noto回退字体都失败
                error_msg = f"无法获取或下载语言 {language_code} 的所有Noto回退字体"
                self.logger.error(error_msg)
                if not auto_download:
                    self.logger.info("提示：可以尝试启用auto_download=True来自动下载字体")
            else:
                # 没有找到任何支持该语言的Noto字体
                error_msg = f"不支持的语言代码: {language_code}，没有对应的Noto字体"
                self.logger.error(error_msg)

            # 所有尝试都失败，返回None
            return None

        except Exception as e:
            # 捕获所有异常，确保不会传播到上层
            self.logger.error(f"获取语言 {language_code} 的Noto字体时发生异常: {e}")
            return None
    
    def get_font_for_text(self, text: str,
                         auto_download: bool = True,
                         progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Optional[FontInfo]:
        """
        根据文本内容自动选择Noto字体 (严格Noto字体依赖)

        Args:
            text: 要渲染的文本
            auto_download: 是否自动下载缺失的字体
            progress_callback: 下载进度回调

        Returns:
            Noto字体信息，如果无法获取则返回None
        """
        try:
            # 检测文本的文字系统
            detected_scripts = self.language_mapper.detect_script_from_text(text)

            if not detected_scripts:
                # 如果无法检测，使用通用拉丁字体
                self.logger.info("无法检测文本的文字系统，使用通用拉丁字体")
                return self.get_font_for_language("eng_Latn", auto_download, progress_callback)

            # 为每个检测到的文字系统尝试获取Noto字体
            for script in detected_scripts:
                self.logger.info(f"检测到文本使用文字系统: {script.value}")
                fonts = self.language_mapper.get_fonts_for_script(script)

                if not fonts:
                    self.logger.warning(f"没有找到支持文字系统 {script.value} 的Noto字体")
                    continue

                for font in fonts:
                    try:
                        font_path = self._get_or_download_noto_font(font, auto_download, progress_callback)
                        if font_path:
                            self.logger.info(f"为文本选择Noto字体: {font.name}")
                            return self._create_font_info(font, font_path)
                    except Exception as e:
                        self.logger.warning(f"获取Noto字体 {font.name} 失败: {e}")
                        continue

            # 所有文字系统的Noto字体都获取失败
            error_msg = f"无法为文本找到合适的Noto字体，检测到的文字系统: {[s.value for s in detected_scripts]}"
            self.logger.error(error_msg)
            return None

        except Exception as e:
            # 捕获所有异常，确保不会传播到上层
            self.logger.error(f"为文本选择Noto字体时发生异常: {e}")
            return None
    
    def _get_or_download_noto_font(self, noto_font: NotoFontInfo,
                                  auto_download: bool,
                                  progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Optional[Path]:
        """
        获取或下载Noto字体文件 (严格Noto字体依赖)

        Args:
            noto_font: Noto字体信息
            auto_download: 是否自动下载
            progress_callback: 下载进度回调

        Returns:
            字体文件路径，失败时返回None
        """
        try:
            font_path = self.cache_dir / noto_font.filename

            # 检查字体是否已存在且有效
            if self.font_validator.validate_font_file(font_path):
                self.logger.debug(f"Noto字体 {noto_font.name} 已存在且有效")
                return font_path

            if not auto_download:
                self.logger.warning(f"Noto字体 {noto_font.name} 不存在且未启用自动下载")
                return None

            # 检查是否正在下载
            if self.font_downloader.is_downloading(noto_font.name):
                self.logger.info(f"Noto字体 {noto_font.name} 正在下载中，等待完成...")
                future = self._download_futures.get(noto_font.name)
                if future:
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        if result and self.font_validator.validate_font_file(result):
                            return result
                        else:
                            self.logger.error(f"Noto字体 {noto_font.name} 下载完成但验证失败")
                            return None
                    except Exception as e:
                        self.logger.error(f"等待Noto字体 {noto_font.name} 下载失败: {e}")
                        return None

            # 开始下载Noto字体
            self.logger.info(f"开始下载Noto字体: {noto_font.name}")
            future = self.font_downloader.download_font_async(noto_font, progress_callback)
            self._download_futures[noto_font.name] = future

            try:
                result = future.result(timeout=300)  # 5分钟超时
                if result and self.font_validator.validate_font_file(result):
                    self.logger.info(f"Noto字体 {noto_font.name} 下载并验证成功")
                    return result
                else:
                    self.logger.error(f"Noto字体 {noto_font.name} 下载失败或验证失败")
                    return None
            except Exception as e:
                self.logger.error(f"Noto字体 {noto_font.name} 下载过程中发生错误: {e}")
                return None
            finally:
                self._download_futures.pop(noto_font.name, None)

        except Exception as e:
            # 捕获所有异常，确保不会传播到上层
            self.logger.error(f"获取或下载Noto字体 {noto_font.name} 时发生异常: {e}")
            return None

        except Exception as e:
            self.logger.error(f"获取或下载Noto字体 {noto_font.name} 时发生异常: {e}")
            return None
    
    def _create_font_info(self, noto_font: NotoFontInfo, font_path: Path) -> FontInfo:
        """创建字体信息对象"""
        return FontInfo(
            name=noto_font.name,
            path=str(font_path),
            family=noto_font.name,
            style="normal",
            size=24,
            weight="normal",
            is_monospace=False,
            supports_unicode=True
        )
    
    def _get_system_font_fallback(self, language_code: str) -> Optional[FontInfo]:
        """获取系统字体回退"""
        # 根据语言选择系统字体类型
        script = self.language_mapper.get_script_for_language(language_code)

        # 系统字体映射表
        system_font_map = {
            'cjk': [
                # 中文字体
                'C:/Windows/Fonts/simhei.ttf',       # 黑体
                'C:/Windows/Fonts/simsun.ttc',       # 宋体
                'C:/Windows/Fonts/simkai.ttf',       # 楷体
                'C:/Windows/Fonts/msyh.ttc',         # 微软雅黑
                # 日文字体
                'C:/Windows/Fonts/msgothic.ttc',     # MS Gothic
                'C:/Windows/Fonts/meiryo.ttc',       # Meiryo
                # 韩文字体
                'C:/Windows/Fonts/malgun.ttf',       # Malgun Gothic
                # macOS中文字体
                '/System/Library/Fonts/PingFang.ttc',
                '/Library/Fonts/Hiragino Sans GB.ttc',
                # Linux中文字体
                '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',
                '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'
            ],
            'arabic': [
                'C:/Windows/Fonts/arial.ttf',        # Arial (含阿拉伯文支持)
                'C:/Windows/Fonts/tahoma.ttf',       # Tahoma
                'C:/Windows/Fonts/segoeui.ttf',      # Segoe UI
                '/System/Library/Fonts/Arial Unicode.ttf',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'
            ],
            'devanagari': [
                'C:/Windows/Fonts/mangal.ttf',       # Mangal
                'C:/Windows/Fonts/aparajita.ttf',    # Aparajita
                '/System/Library/Fonts/Kohinoor.ttc',
                '/usr/share/fonts/truetype/noto/NotoSansDevanagari-Regular.ttf'
            ],
            'thai': [
                'C:/Windows/Fonts/leelawad.ttf',     # Leelawadee
                'C:/Windows/Fonts/tahoma.ttf',       # Tahoma
                '/System/Library/Fonts/Thonburi.ttc',
                '/usr/share/fonts/truetype/tlwg/Garuda.ttf'
            ],
            'hebrew': [
                'C:/Windows/Fonts/david.ttf',        # David
                'C:/Windows/Fonts/arial.ttf',        # Arial
                '/System/Library/Fonts/Arial Hebrew.ttf',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'
            ],
            'bengali': [
                'C:/Windows/Fonts/vrinda.ttf',       # Vrinda
                '/System/Library/Fonts/Kohinoor.ttc',
                '/usr/share/fonts/truetype/noto/NotoSansBengali-Regular.ttf'
            ],
            'tamil': [
                'C:/Windows/Fonts/latha.ttf',        # Latha
                '/System/Library/Fonts/InaiMathi.ttf',
                '/usr/share/fonts/truetype/noto/NotoSansTamil-Regular.ttf'
            ],
            'latin': [
                'C:/Windows/Fonts/arial.ttf',        # Arial
                'C:/Windows/Fonts/times.ttf',        # Times New Roman
                'C:/Windows/Fonts/calibri.ttf',      # Calibri
                'C:/Windows/Fonts/segoeui.ttf',      # Segoe UI
                '/System/Library/Fonts/Helvetica.ttc',
                '/System/Library/Fonts/Times.ttc',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf'
            ]
        }

        # 确定要使用的字体列表
        font_paths = []

        # 1. 根据脚本类型选择字体
        if script:
            script_value = script.value.lower()
            if script_value in system_font_map:
                font_paths.extend(system_font_map[script_value])

        # 2. 添加配置文件中的字体
        if script and script.value in ['cjk', 'chinese']:
            font_paths.extend(self.config.get_font_paths('chinese'))
        else:
            font_paths.extend(self.config.get_font_paths('english'))

        # 3. 添加通用回退字体
        font_paths.extend(system_font_map['latin'])

        # 查找可用的系统字体
        for font_path in font_paths:
            if os.path.exists(font_path):
                self.logger.debug(f"找到系统回退字体: {font_path}")
                return FontInfo(
                    name="System Font",
                    path=font_path,
                    family="System",
                    style="normal",
                    size=24,
                    weight="normal",
                    is_monospace=False,
                    supports_unicode=True
                )

        # 最后的回退：使用任何可用的系统字体
        self.logger.warning(f"无法找到适合语言 {language_code} 的系统字体，使用默认回退")

        # 尝试使用Arial或任何可用的字体
        fallback_paths = [
            'C:/Windows/Fonts/arial.ttf',
            '/System/Library/Fonts/Helvetica.ttc',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'
        ]

        for path in fallback_paths:
            if os.path.exists(path):
                return FontInfo(
                    name="Fallback Font",
                    path=path,
                    family="Fallback",
                    style="normal",
                    size=24,
                    weight="normal",
                    is_monospace=False,
                    supports_unicode=True
                )

        return None
    
    def preload_fonts_for_languages(self, language_codes: List[str],
                                   progress_callback: Optional[Callable[[str, DownloadProgress], None]] = None):
        """预加载指定语言的字体"""
        self.logger.info(f"开始预加载 {len(language_codes)} 种语言的字体")
        
        for language_code in language_codes:
            def lang_progress_callback(progress: DownloadProgress):
                if progress_callback:
                    progress_callback(language_code, progress)
            
            self.get_font_for_language(language_code, auto_download=True, 
                                     progress_callback=lang_progress_callback)
    
    def get_download_progress(self, font_name: str) -> Optional[DownloadProgress]:
        """获取字体下载进度"""
        return self.font_downloader.get_download_progress(font_name)
    
    def cancel_download(self, font_name: str) -> bool:
        """取消字体下载"""
        return self.font_downloader.cancel_download(font_name)
    
    def clear_cache(self):
        """清理字体缓存"""
        with self._cache_lock:
            self._font_cache.clear()
        self.logger.info("字体缓存已清理")
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        return list(self.language_mapper.get_supported_languages())
    
    def is_language_supported(self, language_code: str) -> bool:
        """检查是否支持指定语言"""
        return self.language_mapper.is_language_supported(language_code)
    
    def get_font_info(self) -> Dict[str, Any]:
        """获取字体管理器信息"""
        return {
            "supported_languages": len(self.get_supported_languages()),
            "cached_fonts": len(self._font_cache),
            "active_downloads": len(self._download_futures),
            "cache_directory": str(Path("cache/fonts")),
            "registry_fonts": len(self.font_registry.get_all_fonts())
        }
    
    def cleanup(self):
        """清理资源"""
        self.font_downloader.cleanup()
        self.clear_cache()
        self.logger.info("字体管理器已清理")

# 全局字体管理器实例
_font_manager = None

def get_font_manager() -> FontManager:
    """获取全局字体管理器实例"""
    global _font_manager
    if _font_manager is None:
        _font_manager = FontManager()
    return _font_manager
