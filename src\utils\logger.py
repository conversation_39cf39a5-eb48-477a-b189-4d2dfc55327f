#!/usr/bin/env python3
"""
日志系统模块

提供统一的日志记录功能
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname') and record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class Logger:
    """日志管理器"""
    
    def __init__(self, name: str = "ImageTextTranslator", log_dir: Optional[str] = None):
        """
        初始化日志器
        
        Args:
            name: 日志器名称
            log_dir: 日志目录，默认为项目根目录下的logs
        """
        self.name = name
        self.logger = logging.getLogger(name)
        
        # 设置日志级别
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            # 设置日志目录
            if log_dir is None:
                project_root = Path(__file__).parent.parent.parent
                log_dir = project_root / "logs"
            else:
                log_dir = Path(log_dir)
            
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建处理器
            self._setup_handlers(log_dir)
    
    def _setup_handlers(self, log_dir: Path):
        """设置日志处理器"""
        
        # 1. 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 控制台格式（带颜色）
        console_format = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_format)
        
        # 2. 文件处理器（所有日志）
        log_file = log_dir / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 文件格式
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_format)
        
        # 3. 错误文件处理器
        error_file = log_dir / f"{self.name}_error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_format)
        
        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def debug(self, message: str, *args, **kwargs):
        """调试日志"""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """信息日志"""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """警告日志"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """错误日志"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """异常日志（包含堆栈信息）"""
        self.logger.exception(message, *args, **kwargs)
    
    def log_function_call(self, func_name: str, *args, **kwargs):
        """记录函数调用"""
        args_str = ', '.join(str(arg) for arg in args)
        kwargs_str = ', '.join(f"{k}={v}" for k, v in kwargs.items())
        params = ', '.join(filter(None, [args_str, kwargs_str]))
        self.debug(f"Calling {func_name}({params})")
    
    def log_performance(self, operation: str, duration: float):
        """记录性能信息"""
        self.info(f"Performance: {operation} took {duration:.3f} seconds")
    
    def log_model_info(self, model_name: str, model_size: str, load_time: float):
        """记录模型加载信息"""
        self.info(f"Model loaded: {model_name} (size: {model_size}, load time: {load_time:.3f}s)")
    
    def log_translation_stats(self, source_lang: str, target_lang: str, 
                            text_count: int, processing_time: float):
        """记录翻译统计信息"""
        self.info(f"Translation: {source_lang} -> {target_lang}, "
                 f"{text_count} texts, {processing_time:.3f}s")


# 全局日志器实例
_logger_instance = None


def get_logger(name: str = "ImageTextTranslator") -> Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = Logger(name)
    return _logger_instance


def init_logger(name: str = "ImageTextTranslator", log_dir: Optional[str] = None) -> Logger:
    """
    初始化日志器
    
    Args:
        name: 日志器名称
        log_dir: 日志目录
        
    Returns:
        日志器实例
    """
    global _logger_instance
    _logger_instance = Logger(name, log_dir)
    return _logger_instance


# 便捷函数
def debug(message: str, *args, **kwargs):
    """调试日志"""
    get_logger().debug(message, *args, **kwargs)


def info(message: str, *args, **kwargs):
    """信息日志"""
    get_logger().info(message, *args, **kwargs)


def warning(message: str, *args, **kwargs):
    """警告日志"""
    get_logger().warning(message, *args, **kwargs)


def error(message: str, *args, **kwargs):
    """错误日志"""
    get_logger().error(message, *args, **kwargs)


def critical(message: str, *args, **kwargs):
    """严重错误日志"""
    get_logger().critical(message, *args, **kwargs)


def exception(message: str, *args, **kwargs):
    """异常日志"""
    get_logger().exception(message, *args, **kwargs)


# 装饰器
def log_function_calls(logger_name: str = "ImageTextTranslator"):
    """
    函数调用日志装饰器
    
    Args:
        logger_name: 日志器名称
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            logger.log_function_call(func.__name__, *args, **kwargs)
            
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(func.__name__, duration)
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"Function {func.__name__} failed after {duration:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator
