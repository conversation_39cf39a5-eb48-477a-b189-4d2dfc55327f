#!/usr/bin/env python3
"""
Paddle OCR适配器 - 将用户的PaddleOCRTool集成到系统中
"""

import os
import sys
from typing import List, Optional, Dict
from dataclasses import dataclass

# 添加utils路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))

from ..utils.paddle_ocr_tool import PaddleOCRTool, OCRResult
from .text_region import TextRegion
from ..utils.logger import get_logger
from ..utils.language_mapping import get_language_mapper


@dataclass
class PaddleOCRConfig:
    """Paddle OCR配置类"""
    lang: str = "en"  # 默认英文
    ocr_version: str = "PP-OCRv5"
    device: str = "cpu"
    use_doc_orientation_classify: bool = False
    use_doc_unwarping: bool = False
    use_textline_orientation: bool = False


class PaddleOCRAdapter:
    """
    Paddle OCR适配器
    将用户的PaddleOCRTool适配到系统的OCR接口
    """
    
    def __init__(self, config: Optional[PaddleOCRConfig] = None):
        """
        初始化Paddle OCR适配器

        Args:
            config: OCR配置，如果为None则使用默认配置
        """
        self.logger = get_logger()
        self.language_mapper = get_language_mapper()

        # 使用默认配置或传入的配置
        self.config = config or PaddleOCRConfig()

        # 验证语言支持
        if not self.language_mapper.is_paddleocr_supported(self.config.lang):
            self.logger.warning(f"不支持的OCR语言: {self.config.lang}，使用默认英语")
            self.config.lang = "en"

        # 获取PaddleOCR语言代码
        self.paddleocr_lang = self.language_mapper.get_paddleocr_code(self.config.lang)

        # 初始化OCR工具
        self.ocr_tool = None

        # 缓存不同语言的OCR工具
        self.ocr_tools: Dict[str, PaddleOCRTool] = {}

        self.logger.info(f"PaddleOCR适配器初始化完成，语言: {self.config.lang} -> {self.paddleocr_lang}")

    @property
    def supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        return [code for code, _ in self.language_mapper.get_supported_ocr_languages()]
    
    def _get_ocr_tool(self, lang: str) -> PaddleOCRTool:
        """
        获取指定语言的OCR工具实例
        
        Args:
            lang: 语言代码
            
        Returns:
            PaddleOCRTool实例
        """
        if lang not in self.ocr_tools:
            self.logger.info(f"正在初始化{lang}语言的Paddle OCR模型...")
            
            # 验证语言支持
            if not self.language_mapper.is_paddleocr_supported(lang):
                self.logger.warning(f"不支持的语言: {lang}，使用默认语言: {self.config.lang}")
                lang = self.config.lang
            
            # 创建OCR工具实例
            ocr_tool = PaddleOCRTool(
                lang=lang,
                ocr_version=self.config.ocr_version,
                device=self.config.device,
                use_doc_orientation_classify=self.config.use_doc_orientation_classify,
                use_doc_unwarping=self.config.use_doc_unwarping,
                use_textline_orientation=self.config.use_textline_orientation
            )
            
            self.ocr_tools[lang] = ocr_tool
            self.logger.info(f"{lang}语言Paddle OCR模型初始化成功")
        
        return self.ocr_tools[lang]
    
    def detect_and_recognize(self, image_path: str, lang: str) -> List[TextRegion]:
        """
        检测和识别图像中的文本
        
        Args:
            image_path: 图像文件路径
            lang: 用户指定的源语言代码
            
        Returns:
            文本区域列表
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
            self.logger.info(f"开始处理图像: {image_path}，语言: {lang}")
            
            # 获取OCR工具
            ocr_tool = self._get_ocr_tool(lang)
            
            # 执行OCR识别
            ocr_result = ocr_tool.recognize(image_path, save_result=False)
            
            # 转换为TextRegion格式
            text_regions = []
            for i, (text, box) in enumerate(zip(ocr_result.texts, ocr_result.boxes)):
                if text and text.strip():  # 过滤空文本
                    # 计算基于文本质量的置信度
                    confidence = self._calculate_text_confidence(text.strip())

                    # 创建文本区域对象
                    region = TextRegion(
                        text=text.strip(),
                        bbox=box,  # box已经是四个顶点的坐标列表
                        confidence=confidence,
                        language=lang
                    )
                    text_regions.append(region)
            
            self.logger.info(f"检测到 {len(text_regions)} 个文本区域")
            
            return text_regions
            
        except Exception as e:
            self.logger.error(f"OCR处理失败: {e}")
            return []

    def _calculate_text_confidence(self, text: str) -> float:
        """
        基于文本质量计算置信度

        Args:
            text: 识别的文本

        Returns:
            置信度分数 (0.0-1.0)
        """
        if not text or not text.strip():
            return 0.0

        text = text.strip()
        base_confidence = 0.8  # 基础置信度

        # 长度因子：合理长度的文本通常更可靠
        length_factor = 0.0
        if 1 <= len(text) <= 100:
            length_factor = 0.1
        elif len(text) > 100:
            length_factor = 0.05
        else:
            length_factor = -0.1

        # 字符质量因子
        quality_factor = 0.0

        # 检查是否包含合理的字符
        reasonable_chars = 0
        total_chars = len(text)

        for char in text:
            if (char.isalnum() or
                char in '.,!?;:()[]{}"\'-—–、。！？；：（）【】「」' or
                '\u4e00' <= char <= '\u9fff' or  # 中文字符
                '\u3040' <= char <= '\u309f' or  # 平假名
                '\u30a0' <= char <= '\u30ff' or  # 片假名
                '\uac00' <= char <= '\ud7af'):   # 韩文字符
                reasonable_chars += 1

        if reasonable_chars / total_chars > 0.8:
            quality_factor += 0.1
        elif reasonable_chars / total_chars < 0.5:
            quality_factor -= 0.2

        # 检查是否有过度重复
        if not self._has_excessive_repetition(text):
            quality_factor += 0.05
        else:
            quality_factor -= 0.2

        # 检查是否包含明显的OCR错误模式
        if not self._has_ocr_error_patterns(text):
            quality_factor += 0.05
        else:
            quality_factor -= 0.1

        # 计算最终置信度
        confidence = base_confidence + length_factor + quality_factor

        # 确保置信度在合理范围内
        return max(0.1, min(1.0, confidence))

    def _has_excessive_repetition(self, text: str) -> bool:
        """检查文本是否有过度重复"""
        if len(text) < 4:
            return False

        # 检查连续重复的字符
        for i in range(len(text) - 2):
            if text[i] == text[i+1] == text[i+2]:
                return True

        return False

    def _has_ocr_error_patterns(self, text: str) -> bool:
        """检查是否包含常见的OCR错误模式"""
        # 常见的OCR错误模式
        error_patterns = [
            '|||',  # 竖线错误
            '___',  # 下划线错误
            '...',  # 过多点号
            '???',  # 问号错误
        ]

        for pattern in error_patterns:
            if pattern in text:
                return True

        # 检查是否主要由特殊字符组成
        special_chars = sum(1 for c in text if not c.isalnum() and c not in ' .,!?;:()[]{}"\'-—–、。！？；：（）【】「」')
        if special_chars / len(text) > 0.5:
            return True

        return False
    
    def get_supported_languages(self) -> Dict[str, str]:
        """
        获取支持的语言列表

        Returns:
            语言代码到语言名称的映射字典
        """
        return {code: zh_name for code, zh_name, _ in self.language_mapper.get_supported_ocr_languages()}
    
    def is_language_supported(self, lang: str) -> bool:
        """
        检查是否支持指定语言

        Args:
            lang: 语言代码

        Returns:
            是否支持
        """
        return self.language_mapper.is_paddleocr_supported(lang)
    
    def get_language_name(self, lang: str) -> str:
        """
        获取语言名称

        Args:
            lang: 语言代码

        Returns:
            语言名称
        """
        lang_info = self.language_mapper.get_language_info(lang)
        return lang_info.get('zh_name', f"未知语言({lang})")


# 全局实例
_paddle_ocr_adapter = None


def get_paddle_ocr_adapter(config: Optional[PaddleOCRConfig] = None) -> PaddleOCRAdapter:
    """
    获取Paddle OCR适配器实例（单例模式）
    
    Args:
        config: Paddle OCR配置
        
    Returns:
        PaddleOCRAdapter实例
    """
    global _paddle_ocr_adapter
    if _paddle_ocr_adapter is None:
        _paddle_ocr_adapter = PaddleOCRAdapter(config)
    return _paddle_ocr_adapter


def set_paddle_ocr_config(config: PaddleOCRConfig):
    """
    设置Paddle OCR配置（重新初始化）
    
    Args:
        config: 新的配置
    """
    global _paddle_ocr_adapter
    _paddle_ocr_adapter = PaddleOCRAdapter(config)


if __name__ == "__main__":
    # 测试代码
    print("=== Paddle OCR适配器测试 ===")
    
    # 创建适配器
    adapter = get_paddle_ocr_adapter()
    
    # 显示支持的语言
    print("支持的语言:")
    for code, name in adapter.get_supported_languages().items():
        print(f"  {code}: {name}")
    
    # 测试OCR识别（如果有测试图像）
    test_image = "test_image.png"
    if os.path.exists(test_image):
        print(f"\n测试图像: {test_image}")
        regions = adapter.detect_and_recognize(test_image, "en")
        print(f"检测到 {len(regions)} 个文本区域:")
        for i, region in enumerate(regions):
            print(f"  {i+1}. '{region.text}' (置信度: {region.confidence})")
    else:
        print(f"\n测试图像不存在: {test_image}")
