#!/usr/bin/env python3
"""
文本处理逻辑模块 - OCR结果解析和文本分块处理

提供OCR结果解析、文本分块、方向检测和排列优化功能
"""

import re
import math
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict

from ..utils.logger import get_logger
from ..utils.config import get_config
from .text_region import TextRegion


@dataclass
class TextBlock:
    """文本块数据类"""
    regions: List[TextRegion]    # 包含的文本区域
    block_type: str              # 块类型 ('line', 'paragraph', 'column')
    direction: str               # 文本方向 ('horizontal', 'vertical')
    reading_order: int           # 阅读顺序
    confidence: float            # 整体置信度
    bbox: List[List[int]]        # 整体边界框
    
    @property
    def text(self) -> str:
        """获取块的完整文本"""
        return ' '.join(region.text for region in self.regions)
    
    @property
    def center(self) -> Tuple[int, int]:
        """获取块的中心点"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        return center_x, center_y


@dataclass
class TextLayout:
    """文本布局数据类"""
    blocks: List[TextBlock]      # 文本块列表
    layout_type: str             # 布局类型 ('single_column', 'multi_column', 'mixed')
    reading_direction: str       # 阅读方向 ('ltr', 'rtl', 'ttb')
    language: str                # 主要语言
    confidence: float            # 布局置信度


class TextProcessor:
    """文本处理器类"""
    
    def __init__(self):
        """初始化文本处理器"""
        self.logger = get_logger()
        self.config = get_config()

        # 从配置文件加载文本处理参数
        text_config = self.config.get_text_processing_config()

        # 文本方向检测参数
        self.horizontal_threshold = text_config.get('horizontal_threshold', 15)
        self.vertical_threshold = text_config.get('vertical_threshold', 75)

        # 文本分块参数
        self.line_spacing_threshold = text_config.get('line_spacing_threshold', 1.5)
        self.paragraph_spacing_threshold = text_config.get('paragraph_spacing_threshold', 2.5)
        self.column_spacing_threshold = text_config.get('column_spacing_threshold', 3.0)

        # 置信度过滤参数
        self.min_confidence = text_config.get('min_confidence', 0.1)

        # 布局分析参数
        self.x_cluster_threshold = text_config.get('x_cluster_threshold', 100)
        self.y_cluster_threshold = text_config.get('y_cluster_threshold', 50)

        # 语言特定的阅读方向
        self.rtl_languages = set(text_config.get('rtl_languages', ['ar', 'he', 'fa', 'ur']))
        self.ttb_languages = set(text_config.get('ttb_languages', ['zh', 'ja', 'ko']))

        self.logger.info("文本处理器初始化完成")
        self.logger.debug(f"文本处理参数: 水平阈值={self.horizontal_threshold}°, 垂直阈值={self.vertical_threshold}°")
    
    def parse_ocr_results(self, text_regions: List[TextRegion]) -> Dict[str, Any]:
        """
        解析OCR结果
        
        Args:
            text_regions: OCR识别的文本区域列表
            
        Returns:
            解析结果字典
        """
        if not text_regions:
            return {
                'total_regions': 0,
                'valid_regions': 0,
                'avg_confidence': 0.0,
                'text_directions': {},
                'languages': {},
                'layout_info': {}
            }
        
        # 过滤有效区域
        valid_regions = [r for r in text_regions if r.text.strip() and r.confidence > self.min_confidence]
        
        # 统计置信度
        confidences = [r.confidence for r in valid_regions]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        # 检测文本方向
        text_directions = self._analyze_text_directions(valid_regions)
        
        # 统计语言
        languages = self._analyze_languages(valid_regions)
        
        # 分析布局
        layout_info = self._analyze_layout(valid_regions)
        
        result = {
            'total_regions': len(text_regions),
            'valid_regions': len(valid_regions),
            'avg_confidence': avg_confidence,
            'text_directions': text_directions,
            'languages': languages,
            'layout_info': layout_info
        }
        
        self.logger.debug(f"OCR结果解析: {result}")
        return result
    
    def _analyze_text_directions(self, regions: List[TextRegion]) -> Dict[str, int]:
        """分析文本方向"""
        directions = {'horizontal': 0, 'vertical': 0, 'unknown': 0}
        
        for region in regions:
            bbox = region.bbox
            if len(bbox) != 4:
                directions['unknown'] += 1
                continue
            
            # 计算文本角度
            # 使用第一个点和第二个点计算角度
            p1, p2 = bbox[0], bbox[1]
            angle = math.degrees(math.atan2(p2[1] - p1[1], p2[0] - p1[0]))
            angle = abs(angle)
            
            if angle <= self.horizontal_threshold or angle >= (180 - self.horizontal_threshold):
                directions['horizontal'] += 1
            elif self.vertical_threshold <= angle <= (180 - self.vertical_threshold):
                directions['vertical'] += 1
            else:
                directions['unknown'] += 1
        
        return directions
    
    def _analyze_languages(self, regions: List[TextRegion]) -> Dict[str, int]:
        """分析语言分布"""
        languages = defaultdict(int)
        
        for region in regions:
            if region.language:
                languages[region.language] += 1
            else:
                languages['unknown'] += 1
        
        return dict(languages)
    
    def _analyze_layout(self, regions: List[TextRegion]) -> Dict[str, Any]:
        """分析文本布局"""
        if not regions:
            return {}
        
        # 计算文本区域的分布
        centers = [region.center for region in regions]
        x_coords = [center[0] for center in centers]
        y_coords = [center[1] for center in centers]
        
        # 分析列数（简单的聚类）
        x_clusters = self._cluster_coordinates(x_coords, threshold=self.x_cluster_threshold)
        y_clusters = self._cluster_coordinates(y_coords, threshold=self.y_cluster_threshold)
        
        layout_info = {
            'estimated_columns': len(x_clusters),
            'estimated_rows': len(y_clusters),
            'x_spread': max(x_coords) - min(x_coords) if x_coords else 0,
            'y_spread': max(y_coords) - min(y_coords) if y_coords else 0,
            'density': len(regions) / ((max(x_coords) - min(x_coords) + 1) * (max(y_coords) - min(y_coords) + 1)) if x_coords and y_coords else 0
        }
        
        return layout_info
    
    def _cluster_coordinates(self, coords: List[int], threshold: int) -> List[List[int]]:
        """简单的坐标聚类"""
        if not coords:
            return []
        
        sorted_coords = sorted(coords)
        clusters = []
        current_cluster = [sorted_coords[0]]
        
        for coord in sorted_coords[1:]:
            if coord - current_cluster[-1] <= threshold:
                current_cluster.append(coord)
            else:
                clusters.append(current_cluster)
                current_cluster = [coord]
        
        clusters.append(current_cluster)
        return clusters
    
    def create_text_blocks(self, text_regions: List[TextRegion]) -> List[TextBlock]:
        """
        创建文本块
        
        Args:
            text_regions: 文本区域列表
            
        Returns:
            文本块列表
        """
        if not text_regions:
            return []
        
        # 按位置排序
        sorted_regions = self._sort_regions_by_reading_order(text_regions)
        
        # 检测行
        lines = self._group_regions_into_lines(sorted_regions)
        
        # 创建文本块
        blocks = []
        for i, line_regions in enumerate(lines):
            if not line_regions:
                continue
            
            # 计算整体边界框
            all_points = []
            for region in line_regions:
                all_points.extend(region.bbox)
            
            x_coords = [p[0] for p in all_points]
            y_coords = [p[1] for p in all_points]
            
            bbox = [
                [min(x_coords), min(y_coords)],
                [max(x_coords), min(y_coords)],
                [max(x_coords), max(y_coords)],
                [min(x_coords), max(y_coords)]
            ]
            
            # 计算平均置信度
            avg_confidence = sum(r.confidence for r in line_regions) / len(line_regions)
            
            # 检测文本方向
            direction = self._detect_block_direction(line_regions)
            
            block = TextBlock(
                regions=line_regions,
                block_type='line',
                direction=direction,
                reading_order=i,
                confidence=avg_confidence,
                bbox=bbox
            )
            
            blocks.append(block)
        
        self.logger.info(f"创建了 {len(blocks)} 个文本块")
        return blocks
    
    def _sort_regions_by_reading_order(self, regions: List[TextRegion]) -> List[TextRegion]:
        """按阅读顺序排序文本区域"""
        # 简单的从上到下、从左到右排序
        return sorted(regions, key=lambda r: (r.center[1], r.center[0]))
    
    def _group_regions_into_lines(self, regions: List[TextRegion]) -> List[List[TextRegion]]:
        """将文本区域分组为行"""
        if not regions:
            return []
        
        lines = []
        current_line = [regions[0]]
        
        for region in regions[1:]:
            # 检查是否在同一行
            last_region = current_line[-1]
            
            # 计算垂直距离
            y_distance = abs(region.center[1] - last_region.center[1])
            avg_height = (region.height + last_region.height) / 2
            
            if y_distance <= avg_height * self.line_spacing_threshold:
                current_line.append(region)
            else:
                # 新行
                lines.append(current_line)
                current_line = [region]
        
        if current_line:
            lines.append(current_line)
        
        # 对每行内的区域按X坐标排序
        for line in lines:
            line.sort(key=lambda r: r.center[0])
        
        return lines
    
    def _detect_block_direction(self, regions: List[TextRegion]) -> str:
        """检测文本块的方向"""
        if not regions:
            return 'horizontal'
        
        # 计算主要方向
        horizontal_count = 0
        vertical_count = 0
        
        for region in regions:
            if region.width > region.height * 1.5:
                horizontal_count += 1
            elif region.height > region.width * 1.5:
                vertical_count += 1
        
        return 'vertical' if vertical_count > horizontal_count else 'horizontal'
    
    def create_text_layout(self, text_blocks: List[TextBlock],
                          dominant_language: str) -> TextLayout:
        """
        创建文本布局

        Args:
            text_blocks: 文本块列表
            dominant_language: 主要语言代码（必须指定）

        Returns:
            文本布局对象

        Raises:
            ValueError: 当未指定语言时
        """
        if not dominant_language or dominant_language.strip() == "":
            raise ValueError("必须指定主要语言代码，不能为空")

        if dominant_language.lower() in ['unknown', 'none', 'null']:
            raise ValueError(f"语言代码不能是无效值: '{dominant_language}'，必须指定具体的语言代码")
        if not text_blocks:
            return TextLayout(
                blocks=[],
                layout_type='empty',
                reading_direction='ltr',
                language=dominant_language,
                confidence=0.0
            )
        
        # 检测布局类型
        layout_type = self._detect_layout_type(text_blocks)
        
        # 检测阅读方向
        reading_direction = self._detect_reading_direction(text_blocks, dominant_language)
        
        # 计算整体置信度
        total_confidence = sum(block.confidence for block in text_blocks)
        avg_confidence = total_confidence / len(text_blocks)
        
        layout = TextLayout(
            blocks=text_blocks,
            layout_type=layout_type,
            reading_direction=reading_direction,
            language=dominant_language,
            confidence=avg_confidence
        )
        
        self.logger.info(f"创建文本布局: {layout_type}, {reading_direction}, 置信度: {avg_confidence:.3f}")
        return layout
    
    def _detect_layout_type(self, blocks: List[TextBlock]) -> str:
        """检测布局类型"""
        if len(blocks) <= 1:
            return 'single_column'
        
        # 分析块的X坐标分布
        x_centers = [block.center[0] for block in blocks]
        x_clusters = self._cluster_coordinates(x_centers, threshold=100)
        
        if len(x_clusters) == 1:
            return 'single_column'
        elif len(x_clusters) == 2:
            return 'two_column'
        else:
            return 'multi_column'
    
    def _detect_reading_direction(self, blocks: List[TextBlock], language: str) -> str:
        """检测阅读方向"""
        # 基于语言的默认方向
        if language in self.rtl_languages:
            return 'rtl'
        elif language in self.ttb_languages:
            # 对于中日韩语言，检查是否为垂直布局
            vertical_blocks = sum(1 for block in blocks if block.direction == 'vertical')
            if vertical_blocks > len(blocks) / 2:
                return 'ttb'
        
        return 'ltr'  # 默认从左到右
    
    def optimize_text_flow(self, layout: TextLayout) -> TextLayout:
        """
        优化文本流
        
        Args:
            layout: 输入布局
            
        Returns:
            优化后的布局
        """
        if not layout.blocks:
            return layout
        
        # 根据阅读方向重新排序块
        if layout.reading_direction == 'rtl':
            # 从右到左
            sorted_blocks = sorted(layout.blocks, key=lambda b: (-b.center[1], -b.center[0]))
        elif layout.reading_direction == 'ttb':
            # 从上到下
            sorted_blocks = sorted(layout.blocks, key=lambda b: (b.center[0], b.center[1]))
        else:
            # 从左到右（默认）
            sorted_blocks = sorted(layout.blocks, key=lambda b: (b.center[1], b.center[0]))
        
        # 更新阅读顺序
        for i, block in enumerate(sorted_blocks):
            block.reading_order = i
        
        optimized_layout = TextLayout(
            blocks=sorted_blocks,
            layout_type=layout.layout_type,
            reading_direction=layout.reading_direction,
            language=layout.language,
            confidence=layout.confidence
        )
        
        self.logger.info(f"文本流优化完成，重新排序了 {len(sorted_blocks)} 个块")
        return optimized_layout
    
    def extract_text_content(self, layout: TextLayout, 
                           preserve_structure: bool = True) -> str:
        """
        提取文本内容
        
        Args:
            layout: 文本布局
            preserve_structure: 是否保持结构
            
        Returns:
            提取的文本内容
        """
        if not layout.blocks:
            return ""
        
        if preserve_structure:
            # 保持块结构
            lines = []
            for block in layout.blocks:
                block_text = block.text.strip()
                if block_text:
                    lines.append(block_text)
            return '\n'.join(lines)
        else:
            # 简单连接
            texts = []
            for block in layout.blocks:
                block_text = block.text.strip()
                if block_text:
                    texts.append(block_text)
            return ' '.join(texts)
    
    def is_available(self) -> bool:
        """检查文本处理器是否可用"""
        return True
    
    def get_processor_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            "horizontal_threshold": self.horizontal_threshold,
            "vertical_threshold": self.vertical_threshold,
            "line_spacing_threshold": self.line_spacing_threshold,
            "paragraph_spacing_threshold": self.paragraph_spacing_threshold,
            "column_spacing_threshold": self.column_spacing_threshold,
            "supported_directions": ['ltr', 'rtl', 'ttb'],
            "available": self.is_available()
        }


# 全局文本处理器实例
_text_processor = None


def get_text_processor() -> TextProcessor:
    """获取全局文本处理器实例"""
    global _text_processor
    if _text_processor is None:
        _text_processor = TextProcessor()
    return _text_processor
