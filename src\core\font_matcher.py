#!/usr/bin/env python3
"""
字体匹配系统模块 - 字体检测和相似度匹配

提供字体检测、相似度匹配和字体库管理功能
"""

import os
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import json

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import get_config
from .text_region import TextRegion

# 导入新的字体管理系统
try:
    from .font_management import get_font_manager
    NEW_FONT_SYSTEM_AVAILABLE = True
except ImportError:
    NEW_FONT_SYSTEM_AVAILABLE = False


@dataclass
class FontInfo:
    """字体信息数据类"""
    name: str                    # 字体名称
    path: str                    # 字体文件路径
    family: str                  # 字体族
    style: str                   # 字体样式 (regular, bold, italic, etc.)
    size: int                    # 字体大小
    weight: str = 'normal'       # 字体粗细
    is_monospace: bool = False   # 是否等宽字体
    supports_unicode: bool = True # 是否支持Unicode
    language_support: List[str] = None  # 支持的语言列表


@dataclass
class FontMatchResult:
    """字体匹配结果"""
    font_info: FontInfo          # 匹配的字体信息
    similarity_score: float      # 相似度分数 (0-1)
    confidence: float           # 匹配置信度
    match_reason: str           # 匹配原因


class FontMatcher:
    """字体匹配器类"""
    
    def __init__(self):
        """初始化字体匹配器"""
        self.logger = get_logger()
        self.config = get_config()
        
        # 字体库路径
        self.font_cache_dir = Path("cache/fonts")
        self.font_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 系统字体路径
        self.system_font_paths = self._get_system_font_paths()
        
        # 字体缓存
        self.font_cache = {}
        self.font_database = {}
        
        # 字体特征提取参数
        self.sample_text = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        self.sample_size = (200, 50)
        self.font_size = 24
        
        # 加载字体数据库
        self._load_font_database()
        
        self.logger.info("字体匹配器初始化完成")
    
    def _get_system_font_paths(self) -> List[str]:
        """获取系统字体路径"""
        # 从配置文件获取系统字体路径
        font_paths = self.config.get_system_font_paths()

        # 添加用户特定路径（Windows）
        if os.name == 'nt':
            user_font_path = f"C:/Users/<USER>'USERNAME', '')}/AppData/Local/Microsoft/Windows/Fonts"
            if user_font_path not in font_paths:
                font_paths.append(user_font_path)

        # 过滤存在的路径
        existing_paths = []
        for path in font_paths:
            expanded_path = os.path.expanduser(path)
            if os.path.exists(expanded_path):
                existing_paths.append(expanded_path)

        self.logger.debug(f"找到系统字体路径: {existing_paths}")
        return existing_paths
    
    def _load_font_database(self):
        """加载字体数据库"""
        database_file = self.font_cache_dir / "font_database.json"
        
        if database_file.exists():
            try:
                with open(database_file, 'r', encoding='utf-8') as f:
                    self.font_database = json.load(f)
                self.logger.info(f"加载字体数据库: {len(self.font_database)} 个字体")
            except Exception as e:
                self.logger.error(f"加载字体数据库失败: {e}")
                self.font_database = {}
        else:
            self.logger.info("字体数据库不存在，将创建新的数据库")
            self._scan_system_fonts()
    
    def _save_font_database(self):
        """保存字体数据库"""
        database_file = self.font_cache_dir / "font_database.json"
        
        try:
            with open(database_file, 'w', encoding='utf-8') as f:
                json.dump(self.font_database, f, ensure_ascii=False, indent=2)
            self.logger.info(f"保存字体数据库: {len(self.font_database)} 个字体")
        except Exception as e:
            self.logger.error(f"保存字体数据库失败: {e}")
    
    def _scan_system_fonts(self):
        """扫描系统字体"""
        if not PIL_AVAILABLE:
            self.logger.warning("PIL不可用，无法扫描系统字体")
            return
        
        self.logger.info("开始扫描系统字体...")
        font_count = 0
        
        for font_dir in self.system_font_paths:
            if not os.path.exists(font_dir):
                continue
            
            for root, dirs, files in os.walk(font_dir):
                for file in files:
                    if file.lower().endswith(('.ttf', '.otf', '.ttc')):
                        font_path = os.path.join(root, file)
                        try:
                            font_info = self._analyze_font(font_path)
                            if font_info:
                                self.font_database[font_info.name] = {
                                    'path': font_path,
                                    'family': font_info.family,
                                    'style': font_info.style,
                                    'weight': font_info.weight,
                                    'is_monospace': font_info.is_monospace,
                                    'supports_unicode': font_info.supports_unicode
                                }
                                font_count += 1
                        except Exception as e:
                            self.logger.debug(f"分析字体失败 {font_path}: {e}")
        
        self.logger.info(f"扫描完成，发现 {font_count} 个字体")
        self._save_font_database()
    
    def _analyze_font(self, font_path: str) -> Optional[FontInfo]:
        """分析字体文件"""
        try:
            # 尝试加载字体
            font = ImageFont.truetype(font_path, self.font_size)
            
            # 获取字体名称
            font_name = os.path.splitext(os.path.basename(font_path))[0]
            
            # 简单的字体属性检测
            family = font_name.split('-')[0] if '-' in font_name else font_name
            style = 'regular'
            weight = 'normal'
            
            # 检测样式
            name_lower = font_name.lower()
            if 'bold' in name_lower:
                weight = 'bold'
                style = 'bold'
            if 'italic' in name_lower or 'oblique' in name_lower:
                style = 'italic' if style == 'regular' else 'bold-italic'
            
            # 检测是否为等宽字体
            is_monospace = self._is_monospace_font(font)
            
            return FontInfo(
                name=font_name,
                path=font_path,
                family=family,
                style=style,
                size=self.font_size,
                weight=weight,
                is_monospace=is_monospace,
                supports_unicode=True  # 假设支持Unicode
            )
            
        except Exception as e:
            self.logger.debug(f"分析字体失败 {font_path}: {e}")
            return None
    
    def _is_monospace_font(self, font: ImageFont.FreeTypeFont) -> bool:
        """检测是否为等宽字体"""
        try:
            # 测试几个字符的宽度
            test_chars = ['i', 'l', 'W', 'M', '1', '0']
            widths = []
            
            for char in test_chars:
                bbox = font.getbbox(char)
                width = bbox[2] - bbox[0]
                widths.append(width)
            
            # 如果所有字符宽度相同，则为等宽字体
            return len(set(widths)) == 1
            
        except Exception:
            return False
    
    def extract_font_features(self, text_region: TextRegion, image: np.ndarray) -> Dict[str, Any]:
        """
        从文本区域提取字体特征
        
        Args:
            text_region: 文本区域
            image: 原始图像
            
        Returns:
            字体特征字典
        """
        try:
            # 提取文本区域图像
            bbox = text_region.bbox
            if len(bbox) != 4:
                return {}
            
            # 计算边界框
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            # 确保坐标在图像范围内
            height, width = image.shape[:2]
            x_min = max(0, min(x_min, width - 1))
            x_max = max(0, min(x_max, width - 1))
            y_min = max(0, min(y_min, height - 1))
            y_max = max(0, min(y_max, height - 1))
            
            if x_max <= x_min or y_max <= y_min:
                return {}
            
            # 提取区域
            region_image = image[y_min:y_max, x_min:x_max]
            
            # 转换为灰度图像
            if len(region_image.shape) == 3:
                gray = cv2.cvtColor(region_image, cv2.COLOR_RGB2GRAY)
            else:
                gray = region_image
            
            # 计算字体特征
            features = {
                'height': text_region.height,
                'width': text_region.width,
                'aspect_ratio': text_region.width / text_region.height if text_region.height > 0 else 0,
                'area': text_region.area,
                'text_length': len(text_region.text),
                'char_width': text_region.width / len(text_region.text) if len(text_region.text) > 0 else 0,
                'estimated_font_size': text_region.height,  # 简单估计
                'is_bold': self._detect_bold_text(gray),
                'is_italic': self._detect_italic_text(gray),
                'stroke_width': self._estimate_stroke_width(gray)
            }
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取字体特征失败: {e}")
            return {}
    
    def _detect_bold_text(self, gray_image: np.ndarray) -> bool:
        """检测是否为粗体文本"""
        try:
            # 计算边缘密度
            edges = cv2.Canny(gray_image, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # 粗体文本通常有更高的边缘密度
            return edge_density > 0.1
            
        except Exception:
            return False
    
    def _detect_italic_text(self, gray_image: np.ndarray) -> bool:
        """检测是否为斜体文本"""
        try:
            # 使用霍夫变换检测倾斜线条
            edges = cv2.Canny(gray_image, 50, 150)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=20)
            
            if lines is not None:
                angles = []
                for line in lines:
                    rho, theta = line[0]
                    angle = np.degrees(theta)
                    angles.append(angle)
                
                # 检查是否有倾斜角度
                avg_angle = np.mean(angles)
                return abs(avg_angle - 90) > 10  # 偏离垂直超过10度
            
            return False
            
        except Exception:
            return False
    
    def _estimate_stroke_width(self, gray_image: np.ndarray) -> float:
        """估计笔画宽度"""
        try:
            # 使用形态学操作估计笔画宽度
            kernel_sizes = range(1, 10)
            best_score = 0
            best_width = 1
            
            for size in kernel_sizes:
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (size, size))
                opened = cv2.morphologyEx(gray_image, cv2.MORPH_OPEN, kernel)
                score = np.sum(opened > 0) / np.sum(gray_image > 0) if np.sum(gray_image > 0) > 0 else 0
                
                if score > best_score:
                    best_score = score
                    best_width = size
            
            return float(best_width)
            
        except Exception:
            return 1.0
    
    def find_matching_fonts(self, features: Dict[str, Any],
                          language: str) -> List[FontMatchResult]:
        """
        查找匹配的字体

        Args:
            features: 字体特征
            language: 目标语言代码（必须指定）

        Returns:
            匹配结果列表，按相似度排序

        Raises:
            ValueError: 当未指定语言时
        """
        if not language or language.strip() == "":
            raise ValueError("必须指定目标语言代码，不能为空")

        if language.lower() in ['unknown', 'none', 'null']:
            raise ValueError(f"语言代码不能是无效值: '{language}'，必须指定具体的语言代码")

        if not self.font_database:
            self.logger.warning("字体数据库为空")
            return []
        
        matches = []
        
        for font_name, font_data in self.font_database.items():
            try:
                # 计算相似度分数
                similarity = self._calculate_font_similarity(features, font_data)
                
                # 语言支持检查
                language_bonus = 0.1 if self._supports_language(font_data, language) else 0
                
                # 创建字体信息
                font_info = FontInfo(
                    name=font_name,
                    path=font_data['path'],
                    family=font_data['family'],
                    style=font_data['style'],
                    size=int(features.get('estimated_font_size', 24)),
                    weight=font_data['weight'],
                    is_monospace=font_data['is_monospace'],
                    supports_unicode=font_data['supports_unicode']
                )
                
                # 创建匹配结果
                match_result = FontMatchResult(
                    font_info=font_info,
                    similarity_score=similarity + language_bonus,
                    confidence=min(similarity + language_bonus, 1.0),
                    match_reason=self._get_match_reason(features, font_data)
                )
                
                matches.append(match_result)
                
            except Exception as e:
                self.logger.debug(f"匹配字体失败 {font_name}: {e}")
        
        # 按相似度排序
        matches.sort(key=lambda x: x.similarity_score, reverse=True)
        
        return matches[:10]  # 返回前10个匹配结果
    
    def _calculate_font_similarity(self, features: Dict[str, Any], 
                                 font_data: Dict[str, Any]) -> float:
        """计算字体相似度"""
        score = 0.0
        
        # 样式匹配
        if features.get('is_bold', False) and font_data['weight'] == 'bold':
            score += 0.3
        elif not features.get('is_bold', False) and font_data['weight'] == 'normal':
            score += 0.2
        
        # 斜体匹配
        if features.get('is_italic', False) and 'italic' in font_data['style']:
            score += 0.3
        elif not features.get('is_italic', False) and 'italic' not in font_data['style']:
            score += 0.2
        
        # 等宽字体匹配
        estimated_monospace = features.get('char_width', 0) > 0 and abs(features.get('aspect_ratio', 1) - 0.6) < 0.2
        if estimated_monospace == font_data['is_monospace']:
            score += 0.2
        
        return min(score, 1.0)
    
    def _supports_language(self, font_data: Dict[str, Any], language: str) -> bool:
        """检查字体是否支持指定语言"""
        # 简单的语言支持检查
        if language in ['zh', 'ja', 'ko']:
            # 中日韩语言需要特殊字体
            font_name = font_data.get('family', '').lower()
            cjk_fonts = ['noto', 'source', 'microsoft', 'simhei', 'simsun', 'mingliu', 'meiryo', 'malgun']
            return any(cjk_font in font_name for cjk_font in cjk_fonts)
        
        return font_data.get('supports_unicode', True)
    
    def _get_match_reason(self, features: Dict[str, Any], font_data: Dict[str, Any]) -> str:
        """获取匹配原因"""
        reasons = []
        
        if features.get('is_bold', False) and font_data['weight'] == 'bold':
            reasons.append("粗体匹配")
        
        if features.get('is_italic', False) and 'italic' in font_data['style']:
            reasons.append("斜体匹配")
        
        if font_data['is_monospace']:
            reasons.append("等宽字体")
        
        if not reasons:
            reasons.append("基础匹配")
        
        return ", ".join(reasons)
    
    def get_default_font(self, language: str) -> Optional[FontInfo]:
        """
        获取默认字体

        Args:
            language: 目标语言代码（必须指定）

        Returns:
            默认字体信息，如果找不到则返回None

        Raises:
            ValueError: 当未指定语言时
        """
        if not language or language.strip() == "":
            raise ValueError("必须指定目标语言代码，不能为空")

        if language.lower() in ['unknown', 'none', 'null']:
            raise ValueError(f"语言代码不能是无效值: '{language}'，必须指定具体的语言代码")

        # 根据语言选择默认字体
        default_fonts = {
            'en': ['Arial', 'Helvetica', 'Times New Roman', 'Calibri'],
            'zh': ['SimHei', 'SimSun', 'Microsoft YaHei', 'Noto Sans CJK'],
            'ja': ['Meiryo', 'MS Gothic', 'Noto Sans CJK JP'],
            'ko': ['Malgun Gothic', 'Dotum', 'Noto Sans CJK KR'],
            'ar': ['Arial Unicode MS', 'Tahoma', 'Noto Sans Arabic'],
        }
        
        candidates = default_fonts.get(language)
        if candidates is None:
            self.logger.warning(f"不支持的语言: {language}，无法获取默认字体")
            return None
        
        for font_name in candidates:
            for db_name, font_data in self.font_database.items():
                if font_name.lower() in db_name.lower():
                    return FontInfo(
                        name=db_name,
                        path=font_data['path'],
                        family=font_data['family'],
                        style=font_data['style'],
                        size=24,
                        weight=font_data['weight'],
                        is_monospace=font_data['is_monospace'],
                        supports_unicode=font_data['supports_unicode']
                    )
        
        # 如果没有找到，返回第一个可用字体
        if self.font_database:
            first_font = next(iter(self.font_database.items()))
            font_name, font_data = first_font
            return FontInfo(
                name=font_name,
                path=font_data['path'],
                family=font_data['family'],
                style=font_data['style'],
                size=24,
                weight=font_data['weight'],
                is_monospace=font_data['is_monospace'],
                supports_unicode=font_data['supports_unicode']
            )
        
        return None
    
    def refresh_font_database(self):
        """刷新字体数据库"""
        self.font_database = {}
        self._scan_system_fonts()
    
    def get_font_count(self) -> int:
        """获取字体数量"""
        return len(self.font_database)
    
    def is_available(self) -> bool:
        """检查字体匹配器是否可用"""
        return PIL_AVAILABLE
    
    def get_matcher_info(self) -> Dict[str, Any]:
        """获取匹配器信息"""
        return {
            "pil_available": PIL_AVAILABLE,
            "font_count": len(self.font_database),
            "system_font_paths": self.system_font_paths,
            "cache_dir": str(self.font_cache_dir),
            "available": self.is_available(),
            "new_font_system": NEW_FONT_SYSTEM_AVAILABLE
        }

    def get_font_for_language_v2(self, language_code: str, auto_download: bool = True) -> Optional[FontInfo]:
        """
        使用新字体管理系统获取Noto语言字体 (严格Noto字体依赖)

        Args:
            language_code: NLLB语言代码 (如 'zho_Hans', 'ara_Arab')
            auto_download: 是否自动下载缺失的字体

        Returns:
            Noto字体信息，如果无法获取则返回None (内部处理所有异常)
        """
        if not NEW_FONT_SYSTEM_AVAILABLE:
            self.logger.error("新字体管理系统不可用，无法获取Noto字体")
            return None

        try:
            font_manager = get_font_manager()
            new_font_info = font_manager.get_font_for_language(language_code, auto_download)

            if new_font_info:
                # 转换为兼容的FontInfo格式
                return FontInfo(
                    name=new_font_info.name,
                    path=new_font_info.path,
                    family=new_font_info.family,
                    style=new_font_info.style,
                    size=new_font_info.size,
                    weight=new_font_info.weight,
                    is_monospace=new_font_info.is_monospace,
                    supports_unicode=new_font_info.supports_unicode,
                    language_support=[language_code]
                )
            else:
                self.logger.warning(f"无法获取语言 {language_code} 的Noto字体")
                return None

        except Exception as e:
            self.logger.error(f"新字体管理系统获取Noto字体失败: {e}")
            return None

    def get_font_for_text_v2(self, text: str, auto_download: bool = True) -> Optional[FontInfo]:
        """
        使用新字体管理系统根据文本内容获取Noto字体 (严格Noto字体依赖)

        Args:
            text: 要渲染的文本
            auto_download: 是否自动下载缺失的字体

        Returns:
            Noto字体信息，如果无法获取则返回None (内部处理所有异常)
        """
        if not NEW_FONT_SYSTEM_AVAILABLE:
            self.logger.error("新字体管理系统不可用，无法获取Noto字体")
            return None

        try:
            font_manager = get_font_manager()
            new_font_info = font_manager.get_font_for_text(text, auto_download)

            if new_font_info:
                # 转换为兼容的FontInfo格式
                return FontInfo(
                    name=new_font_info.name,
                    path=new_font_info.path,
                    family=new_font_info.family,
                    style=new_font_info.style,
                    size=new_font_info.size,
                    weight=new_font_info.weight,
                    is_monospace=new_font_info.is_monospace,
                    supports_unicode=new_font_info.supports_unicode,
                    language_support=[]
                )
            else:
                self.logger.warning(f"无法为文本获取Noto字体: {text[:50]}...")
                return None

        except Exception as e:
            self.logger.error(f"新字体管理系统获取Noto字体失败: {e}")
            return None

    def is_language_supported_v2(self, language_code: str) -> bool:
        """
        检查新字体管理系统是否支持指定语言

        Args:
            language_code: NLLB语言代码

        Returns:
            是否支持该语言 (内部处理所有异常)
        """
        if not NEW_FONT_SYSTEM_AVAILABLE:
            self.logger.warning("新字体管理系统不可用，无法检查语言支持")
            return False

        try:
            font_manager = get_font_manager()
            return font_manager.is_language_supported(language_code)
        except Exception as e:
            self.logger.error(f"检查语言支持失败: {e}")
            return False


# 全局字体匹配器实例
_font_matcher = None


def get_font_matcher() -> FontMatcher:
    """获取全局字体匹配器实例"""
    global _font_matcher
    if _font_matcher is None:
        _font_matcher = FontMatcher()
    return _font_matcher
