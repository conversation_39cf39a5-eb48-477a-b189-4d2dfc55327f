#!/usr/bin/env python3
"""
快速打包脚本 - 简化版本，用于快速测试和打包
"""

import os
import sys
import platform
import subprocess
from pathlib import Path

def detect_platform():
    """检测平台"""
    system = platform.system().lower()
    if system == 'windows':
        return 'windows'
    elif system == 'darwin':
        return 'macos'
    else:
        return 'linux'

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyInstaller已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ PyInstaller未正确安装")
            return False
    except Exception as e:
        print(f"❌ 检查PyInstaller时出错: {e}")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], 
                      check=True)
        print("✅ PyInstaller安装完成")
        return True
    except Exception as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

# 注意：现在使用直接命令行打包，不再需要spec文件生成函数

def get_all_dependencies():
    """从 pyproject.toml 获取所有依赖包名"""
    try:
        import tomllib  # Python 3.11+
    except ImportError:
        try:
            import tomli as tomllib  # fallback
        except ImportError:
            print("⚠️ 无法导入 TOML 解析器，使用手动依赖列表")
            return get_manual_dependencies()

    project_root = Path(__file__).parent
    pyproject_file = project_root / "pyproject.toml"

    if not pyproject_file.exists():
        return []

    try:
        with open(pyproject_file, 'rb') as f:
            data = tomllib.load(f)

        dependencies = []

        # 获取主要依赖
        if 'project' in data and 'dependencies' in data['project']:
            for dep in data['project']['dependencies']:
                # 提取包名（去掉版本号等）
                pkg_name = dep.split('>=')[0].split('==')[0].split('~=')[0].split('>')[0].split('<')[0].split('!')[0].strip()
                dependencies.append(pkg_name)

        # 获取可选依赖
        if 'project' in data and 'optional-dependencies' in data['project']:
            for group, deps in data['project']['optional-dependencies'].items():
                for dep in deps:
                    pkg_name = dep.split('>=')[0].split('==')[0].split('~=')[0].split('>')[0].split('<')[0].split('!')[0].strip()
                    dependencies.append(pkg_name)

        # 去重并过滤掉一些不需要的包
        exclude_packages = {'pytest', 'black', 'isort', 'flake8', 'mypy', 'coverage'}
        dependencies = list(set(dependencies) - exclude_packages)

        print(f"📦 从 pyproject.toml 发现 {len(dependencies)} 个依赖包")
        return dependencies

    except Exception as e:
        print(f"⚠️ 解析 pyproject.toml 失败: {e}")
        return get_manual_dependencies()

def check_pytorch_compatibility(platform_name):
    """检查 PyTorch 跨平台兼容性"""
    print(f"🔍 检查 PyTorch 平台兼容性 ({platform_name})...")

    try:
        import torch
        torch_version = torch.__version__
        has_cuda = torch.cuda.is_available()

        print(f"📦 当前 PyTorch 版本: {torch_version}")

        if platform_name == 'macos':
            if 'cu' in torch_version.lower():
                print("⚠️ 警告：在 Mac 上检测到 CUDA 版本的 PyTorch")
                print("💡 建议：确保在 Mac 上使用 CPU 版本的 PyTorch")
            else:
                print("✅ 正确：在 Mac 上使用 CPU 版本的 PyTorch")
        elif platform_name == 'windows':
            if has_cuda:
                print("✅ 正确：在 Windows 上使用 CUDA 版本的 PyTorch")
            else:
                print("⚠️ 注意：在 Windows 上使用 CPU 版本的 PyTorch（可能影响性能）")

        return True

    except ImportError:
        print("❌ 错误：无法导入 PyTorch")
        return False
    except Exception as e:
        print(f"⚠️ PyTorch 兼容性检查失败: {e}")
        return True  # 不阻止打包继续

def get_manual_dependencies():
    """手动维护的关键依赖列表"""
    return [
        # 核心依赖
        'paddlex', 'paddleocr', 'paddlepaddle',
        'PyQt6', 'torch', 'torchvision', 'torchaudio', 'transformers',
        'numpy', 'pillow', 'opencv-contrib-python',
        # 新发现的依赖
        'pypdfium2', 'shapely', 'pyclipper', 'cv2',
        # 其他重要依赖
        'requests', 'urllib3', 'certifi', 'charset-normalizer',
        'huggingface-hub', 'safetensors', 'tokenizers',
        'scipy', 'pandas', 'scikit-learn',
        'langchain', 'langchain-core', 'langchain-openai', 'langchain-community',
        'openai', 'tiktoken', 'pydantic', 'pydantic-core',
        'yaml', 'ruamel.yaml', 'jinja2', 'markupsafe',
        'tqdm', 'psutil', 'packaging', 'filelock',
        'regex', 'ftfy', 'einops', 'accelerate',
        'aiohttp', 'multidict', 'yarl', 'frozenlist',
        'lxml', 'beautifulsoup4', 'soupsieve',
        'orjson', 'ujson', 'zstandard',
    ]

def run_packaging_direct(platform_name):
    """直接使用命令行参数运行打包（不使用spec文件）"""
    print("🔨 开始打包...")
    try:
        project_root = Path(__file__).parent
        main_py = str(project_root / "src" / "main.py")
        assets_path = str(project_root / "src" / "assets")

        # 检查 PyTorch 兼容性
        check_pytorch_compatibility(platform_name)

        # 获取所有依赖
        all_dependencies = get_all_dependencies()

        # 根据平台选择路径分隔符
        path_sep = ';' if platform_name == 'windows' else ':'

        # 构建基础命令
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            main_py,
            '--name', 'ImageTextTranslator',
            '--onedir',  # 创建目录而不是单文件
            '--clean',
            '--noconfirm',  # 自动确认覆盖
            # 数据文件
            '--add-data', f'{assets_path}{path_sep}src/assets',
        ]

        # 为所有依赖添加收集参数
        for dep in all_dependencies:
            # 跳过一些特殊包和测试包
            skip_packages = {
                'python', 'pip', 'setuptools', 'wheel',
                'pytest', 'pytest-mock', 'pytest-cov', 'pytest-asyncio',
                'black', 'isort', 'flake8', 'mypy', 'coverage',
                'PyQt6-Qt6', 'PyQt6-sip',  # 这些是子包，会被 PyQt6 包含
            }

            if dep in skip_packages:
                print(f"  ⏭️ 跳过包: {dep}")
                continue

            # 包名映射（处理命名不一致的情况）
            package_mapping = {
                'opencv-contrib-python': 'cv2',
                'pillow': 'PIL',
                'PyYAML': 'yaml',
                'beautifulsoup4': 'bs4',
                'python-dotenv': 'dotenv',
                'scikit-learn': 'sklearn',
                'typing-extensions': 'typing_extensions',
            }

            # Mac 特定的包映射
            if platform_name == 'macos':
                # Mac 上可能需要特殊处理的包
                mac_specific_mapping = {
                    # 如果发现 Mac 特定的包名问题，在这里添加
                }
                package_mapping.update(mac_specific_mapping)

            actual_package = package_mapping.get(dep, dep)

            try:
                # 只添加基本的收集参数，避免元数据问题
                cmd.extend(['--collect-all', actual_package])
                cmd.extend(['--hidden-import', actual_package])
                print(f"  📦 添加依赖: {dep} -> {actual_package}")
            except Exception as e:
                print(f"  ⚠️ 跳过依赖: {dep} ({e})")

        # 添加关键包的元数据和数据收集
        critical_packages = [
            'paddlex', 'paddleocr', 'pypdfium2', 'shapely', 'pyclipper', 'transformers'
        ]

        # PyTorch 特殊处理（确保跨平台兼容性）
        pytorch_packages = ['torch', 'torchvision', 'torchaudio']
        for pkg in pytorch_packages:
            try:
                cmd.extend(['--collect-all', pkg])
                cmd.extend(['--collect-data', pkg])
                cmd.extend(['--copy-metadata', pkg])
                cmd.extend(['--hidden-import', pkg])
                print(f"  🔥 添加 PyTorch 包: {pkg}")
            except Exception as e:
                print(f"  ⚠️ PyTorch 包收集失败: {pkg} ({e})")

        for pkg in critical_packages:
            try:
                cmd.extend(['--collect-data', pkg])
                cmd.extend(['--copy-metadata', pkg])
                print(f"  🔑 添加关键包数据: {pkg}")
            except Exception as e:
                print(f"  ⚠️ 关键包数据收集失败: {pkg} ({e})")

        # 添加特殊的隐藏导入
        special_imports = [
            'PyQt6.QtCore',
            'PyQt6.QtGui',
            'PyQt6.QtWidgets',
            'paddleocr._pipelines',
            'paddleocr._pipelines.ocr',
            'paddleocr._pipelines.base',
        ]

        for imp in special_imports:
            cmd.extend(['--hidden-import', imp])

        # 添加 PaddlePaddle 动态库收集（跨平台）
        print("🔧 添加 PaddlePaddle 动态库收集...")
        try:
            import paddle
            paddle_path = paddle.__file__.replace('__init__.py', '')

            # 查找 MKL 相关的动态库文件
            import glob
            import os

            # 根据平台选择动态库扩展名和分隔符
            if platform_name == 'windows':
                lib_ext = 'dll'
                path_sep = ';'
                lib_patterns = [
                    f'mklml*.{lib_ext}',
                    f'libiomp5md.{lib_ext}',
                    f'mkldnn.{lib_ext}',
                    f'paddle_inference.{lib_ext}',
                    f'common.{lib_ext}',
                ]
            else:  # macOS
                lib_ext = 'dylib'
                path_sep = ':'
                lib_patterns = [
                    f'libmklml*.{lib_ext}',
                    f'libiomp5.{lib_ext}',
                    f'libmkldnn*.{lib_ext}',
                    f'libpaddle_inference.{lib_ext}',
                    f'libcommon.{lib_ext}',
                ]

            # 搜索动态库文件
            for lib_pattern in lib_patterns:
                full_pattern = os.path.join(paddle_path, '**', lib_pattern)
                lib_files = glob.glob(full_pattern, recursive=True)
                for lib_file in lib_files:
                    cmd.extend(['--add-binary', f'{lib_file}{path_sep}.'])
                    print(f"  🔗 添加动态库: {os.path.basename(lib_file)}")

        except Exception as e:
            print(f"  ⚠️ 动态库收集失败: {e}")

        # 控制台设置
        if platform_name != 'windows':
            cmd.append('--noconsole')
        else:
            cmd.append('--console')

        subprocess.run(cmd, check=True)
        print("✅ 打包完成")
        return True
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        return False

def copy_external_files():
    """创建完整的外部文件目录结构（基于config.yaml配置）"""
    print("📁 创建外部文件目录结构...")
    project_root = Path(__file__).parent
    dist_dir = project_root / 'dist'
    exe_dir = dist_dir / 'ImageTextTranslator'

    try:
        import shutil

        # 复制配置文件到可执行文件目录
        config_source = project_root / 'config.yaml'
        config_target = exe_dir / 'config.yaml'  # 复制到exe同目录
        if config_source.exists():
            shutil.copy2(config_source, config_target)
            print("✅ 复制配置文件到可执行文件目录: config.yaml")

        # 基于config.yaml创建完整的目录结构
        directories = [
            # 主要目录
            'models',
            'models/compression',
            'models/translate',
            'fonts',
            'cache',
            'cache/fonts',
            'cache/paddleocr',      # PaddleOCR缓存目录
            'cache/paddlex',        # PaddleX缓存目录
            'cache/test_outputs',
            'output',
            'temp'
        ]

        for dir_name in directories:
            target = exe_dir / dir_name  # 修改：在exe目录下创建
            target.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录结构: {dir_name}")

        print("✅ 完整目录结构创建完成")
        return True
    except Exception as e:
        print(f"❌ 创建目录结构失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Image Text Translator 快速打包工具")
    print("=" * 50)
    
    # 检测平台
    platform_name = detect_platform()
    print(f"🖥️ 检测到平台: {platform_name}")
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return 1
    
    # 直接运行打包（不使用spec文件）
    print("📝 准备打包配置...")
    if not run_packaging_direct(platform_name):
        return 1

    # 复制外部文件
    if not copy_external_files():
        return 1

    # 清理build目录
    build_dir = Path(__file__).parent / 'build'
    if build_dir.exists():
        import shutil
        shutil.rmtree(build_dir)
    
    print("\n🎉 打包完成！")
    print(f"📦 结果位于: {Path(__file__).parent / 'dist'}")
    print("\n📋 使用说明:")
    print("1. 打包结果只包含可执行文件和基本目录结构")
    print("2. 程序运行时会自动访问原项目位置的数据文件")
    print("3. 如需完全独立部署，请手动复制所需的模型和字体文件")
    print("4. 运行ImageTextTranslator可执行文件即可")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
