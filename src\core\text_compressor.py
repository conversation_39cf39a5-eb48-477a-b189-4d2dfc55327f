#!/usr/bin/env python3
"""
文本压缩模块 - 基于大语言模型的智能文本压缩

当翻译后的文本过长时，使用大模型进行语义压缩，保持原意的同时简化表达
"""

import os
import time
import re
import requests
import socket
import platform
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

try:
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from transformers.utils import logging as transformers_logging
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import get_config
from ..utils.device_manager import get_device_manager
from .text_region import TextRegion
from .llm_backends import BaseLLMCompressor, LocalLLMCompressor, APILLMCompressor


@dataclass
class CompressionResult:
    """文本压缩结果数据类"""
    original_text: str         # 原始文本
    compressed_text: str       # 压缩后文本
    compression_ratio: float   # 压缩比例 (压缩后长度/原始长度)
    processing_time: float     # 处理时间
    success: bool = True       # 是否成功


class TextCompressor:
    """文本压缩器类 - 支持本地模型和API调用"""

    def __init__(self, model_name: Optional[str] = None):
        """
        初始化文本压缩器

        Args:
            model_name: 模型名称，用于本地模型。API模式时可为None
        """
        self.logger = get_logger()
        self.config = get_config()

        # 获取压缩模型配置
        compression_config = self.config.get('models.compression', {})
        compression_type = compression_config.get('type', 'local')

        # 从配置文件加载压缩策略
        compression_strategies = self.config.get_compression_strategies()

        # 根据配置类型初始化后端
        if compression_type == 'api':
            # API模式
            self.backend = APILLMCompressor({
                'models': self.config.get('models', {}),
                'llm_prompt': self.config.get('llm_prompt', {})
            })
            self.logger.info("使用API LLM后端")
        else:
            # 本地模式
            if not model_name:
                model_name = compression_config.get('local', {}).get('default_model')
                if not model_name:
                    raise ValueError("本地模式下必须指定模型名称")

            self.backend = LocalLLMCompressor(model_name, {
                'models': self.config.get('models', {}),
                'llm_prompt': self.config.get('llm_prompt', {})
            })
            self.logger.info(f"使用本地LLM后端: {model_name}")

        # 保持向后兼容的属性
        self.model_name = model_name or "API模式"
        self.compression_strategies = compression_strategies

        self.logger.info(f"文本压缩器初始化完成，模式: {compression_type}，模型: {self.model_name}")

    def _validate_device(self):
        """验证设备是否适合文本压缩推理"""
        if self.device.type == 'cpu':
            # 检查是否为Apple芯片
            if self._is_apple_silicon():
                self.logger.info("✅ 检测到Apple Silicon芯片，支持高效CPU推理")
                return
            else:
                # 普通CPU，不适合大模型推理
                error_msg = "❌ 检测到CPU设备，不适合大语言模型推理"
                self.logger.error(error_msg)
                self.logger.error("💡 建议解决方案:")
                self.logger.error("   1. 使用支持CUDA的NVIDIA GPU")
                self.logger.error("   2. 使用Apple Silicon芯片的Mac")
                self.logger.error("   3. 选择更小的模型")
                self.logger.error("   4. 禁用文本压缩功能")
                raise RuntimeError("CPU设备不适合大语言模型推理，请使用GPU或Apple Silicon芯片")

        # GPU设备，继续正常流程
        self.logger.info(f"✅ 设备验证通过: {self.device}")

    def _is_apple_silicon(self) -> bool:
        """检查是否为Apple Silicon芯片"""
        try:
            # 检查是否为macOS
            if platform.system() != 'Darwin':
                return False

            # 检查芯片架构
            machine = platform.machine().lower()
            if 'arm' in machine or 'aarch64' in machine:
                # 进一步检查是否为M系列芯片
                try:
                    import subprocess
                    result = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'],
                                          capture_output=True, text=True, timeout=5)
                    cpu_info = result.stdout.strip().lower()

                    # 检查是否包含Apple M系列芯片标识
                    if any(chip in cpu_info for chip in ['apple m1', 'apple m2', 'apple m3', 'apple m4']):
                        # 进一步检查是否为M2及以上
                        if any(chip in cpu_info for chip in ['apple m2', 'apple m3', 'apple m4']):
                            self.logger.info(f"检测到Apple Silicon芯片: {cpu_info}")
                            return True
                        else:
                            self.logger.warning("检测到Apple M1芯片，性能可能不足，建议使用M2及以上芯片")
                            return False
                except Exception as e:
                    self.logger.debug(f"无法获取详细CPU信息: {e}")
                    # 如果无法获取详细信息，但确认是ARM架构的macOS，保守地认为是Apple Silicon
                    return True

            return False
        except Exception as e:
            self.logger.debug(f"Apple Silicon检测失败: {e}")
            return False

    def _check_network_connection(self) -> bool:
        """
        检查网络连接和 Hugging Face 可访问性

        Returns:
            是否可以访问 Hugging Face
        """
        try:
            # 检查基本网络连接
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            self.logger.debug("网络连接正常")

            # 检查 Hugging Face 可访问性
            response = requests.get("https://huggingface.co", timeout=10)
            if response.status_code == 200:
                self.logger.info("✅ Hugging Face 可访问，支持模型下载")
                return True
            else:
                self.logger.warning(f"⚠️ Hugging Face 访问异常，状态码: {response.status_code}")
                return False

        except socket.error:
            self.logger.error("❌ 网络连接失败，无法访问互联网")
            return False
        except requests.RequestException as e:
            self.logger.warning(f"⚠️ Hugging Face 访问失败: {e}")
            return False
        except Exception as e:
            self.logger.warning(f"⚠️ 网络检查异常: {e}")
            return False

    def _setup_download_progress(self):
        """设置下载进度显示"""
        if TRANSFORMERS_AVAILABLE:
            # 设置 transformers 日志级别以显示下载进度
            transformers_logging.set_verbosity_info()
            self.logger.info("📥 已启用模型下载进度显示")

    def _load_model(self):
        """加载文本压缩模型"""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers库未安装，请先安装: pip install transformers torch")

        if self.model is None:
            try:
                self.logger.info(f"正在加载文本压缩模型: {self.model_name}")
                start_time = time.time()

                # 注意：模型加载时可能出现显存不足，将在加载过程中处理

                # 检查网络连接
                network_available = self._check_network_connection()
                if not network_available:
                    self.logger.warning("⚠️ 网络连接异常，如果模型未缓存，加载可能失败")

                # 设置下载进度显示
                self._setup_download_progress()

                # 加载文本压缩模型
                self.logger.info(f"📥 正在加载文本压缩模型: {self.model_name}")
                if network_available:
                    self.logger.info("💡 首次下载可能需要较长时间，请耐心等待...")

                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    cache_dir=self.model_cache_dir,
                    trust_remote_code=True
                )
                self.logger.info("✅ 分词器加载完成")

                # 加载压缩模型
                self.logger.info(f"📥 正在加载压缩模型: {self.model_name}")
                self.logger.info(f"📁 模型缓存路径: {os.path.abspath(self.model_cache_dir)}")
                self.logger.info(f"🖥️  目标设备: {self.device}")
                if network_available:
                    self.logger.info("💡 首次下载可能需要较长时间，请耐心等待...")

                # 尝试加载模型，捕获显存不足异常
                try:
                    # 根据设备选择加载策略
                    if self.device.type == 'cpu':
                        self.model = AutoModelForCausalLM.from_pretrained(
                            self.model_name,
                            cache_dir=self.model_cache_dir,
                            torch_dtype=self.dtype,
                            device_map="cpu",
                            trust_remote_code=True
                        )
                    else:
                        self.model = AutoModelForCausalLM.from_pretrained(
                            self.model_name,
                            cache_dir=self.model_cache_dir,
                            torch_dtype=self.dtype,
                            trust_remote_code=True
                        )
                        self.model = self.model.to(self.device)

                except torch.cuda.OutOfMemoryError as e:
                    error_msg = f"❌ GPU显存不足，无法加载模型 {self.model_name}"
                    self.logger.error(error_msg)
                    self.logger.error(f"显存错误详情: {str(e)}")
                    self.logger.error("💡 建议解决方案:")
                    self.logger.error("   1. 选择更小的模型")
                    self.logger.error("   2. 关闭其他占用显存的程序")
                    self.logger.error("   3. 使用CPU推理（性能较慢）")
                    raise RuntimeError(f"显存不足，无法加载压缩模型: {self.model_name}") from e

                except Exception as e:
                    if "out of memory" in str(e).lower() or "cuda" in str(e).lower():
                        error_msg = f"❌ 显存相关错误，无法加载模型 {self.model_name}"
                        self.logger.error(error_msg)
                        self.logger.error(f"错误详情: {str(e)}")
                        raise RuntimeError(f"显存不足，无法加载压缩模型: {self.model_name}") from e
                    else:
                        # 其他类型的错误，重新抛出
                        raise

                self.logger.info("✅ 模型加载完成")

                # 设置为评估模式
                self.model.eval()

                load_time = time.time() - start_time
                self.logger.info(f"🎉 压缩模型加载成功: {self.model_name}，设备: {self.device}，总耗时: {load_time:.2f}秒")

            except Exception as e:
                self.logger.error(f"❌ 压缩模型加载失败: {self.model_name}")
                self.logger.error(f"错误详情: {e}")
                self.logger.error("💡 可能的解决方案:")
                self.logger.error("   1. 检查网络连接")
                self.logger.error("   2. 确保有足够的存储空间")
                self.logger.error("   3. 检查 Hugging Face 访问权限")
                self.logger.error("   4. 验证模型名称是否正确")
                self.logger.error("📝 文本压缩功能将不可用，压缩时将直接返回原文")
                raise
    
    def _llm_compress(self, text: str, strategy: str = "simple") -> str:
        """
        使用大语言模型进行文本压缩

        Args:
            text: 要压缩的文本
            strategy: 压缩策略

        Returns:
            压缩后的文本
        """
        try:
            # 获取策略配置
            default_strategy = "simple"
            if strategy not in self.compression_strategies:
                self.logger.warning(f"未知的压缩策略: {strategy}，使用默认策略 '{default_strategy}'")
                strategy = default_strategy

            strategy_config = self.compression_strategies[strategy]

            # 构建对话格式的输入
            system_prompt = strategy_config.get("system_prompt", strategy_config.get("system", ""))
            user_template = strategy_config.get("user_template", "")

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_template.format(text=text)}
            ]

            # 使用分词器构建输入
            input_text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            # 编码输入
            inputs = self.tokenizer(
                input_text,
                return_tensors="pt",
                truncation=True,
                max_length=2048
            )

            # 确保输入数据在正确的设备上
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 生成参数
            generation_config = {
                "max_new_tokens": 512,
                "temperature": 0.3,
                "do_sample": True,
                "top_p": 0.8,
                "repetition_penalty": 1.1,
                "pad_token_id": self.tokenizer.eos_token_id
            }

            # 生成回复，捕获显存不足异常
            try:
                with torch.no_grad():
                    outputs = self.model.generate(
                        inputs['input_ids'],
                        attention_mask=inputs['attention_mask'],
                        **generation_config
                    )
            except torch.cuda.OutOfMemoryError as e:
                error_msg = "❌ 文本压缩过程中GPU显存不足"
                self.logger.error(error_msg)
                self.logger.error(f"显存错误详情: {str(e)}")
                raise RuntimeError("文本压缩过程中显存不足，请尝试使用更小的模型或减少输入文本长度") from e
            except Exception as e:
                if "out of memory" in str(e).lower() or "cuda" in str(e).lower():
                    error_msg = "❌ 文本压缩过程中显存相关错误"
                    self.logger.error(error_msg)
                    self.logger.error(f"错误详情: {str(e)}")
                    raise RuntimeError("文本压缩过程中显存不足，请尝试使用更小的模型或减少输入文本长度") from e
                else:
                    # 其他类型的错误，重新抛出
                    raise

            # 解码输出
            generated_text = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            # 清理输出
            compressed_text = generated_text.strip()

            # 如果输出为空或过短，返回原文
            if not compressed_text or len(compressed_text) < 3:
                self.logger.warning(f"LLM压缩输出过短，返回原文: '{generated_text}'")
                return text

            return compressed_text

        except Exception as e:
            self.logger.error(f"LLM文本压缩失败: {e}")
            raise
    
    def compress(self, text: str, target_length: Optional[int] = None,
                strategy: str = "simple") -> CompressionResult:
        """
        压缩文本

        Args:
            text: 要压缩的文本
            target_length: 目标长度（字符数），None表示自动压缩
            strategy: 压缩策略

        Returns:
            压缩结果
        """
        # 直接使用后端的compress方法
        return self.backend.compress(text, target_length, strategy)
    

    
    def compress_batch(self, texts: List[str], target_length: Optional[int] = None,
                      strategy: str = "simple") -> List[CompressionResult]:
        """
        批量压缩文本

        Args:
            texts: 要压缩的文本列表
            target_length: 目标长度
            strategy: 压缩策略

        Returns:
            压缩结果列表
        """
        results = []

        self.logger.info(f"开始使用LLM批量压缩 {len(texts)} 个文本")

        # 预先加载模型以避免重复加载
        try:
            self._load_model()
        except Exception as e:
            self.logger.error(f"模型加载失败，批量压缩将返回原文: {e}")
            # 如果模型加载失败，所有文本都返回原文
            for text in texts:
                results.append(CompressionResult(
                    original_text=text,
                    compressed_text=text,
                    compression_ratio=1.0,
                    processing_time=0.0,
                    success=False
                ))
            return results

        for i, text in enumerate(texts):
            if text.strip():  # 跳过空文本
                result = self.compress(text, target_length, strategy)
                results.append(result)
                self.logger.debug(f"LLM批量压缩进度: {i+1}/{len(texts)}")
            else:
                # 空文本直接返回空结果
                results.append(CompressionResult(
                    original_text=text,
                    compressed_text=text,
                    compression_ratio=1.0,
                    processing_time=0.0,
                    success=True
                ))

        successful_count = sum(1 for r in results if r.success)
        self.logger.info(f"LLM批量压缩完成，共处理 {len(results)} 个文本，成功 {successful_count} 个")
        return results

    def compress_batch_with_context(self, text_regions, translated_texts: List[str],
                                   target_length: Optional[int] = None,
                                   strategy: str = "simple") -> List[CompressionResult]:
        """
        批量上下文感知压缩文本

        Args:
            text_regions: TextRegion对象列表，包含位置信息
            translated_texts: 对应的翻译文本列表
            target_length: 目标长度
            strategy: 压缩策略

        Returns:
            压缩结果列表
        """
        # 直接使用后端的compress_batch_with_context方法
        return self.backend.compress_batch_with_context(text_regions, translated_texts, target_length, strategy)

    def _format_batch_context_input(self, text_regions, translated_texts: List[str]) -> str:
        """
        格式化批量上下文输入

        Args:
            text_regions: TextRegion对象列表
            translated_texts: 翻译文本列表

        Returns:
            格式化的批量输入字符串
        """
        batch_lines = []

        for i, (region, translated_text) in enumerate(zip(text_regions, translated_texts), 1):
            # 获取文本框的四角坐标
            bbox_str = f"[{region.bbox[0]}, {region.bbox[1]}, {region.bbox[2]}, {region.bbox[3]}]"

            # 计算中心点位置描述
            center_x, center_y = region.center
            position_desc = f"坐标{bbox_str}, 中心点({center_x}, {center_y})"

            # 格式化单个文本框信息
            batch_lines.append(f"文本框{i} [位置: {position_desc}]")
            batch_lines.append(f"原文: {region.text}")

            # 检查是否为直接翻译模式（译文与原文相同表示跳过了本地翻译）
            if translated_text == region.text:
                # 直接翻译模式：只提供原文
                pass  # 不添加译文行
            else:
                # 优化模式：提供机器翻译结果
                batch_lines.append(f"译文: {translated_text}")

            batch_lines.append("")  # 空行分隔

        return "\n".join(batch_lines)

    def _llm_compress_batch(self, batch_content: str, strategy: str) -> str:
        """
        使用LLM进行批量压缩

        Args:
            batch_content: 格式化的批量内容
            strategy: 压缩策略

        Returns:
            LLM输出的压缩结果
        """
        # 获取策略配置
        default_strategy = "simple"
        if strategy not in self.compression_strategies:
            self.logger.warning(f"未知的压缩策略: {strategy}，使用默认策略 '{default_strategy}'")
            strategy = default_strategy

        strategy_config = self.compression_strategies[strategy]

        # 构建对话格式的输入
        system_prompt = strategy_config.get("system_prompt", strategy_config.get("system", ""))
        user_template = strategy_config.get("user_template", "")

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_template.format(batch_content=batch_content)}
        ]

        # 使用分词器构建输入
        input_text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )

        # 编码输入
        inputs = self.tokenizer(
            input_text,
            return_tensors="pt",
            truncation=True,
            max_length=4096  # 增加最大长度以支持批量输入
        )

        # 移动到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # 生成输出
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=2048,  # 增加输出长度以支持批量输出
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id
            )

        # 解码输出
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 提取生成的部分（去除输入部分）
        if input_text in generated_text:
            compressed_text = generated_text.replace(input_text, "").strip()
        else:
            compressed_text = generated_text.strip()

        return compressed_text

    def _parse_batch_context_output(self, llm_output: str, expected_count: int) -> List[str]:
        """
        解析批量上下文输出

        Args:
            llm_output: LLM的输出文本
            expected_count: 期望的文本框数量

        Returns:
            解析出的压缩文本列表
        """
        compressed_texts = []

        # 查找assistant部分的输出
        if "assistant\n" in llm_output:
            # 提取assistant后的内容
            assistant_output = llm_output.split("assistant\n", 1)[-1].strip()
        else:
            # 如果没有找到assistant标记，使用整个输出
            assistant_output = llm_output.strip()

        # 按行分割输出
        lines = assistant_output.split('\n')

        # 解析每个文本框的结果
        for i in range(1, expected_count + 1):
            found = False
            for line in lines:
                line = line.strip()
                # 匹配格式：文本框N: 内容
                if line.startswith(f"文本框{i}:") or line.startswith(f"文本框{i}："):
                    # 提取冒号后的内容
                    if ":" in line:
                        content = line.split(":", 1)[-1].strip()
                    else:
                        content = line.split("：", 1)[-1].strip()
                    compressed_texts.append(content)
                    found = True
                    self.logger.debug(f"解析到文本框{i}: '{content}'")
                    break

            if not found:
                # 如果没有找到对应的文本框结果，使用空字符串
                self.logger.warning(f"未找到文本框{i}的压缩结果，将使用空字符串")
                compressed_texts.append("")

        # 如果解析的数量不匹配，补齐或截断
        while len(compressed_texts) < expected_count:
            compressed_texts.append("")

        if len(compressed_texts) > expected_count:
            compressed_texts = compressed_texts[:expected_count]

        self.logger.info(f"成功解析 {len([t for t in compressed_texts if t])} / {expected_count} 个文本框结果")
        return compressed_texts

    def should_compress(self, original_text: str, translated_text: str,
                       threshold: float = 1.5) -> bool:
        """
        判断是否需要压缩
        
        Args:
            original_text: 原始文本
            translated_text: 翻译后文本
            threshold: 长度比例阈值
            
        Returns:
            是否需要压缩
        """
        if not original_text or not translated_text:
            return False
        
        length_ratio = len(translated_text) / len(original_text)
        should_compress = length_ratio > threshold
        
        self.logger.debug(f"长度比例: {length_ratio:.2f}, 阈值: {threshold}, 需要压缩: {should_compress}")
        return should_compress
    
    def get_compression_strategies(self) -> Dict[str, str]:
        """获取可用的压缩策略"""
        return self.compression_strategies.copy()
    
    def is_available(self) -> bool:
        """检查文本压缩器是否可用"""
        return TRANSFORMERS_AVAILABLE  # 需要transformers库支持
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "loaded": self.model is not None and self.tokenizer is not None,
            "available": self.is_available(),
            "type": "llm_based",  # 使用大语言模型的压缩器
            "strategies": list(self.compression_strategies.keys())
        }


# 全局文本压缩器实例缓存
_text_compressor_cache = {}


def get_text_compressor(model_name: Optional[str] = None) -> TextCompressor:
    """
    获取文本压缩器实例

    Args:
        model_name: 模型名称，本地模式时必须指定，API模式时可为None

    Returns:
        TextCompressor实例
    """
    global _text_compressor_cache

    # 获取配置以确定模式
    config = get_config()
    compression_config = config.get('models.compression', {})
    compression_type = compression_config.get('type', 'local')

    # 生成缓存键
    if compression_type == 'api':
        # API模式使用provider和model作为缓存键
        api_config = compression_config.get('api', {})
        provider = api_config.get('provider', 'openai')
        api_model = api_config.get('model', 'gpt-4o')
        cache_key = f"api_{provider}_{api_model}"
    else:
        # 本地模式使用model_name作为缓存键
        if not model_name:
            model_name = compression_config.get('local', {}).get('default_model')
            if not model_name:
                raise ValueError("本地模式下必须指定模型名称")
        cache_key = f"local_{model_name}"

    # 使用缓存键获取或创建实例
    if cache_key not in _text_compressor_cache:
        _text_compressor_cache[cache_key] = TextCompressor(model_name)

    return _text_compressor_cache[cache_key]


def clear_text_compressor_cache():
    """清空文本压缩器缓存"""
    global _text_compressor_cache
    _text_compressor_cache.clear()
