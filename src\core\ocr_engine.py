#!/usr/bin/env python3
"""
OCR引擎模块 - 基于用户自定义PaddleOCRTool

使用用户提供的PaddleOCRTool，强制用户指定源语言
"""

import os
from typing import List, Optional, Dict
from dataclasses import dataclass

from .text_region import TextRegion
from ..utils.logger import get_logger
from .paddle_ocr_adapter import get_paddle_ocr_adapter, PaddleOCRConfig, set_paddle_ocr_config


@dataclass
class OCREngineConfig:
    """OCR引擎配置"""
    default_language: str  # 必须指定的默认语言，不允许为空
    device: str = "cpu"  # 设备类型
    ocr_version: str = "PP-OCRv5"  # OCR版本
    use_doc_orientation_classify: bool = False
    use_doc_unwarping: bool = False
    use_textline_orientation: bool = False

    def __post_init__(self):
        """初始化后验证"""
        if not self.default_language or self.default_language.strip() == "":
            raise ValueError("default_language不能为空，必须指定具体的语言代码")

        if self.default_language.lower() in ['unknown', 'none', 'null']:
            raise ValueError(f"default_language不能是无效值: '{self.default_language}'，必须指定具体的语言代码")


class OCREngine:
    """
    OCR引擎
    基于用户自定义的PaddleOCRTool，强制用户指定源语言
    """
    
    def __init__(self, config: Optional[OCREngineConfig] = None):
        """
        初始化OCR引擎

        Args:
            config: OCR引擎配置，必须指定default_language

        Raises:
            ValueError: 当未指定配置或语言时
        """
        if config is None:
            raise ValueError("必须提供OCREngineConfig配置，且必须指定default_language")

        # 验证语言是否指定
        if not hasattr(config, 'default_language') or not config.default_language:
            raise ValueError("必须在OCREngineConfig中指定default_language，不能为空")

        # 验证语言不能是unknown或其他无效值
        if config.default_language.lower() in ['unknown', 'none', '', 'null']:
            raise ValueError(f"default_language不能是无效值: '{config.default_language}'，必须指定具体的语言代码")

        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化Paddle OCR适配器
        paddle_config = PaddleOCRConfig(
            lang=self.config.default_language,
            ocr_version=self.config.ocr_version,
            device=self.config.device,
            use_doc_orientation_classify=self.config.use_doc_orientation_classify,
            use_doc_unwarping=self.config.use_doc_unwarping,
            use_textline_orientation=self.config.use_textline_orientation
        )
        
        set_paddle_ocr_config(paddle_config)
        self.paddle_adapter = get_paddle_ocr_adapter()
        
        self.logger.info("OCR引擎初始化完成")
    
    def detect_and_recognize(self, image_path: str, source_language: str) -> List[TextRegion]:
        """
        检测和识别图像中的文本
        
        Args:
            image_path: 图像文件路径
            source_language: 用户指定的源语言代码（必须指定）
            
        Returns:
            文本区域列表
        """
        if not source_language:
            raise ValueError("必须指定源语言！Paddle OCR不支持自动语言检测")
        
        # 验证语言支持
        if not self.paddle_adapter.is_language_supported(source_language):
            supported_langs = list(self.paddle_adapter.get_supported_languages().keys())
            raise ValueError(f"不支持的语言: {source_language}。支持的语言: {supported_langs}")
        
        self.logger.info(f"使用指定源语言进行OCR: {source_language} ({self.paddle_adapter.get_language_name(source_language)})")
        
        # 执行OCR识别
        return self.paddle_adapter.detect_and_recognize(image_path, source_language)
    
    def get_supported_languages(self) -> Dict[str, str]:
        """
        获取支持的语言列表
        
        Returns:
            语言代码到语言名称的映射字典
        """
        return self.paddle_adapter.get_supported_languages()
    
    def is_language_supported(self, lang: str) -> bool:
        """
        检查是否支持指定语言
        
        Args:
            lang: 语言代码
            
        Returns:
            是否支持
        """
        return self.paddle_adapter.is_language_supported(lang)
    
    def get_language_name(self, lang: str) -> str:
        """
        获取语言名称
        
        Args:
            lang: 语言代码
            
        Returns:
            语言名称
        """
        return self.paddle_adapter.get_language_name(lang)
    
    def validate_language(self, lang: str) -> bool:
        """
        验证语言代码是否有效
        
        Args:
            lang: 语言代码
            
        Returns:
            是否有效
        """
        return self.is_language_supported(lang)
    
    def get_language_suggestions(self, partial_lang: str = "") -> List[str]:
        """
        获取语言建议（用于自动补全）
        
        Args:
            partial_lang: 部分语言代码
            
        Returns:
            匹配的语言代码列表
        """
        all_langs = self.get_supported_languages()
        if not partial_lang:
            return list(all_langs.keys())
        
        # 模糊匹配
        matches = []
        partial_lower = partial_lang.lower()
        
        for code, name in all_langs.items():
            if (partial_lower in code.lower() or 
                partial_lower in name.lower()):
                matches.append(code)
        
        return matches
    
    def print_supported_languages(self):
        """打印支持的语言列表"""
        print("=== 支持的源语言列表 ===")
        languages = self.get_supported_languages()
        
        # 按语言代码排序
        sorted_langs = sorted(languages.items())
        
        for code, name in sorted_langs:
            print(f"  {code:12} - {name}")
        
        print(f"\n总计支持 {len(languages)} 种语言")


# 全局实例
_ocr_engine = None


def get_ocr_engine(config: Optional[OCREngineConfig] = None) -> OCREngine:
    """
    获取OCR引擎实例（单例模式）

    Args:
        config: OCR引擎配置，必须指定且包含default_language

    Returns:
        OCREngine实例

    Raises:
        ValueError: 当未提供配置时
    """
    global _ocr_engine
    if _ocr_engine is None:
        if config is None:
            raise ValueError("首次创建OCR引擎时必须提供OCREngineConfig配置")
        _ocr_engine = OCREngine(config)
    return _ocr_engine


def set_ocr_engine_config(config: OCREngineConfig):
    """
    设置OCR引擎配置（重新初始化）

    Args:
        config: 新的配置
    """
    global _ocr_engine
    _ocr_engine = OCREngine(config)


if __name__ == "__main__":
    # 测试代码
    print("=== OCR引擎测试 ===")

    # 创建OCR引擎
    config = OCREngineConfig(default_language="en")
    ocr_engine = get_ocr_engine(config)
    
    # 显示支持的语言
    ocr_engine.print_supported_languages()
    
    # 测试语言验证
    test_langs = ["en", "ch", "fr", "invalid_lang"]
    print(f"\n=== 语言验证测试 ===")
    for lang in test_langs:
        is_valid = ocr_engine.validate_language(lang)
        name = ocr_engine.get_language_name(lang) if is_valid else "无效"
        print(f"  {lang}: {'✅' if is_valid else '❌'} {name}")
    
    # 测试语言建议
    print(f"\n=== 语言建议测试 ===")
    suggestions = ocr_engine.get_language_suggestions("en")
    print(f"包含'en'的语言: {suggestions}")
    
    # 测试OCR识别（如果有测试图像）
    test_image = "test_image.png"
    if os.path.exists(test_image):
        print(f"\n=== OCR识别测试 ===")
        print(f"测试图像: {test_image}")
        
        try:
            # 测试英文识别
            regions = ocr_engine.detect_and_recognize(test_image, "en")
            print(f"英文识别结果: {len(regions)} 个文本区域")
            for i, region in enumerate(regions[:3]):  # 只显示前3个
                print(f"  {i+1}. '{region.text}' (置信度: {region.confidence})")
        
        except Exception as e:
            print(f"OCR识别失败: {e}")
    else:
        print(f"\n测试图像不存在: {test_image}")
    
    print(f"\n=== 测试完成 ===")
