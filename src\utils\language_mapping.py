#!/usr/bin/env python3
"""
语言映射模块 - 统一管理OCR和翻译模块的语言支持

提供PaddleOCR和NLLB-200模型的完整语言映射和转换功能
"""

# 不需要额外的导入，只使用内置类型

# PaddleOCR官方支持的语言映射（基于官方文档）
PADDLEOCR_LANGUAGES = {
    # 语言代码: (中文名称, 英文名称)
    'ch': ('中文', 'Chinese & English'),
    'en': ('英文', 'English'),
    'fr': ('法文', 'French'),
    'de': ('德文', 'German'),
    'japan': ('日文', 'Japanese'),
    'korean': ('韩文', 'Korean'),
    'chinese_cht': ('中文繁体', 'Chinese Traditional'),
    'af': ('南非荷兰文', 'Afrikaans'),
    'it': ('意大利文', 'Italian'),
    'es': ('西班牙文', 'Spanish'),
    'bs': ('波斯尼亚文', 'Bosnian'),
    'pt': ('葡萄牙文', 'Portuguese'),
    'cs': ('捷克文', 'Czech'),
    'cy': ('威尔士文', 'Welsh'),
    'da': ('丹麦文', 'Danish'),
    'et': ('爱沙尼亚文', 'Estonian'),
    'ga': ('爱尔兰文', 'Irish'),
    'hr': ('克罗地亚文', 'Croatian'),
    'hu': ('匈牙利文', 'Hungarian'),
    'rs_latin': ('塞尔维亚文（latin）', 'Serbian(latin)'),
    'id': ('印度尼西亚文', 'Indonesian'),
    'oc': ('欧西坦文', 'Occitan'),
    'is': ('冰岛文', 'Icelandic'),
    'lt': ('立陶宛文', 'Lithuanian'),
    'mi': ('毛利文', 'Maori'),
    'ms': ('马来文', 'Malay'),
    'nl': ('荷兰文', 'Dutch'),
    'no': ('挪威文', 'Norwegian'),
    'pl': ('波兰文', 'Polish'),
    'sk': ('斯洛伐克文', 'Slovak'),
    'sl': ('斯洛文尼亚文', 'Slovenian'),
    'sq': ('阿尔巴尼亚文', 'Albanian'),
    'sv': ('瑞典文', 'Swedish'),
    'sw': ('西瓦希里文', 'Swahili'),
    'tl': ('塔加洛文', 'Tagalog'),
    'tr': ('土耳其文', 'Turkish'),
    'uz': ('乌兹别克文', 'Uzbek'),
    'ru': ('俄罗斯文', 'Russian'),
    'uk': ('乌克兰文', 'Ukrainian'),
    'la': ('拉丁文', 'Latin'),
    'be': ('白俄罗斯文', 'Belarusian'),
}

# NLLB-200官方支持的语言映射（基于官方language_details）
NLLB_LANGUAGES = {
    # 语言代码: (中文名称, 英文名称)
    'ace_Arab': ('亚齐语(阿拉伯文)', 'Acehnese (Arabic)'),
    'ace_Latn': ('亚齐语(拉丁文)', 'Acehnese (Latin)'),
    'acm_Arab': ('美索不达米亚阿拉伯语', 'Mesopotamian Arabic'),
    'acq_Arab': ('塔伊兹-阿登阿拉伯语', 'Ta\'izzi-Adeni Arabic'),
    'aeb_Arab': ('突尼斯阿拉伯语', 'Tunisian Arabic'),
    'afr_Latn': ('南非荷兰语', 'Afrikaans'),
    'ajp_Arab': ('南黎凡特阿拉伯语', 'South Levantine Arabic'),
    'aka_Latn': ('阿肯语', 'Akan'),
    'amh_Ethi': ('阿姆哈拉语', 'Amharic'),
    'apc_Arab': ('北黎凡特阿拉伯语', 'North Levantine Arabic'),
    'arb_Arab': ('现代标准阿拉伯语', 'Modern Standard Arabic'),
    'ars_Arab': ('纳吉迪阿拉伯语', 'Najdi Arabic'),
    'ary_Arab': ('摩洛哥阿拉伯语', 'Moroccan Arabic'),
    'arz_Arab': ('埃及阿拉伯语', 'Egyptian Arabic'),
    'asm_Beng': ('阿萨姆语', 'Assamese'),
    'ast_Latn': ('阿斯图里亚斯语', 'Asturian'),
    'awa_Deva': ('阿瓦德语', 'Awadhi'),
    'ayr_Latn': ('中央艾马拉语', 'Central Aymara'),
    'azb_Arab': ('南阿塞拜疆语', 'South Azerbaijani'),
    'azj_Latn': ('北阿塞拜疆语', 'North Azerbaijani'),
    'bak_Cyrl': ('巴什基尔语', 'Bashkir'),
    'bam_Latn': ('班巴拉语', 'Bambara'),
    'ban_Latn': ('巴厘语', 'Balinese'),
    'bel_Cyrl': ('白俄罗斯语', 'Belarusian'),
    'bem_Latn': ('本巴语', 'Bemba'),
    'ben_Beng': ('孟加拉语', 'Bengali'),
    'bho_Deva': ('博杰普尔语', 'Bhojpuri'),
    'bjn_Arab': ('班贾尔语(阿拉伯文)', 'Banjarese (Arabic)'),
    'bjn_Latn': ('班贾尔语(拉丁文)', 'Banjarese (Latin)'),
    'bod_Tibt': ('标准藏语', 'Standard Tibetan'),
    'bos_Latn': ('波斯尼亚语', 'Bosnian'),
    'bug_Latn': ('布吉语', 'Buginese'),
    'bul_Cyrl': ('保加利亚语', 'Bulgarian'),
    'cat_Latn': ('加泰罗尼亚语', 'Catalan'),
    'ceb_Latn': ('宿务语', 'Cebuano'),
    'ces_Latn': ('捷克语', 'Czech'),
    'cjk_Latn': ('乔语', 'Chokwe'),
    'ckb_Arab': ('中库尔德语', 'Central Kurdish'),
    'crh_Latn': ('克里米亚鞑靼语', 'Crimean Tatar'),
    'cym_Latn': ('威尔士语', 'Welsh'),
    'dan_Latn': ('丹麦语', 'Danish'),
    'deu_Latn': ('德语', 'German'),
    'dik_Latn': ('西南丁卡语', 'Southwestern Dinka'),
    'dyu_Latn': ('迪乌拉语', 'Dyula'),
    'dzo_Tibt': ('宗卡语', 'Dzongkha'),
    'ell_Grek': ('希腊语', 'Greek'),
    'eng_Latn': ('英语', 'English'),
    'epo_Latn': ('世界语', 'Esperanto'),
    'est_Latn': ('爱沙尼亚语', 'Estonian'),
    'eus_Latn': ('巴斯克语', 'Basque'),
    'ewe_Latn': ('埃维语', 'Ewe'),
    'fao_Latn': ('法罗语', 'Faroese'),
    'pes_Arab': ('西波斯语', 'Western Persian'),
    'fij_Latn': ('斐济语', 'Fijian'),
    'fin_Latn': ('芬兰语', 'Finnish'),
    'fon_Latn': ('丰语', 'Fon'),
    'fra_Latn': ('法语', 'French'),
    'fur_Latn': ('弗留利语', 'Friulian'),
    'fuv_Latn': ('尼日利亚富拉语', 'Nigerian Fulfulde'),
    'gla_Latn': ('苏格兰盖尔语', 'Scottish Gaelic'),
    'gle_Latn': ('爱尔兰语', 'Irish'),
    'glg_Latn': ('加利西亚语', 'Galician'),
    'grn_Latn': ('瓜拉尼语', 'Guarani'),
    'guj_Gujr': ('古吉拉特语', 'Gujarati'),
    'hat_Latn': ('海地克里奥尔语', 'Haitian Creole'),
    'hau_Latn': ('豪萨语', 'Hausa'),
    'heb_Hebr': ('希伯来语', 'Hebrew'),
    'hin_Deva': ('印地语', 'Hindi'),
    'hne_Deva': ('恰蒂斯加尔语', 'Chhattisgarhi'),
    'hrv_Latn': ('克罗地亚语', 'Croatian'),
    'hun_Latn': ('匈牙利语', 'Hungarian'),
    'hye_Armn': ('亚美尼亚语', 'Armenian'),
    'ibo_Latn': ('伊博语', 'Igbo'),
    'ilo_Latn': ('伊洛卡诺语', 'Ilocano'),
    'ind_Latn': ('印尼语', 'Indonesian'),
    'isl_Latn': ('冰岛语', 'Icelandic'),
    'ita_Latn': ('意大利语', 'Italian'),
    'jav_Latn': ('爪哇语', 'Javanese'),
    'jpn_Jpan': ('日语', 'Japanese'),
    'kab_Latn': ('卡拜尔语', 'Kabyle'),
    'kac_Latn': ('景颇语', 'Jingpho'),
    'kam_Latn': ('卡姆巴语', 'Kamba'),
    'kan_Knda': ('卡纳达语', 'Kannada'),
    'kas_Arab': ('克什米尔语(阿拉伯文)', 'Kashmiri (Arabic)'),
    'kas_Deva': ('克什米尔语(天城文)', 'Kashmiri (Devanagari)'),
    'kat_Geor': ('格鲁吉亚语', 'Georgian'),
    'knc_Arab': ('中卡奈姆布语(阿拉伯文)', 'Central Kanuri (Arabic)'),
    'knc_Latn': ('中卡奈姆布语(拉丁文)', 'Central Kanuri (Latin)'),
    'kaz_Cyrl': ('哈萨克语', 'Kazakh'),
    'kbp_Latn': ('卡拜语', 'Kabiyè'),
    'kea_Latn': ('佛得角克里奥尔语', 'Kabuverdianu'),
    'khm_Khmr': ('中央高棉语', 'Central Khmer'),
    'kik_Latn': ('基库尤语', 'Kikuyu'),
    'kin_Latn': ('卢旺达语', 'Kinyarwanda'),
    'kir_Cyrl': ('吉尔吉斯语', 'Kyrgyz'),
    'kmb_Latn': ('金邦杜语', 'Kimbundu'),
    'kon_Latn': ('刚果语', 'Kikongo'),
    'kor_Hang': ('韩语', 'Korean'),
    'kmr_Latn': ('北库尔德语', 'Northern Kurdish'),
    'lao_Laoo': ('老挝语', 'Lao'),
    'lvs_Latn': ('标准拉脱维亚语', 'Standard Latvian'),
    'lij_Latn': ('利古里亚语', 'Ligurian'),
    'lim_Latn': ('林堡语', 'Limburgish'),
    'lin_Latn': ('林加拉语', 'Lingala'),
    'lit_Latn': ('立陶宛语', 'Lithuanian'),
    'lmo_Latn': ('伦巴第语', 'Lombard'),
    'ltg_Latn': ('拉特加莱语', 'Latgalian'),
    'ltz_Latn': ('卢森堡语', 'Luxembourgish'),
    'lua_Latn': ('卢巴-卢卢阿语', 'Luba-Lulua'),
    'lug_Latn': ('干达语', 'Ganda'),
    'luo_Latn': ('卢奥语', 'Luo'),
    'lus_Latn': ('米佐语', 'Mizo'),
    'mag_Deva': ('摩揭陀语', 'Magahi'),
    'mai_Deva': ('迈蒂利语', 'Maithili'),
    'mal_Mlym': ('马拉雅拉姆语', 'Malayalam'),
    'mar_Deva': ('马拉地语', 'Marathi'),
    'min_Latn': ('米南佳保语', 'Minangkabau'),
    'mkd_Cyrl': ('马其顿语', 'Macedonian'),
    'plt_Latn': ('马达加斯加语', 'Plateau Malagasy'),
    'mlt_Latn': ('马耳他语', 'Maltese'),
    'mni_Beng': ('曼尼普尔语', 'Meitei'),
    'khk_Cyrl': ('哈尔喀蒙古语', 'Halh Mongolian'),
    'mos_Latn': ('莫西语', 'Mossi'),
    'mri_Latn': ('毛利语', 'Maori'),
    'zsm_Latn': ('标准马来语', 'Standard Malay'),
    'mya_Mymr': ('缅甸语', 'Burmese'),
    'nld_Latn': ('荷兰语', 'Dutch'),
    'nno_Latn': ('新挪威语', 'Norwegian Nynorsk'),
    'nob_Latn': ('书面挪威语', 'Norwegian Bokmål'),
    'npi_Deva': ('尼泊尔语', 'Nepali'),
    'nso_Latn': ('北索托语', 'Northern Sotho'),
    'nus_Latn': ('努埃尔语', 'Nuer'),
    'nya_Latn': ('尼扬贾语', 'Nyanja'),
    'oci_Latn': ('奥克语', 'Occitan'),
    'gaz_Latn': ('西奥罗莫语', 'West Central Oromo'),
    'ory_Orya': ('奥里亚语', 'Odia'),
    'pag_Latn': ('邦阿西南语', 'Pangasinan'),
    'pan_Guru': ('东旁遮普语', 'Eastern Panjabi'),
    'pap_Latn': ('帕皮阿门托语', 'Papiamento'),
    'pol_Latn': ('波兰语', 'Polish'),
    'por_Latn': ('葡萄牙语', 'Portuguese'),
    'prs_Arab': ('达里语', 'Dari'),
    'pbt_Arab': ('南普什图语', 'Southern Pashto'),
    'quy_Latn': ('阿亚库乔克丘亚语', 'Ayacucho Quechua'),
    'ron_Latn': ('罗马尼亚语', 'Romanian'),
    'run_Latn': ('隆迪语', 'Rundi'),
    'rus_Cyrl': ('俄语', 'Russian'),
    'sag_Latn': ('桑戈语', 'Sango'),
    'san_Deva': ('梵语', 'Sanskrit'),
    'sat_Beng': ('桑塔利语', 'Santali'),
    'scn_Latn': ('西西里语', 'Sicilian'),
    'shn_Mymr': ('掸语', 'Shan'),
    'sin_Sinh': ('僧伽罗语', 'Sinhala'),
    'slk_Latn': ('斯洛伐克语', 'Slovak'),
    'slv_Latn': ('斯洛文尼亚语', 'Slovenian'),
    'smo_Latn': ('萨摩亚语', 'Samoan'),
    'sna_Latn': ('绍纳语', 'Shona'),
    'snd_Arab': ('信德语', 'Sindhi'),
    'som_Latn': ('索马里语', 'Somali'),
    'sot_Latn': ('南索托语', 'Southern Sotho'),
    'spa_Latn': ('西班牙语', 'Spanish'),
    'als_Latn': ('托斯克阿尔巴尼亚语', 'Tosk Albanian'),
    'srd_Latn': ('萨丁尼亚语', 'Sardinian'),
    'srp_Cyrl': ('塞尔维亚语', 'Serbian'),
    'ssw_Latn': ('斯瓦蒂语', 'Swati'),
    'sun_Latn': ('巽他语', 'Sundanese'),
    'swe_Latn': ('瑞典语', 'Swedish'),
    'swh_Latn': ('斯瓦希里语', 'Swahili'),
    'szl_Latn': ('西里西亚语', 'Silesian'),
    'tam_Taml': ('泰米尔语', 'Tamil'),
    'tat_Cyrl': ('鞑靼语', 'Tatar'),
    'tel_Telu': ('泰卢固语', 'Telugu'),
    'tgk_Cyrl': ('塔吉克语', 'Tajik'),
    'tgl_Latn': ('他加禄语', 'Tagalog'),
    'tha_Thai': ('泰语', 'Thai'),
    'tir_Ethi': ('提格里尼亚语', 'Tigrinya'),
    'taq_Latn': ('塔马塞特语(拉丁文)', 'Tamasheq (Latin)'),
    'taq_Tfng': ('塔马塞特语(提非纳文)', 'Tamasheq (Tifinagh)'),
    'tpi_Latn': ('托克皮辛语', 'Tok Pisin'),
    'tsn_Latn': ('茨瓦纳语', 'Tswana'),
    'tso_Latn': ('聪加语', 'Tsonga'),
    'tuk_Latn': ('土库曼语', 'Turkmen'),
    'tum_Latn': ('通布卡语', 'Tumbuka'),
    'tur_Latn': ('土耳其语', 'Turkish'),
    'twi_Latn': ('契维语', 'Twi'),
    'tzm_Tfng': ('中阿特拉斯塔马塞特语', 'Central Atlas Tamazight'),
    'uig_Arab': ('维吾尔语', 'Uyghur'),
    'ukr_Cyrl': ('乌克兰语', 'Ukrainian'),
    'umb_Latn': ('翁本杜语', 'Umbundu'),
    'urd_Arab': ('乌尔都语', 'Urdu'),
    'uzn_Latn': ('北乌兹别克语', 'Northern Uzbek'),
    'vec_Latn': ('威尼斯语', 'Venetian'),
    'vie_Latn': ('越南语', 'Vietnamese'),
    'war_Latn': ('瓦赖语', 'Waray'),
    'wol_Latn': ('沃洛夫语', 'Wolof'),
    'xho_Latn': ('科萨语', 'Xhosa'),
    'ydd_Hebr': ('东意第绪语', 'Eastern Yiddish'),
    'yor_Latn': ('约鲁巴语', 'Yoruba'),
    'yue_Hant': ('粤语', 'Yue Chinese'),
    'zho_Hans': ('中文简体', 'Chinese (Simplified)'),
    'zho_Hant': ('中文繁体', 'Chinese (Traditional)'),
    'zul_Latn': ('祖鲁语', 'Zulu'),
}

# PaddleOCR语言代码到NLLB语言代码的直接映射（基于两个官方语言列表的交集）
PADDLEOCR_TO_NLLB_MAPPING = {
    # 中文
    'ch': 'zho_Hans',                    # 中文 -> 中文简体
    'chinese_cht': 'zho_Hant',           # 中文繁体 -> 中文繁体

    # 主要语言
    'en': 'eng_Latn',                    # 英文 -> 英语
    'japan': 'jpn_Jpan',                 # 日文 -> 日语
    'korean': 'kor_Hang',                # 韩文 -> 韩语
    'fr': 'fra_Latn',                    # 法文 -> 法语
    'de': 'deu_Latn',                    # 德文 -> 德语
    'es': 'spa_Latn',                    # 西班牙文 -> 西班牙语
    'it': 'ita_Latn',                    # 意大利文 -> 意大利语
    'pt': 'por_Latn',                    # 葡萄牙文 -> 葡萄牙语
    'ru': 'rus_Cyrl',                    # 俄文 -> 俄语
    'uk': 'ukr_Cyrl',                    # 乌克兰文 -> 乌克兰语

    # 其他欧洲语言
    'af': 'afr_Latn',                    # 南非荷兰文 -> 南非荷兰语
    'bs': 'bos_Latn',                    # 波斯尼亚文 -> 波斯尼亚语
    'cs': 'ces_Latn',                    # 捷克文 -> 捷克语
    'cy': 'cym_Latn',                    # 威尔士文 -> 威尔士语
    'da': 'dan_Latn',                    # 丹麦文 -> 丹麦语
    'et': 'est_Latn',                    # 爱沙尼亚文 -> 爱沙尼亚语
    'ga': 'gle_Latn',                    # 爱尔兰文 -> 爱尔兰语
    'hr': 'hrv_Latn',                    # 克罗地亚文 -> 克罗地亚语
    'hu': 'hun_Latn',                    # 匈牙利文 -> 匈牙利语
    'is': 'isl_Latn',                    # 冰岛文 -> 冰岛语
    'lt': 'lit_Latn',                    # 立陶宛文 -> 立陶宛语
    'nl': 'nld_Latn',                    # 荷兰文 -> 荷兰语
    'no': 'nob_Latn',                    # 挪威文 -> 书面挪威语
    'pl': 'pol_Latn',                    # 波兰文 -> 波兰语
    'sk': 'slk_Latn',                    # 斯洛伐克文 -> 斯洛伐克语
    'sl': 'slv_Latn',                    # 斯洛文尼亚文 -> 斯洛文尼亚语
    'sq': 'als_Latn',                    # 阿尔巴尼亚文 -> 托斯克阿尔巴尼亚语
    'sv': 'swe_Latn',                    # 瑞典文 -> 瑞典语

    # 亚洲和其他语言
    'id': 'ind_Latn',                    # 印度尼西亚文 -> 印尼语
    'ms': 'zsm_Latn',                    # 马来文 -> 标准马来语
    'mi': 'mri_Latn',                    # 毛利文 -> 毛利语
    'sw': 'swh_Latn',                    # 西瓦希里文 -> 斯瓦希里语
    'tl': 'tgl_Latn',                    # 塔加洛文 -> 他加禄语
    'tr': 'tur_Latn',                    # 土耳其文 -> 土耳其语
    'uz': 'uzn_Latn',                    # 乌兹别克文 -> 北乌兹别克语
    'be': 'bel_Cyrl',                    # 白俄罗斯文 -> 白俄罗斯语

    # 特殊映射
    'rs_latin': 'srp_Cyrl',              # 塞尔维亚文（latin） -> 塞尔维亚语
    'oc': 'oci_Latn',                    # 欧西坦文 -> 奥克语
}


def convert_paddleocr_to_nllb(paddleocr_code: str) -> str:
    """
    将PaddleOCR语言代码转换为NLLB语言代码

    Args:
        paddleocr_code: PaddleOCR语言代码

    Returns:
        对应的NLLB语言代码

    Raises:
        ValueError: 当无法找到对应的NLLB语言代码时
    """
    if paddleocr_code in PADDLEOCR_TO_NLLB_MAPPING:
        return PADDLEOCR_TO_NLLB_MAPPING[paddleocr_code]
    elif paddleocr_code in NLLB_LANGUAGES:
        # 如果已经是NLLB格式，直接返回
        return paddleocr_code
    else:
        supported_codes = list(PADDLEOCR_TO_NLLB_MAPPING.keys())
        raise ValueError(f"无法将PaddleOCR语言代码 '{paddleocr_code}' 转换为NLLB代码。支持的代码: {supported_codes}")


# 真正的拉丁语种（NLLB中标注为"拉丁文"的语言）
LATIN_LANGUAGES = {
    'ace_Latn': ('亚齐语(拉丁文)', 'Acehnese (Latin)'),
    'bjn_Latn': ('班贾尔语(拉丁文)', 'Banjarese (Latin)'),
    'knc_Latn': ('中卡奈姆布语(拉丁文)', 'Central Kanuri (Latin)'),
    'taq_Latn': ('塔马塞特语(拉丁文)', 'Tamasheq (Latin)'),
}


def convert_paddleocr_to_nllb(paddleocr_code: str, specific_latin_lang: str = None) -> str:
    """
    将PaddleOCR语言代码转换为NLLB语言代码

    Args:
        paddleocr_code: PaddleOCR语言代码
        specific_latin_lang: 当paddleocr_code为'la'时，用户指定的具体拉丁语代码

    Returns:
        对应的NLLB语言代码

    Raises:
        ValueError: 当无法找到对应的NLLB语言代码时
    """
    # 特殊处理拉丁语
    if paddleocr_code == 'la':
        if specific_latin_lang:
            if specific_latin_lang in LATIN_LANGUAGES:
                return specific_latin_lang
            else:
                supported_latin_langs = list(LATIN_LANGUAGES.keys())
                raise ValueError(f"不支持的拉丁语代码: {specific_latin_lang}。支持的拉丁语: {supported_latin_langs}")
        else:
            # 如果没有指定具体语言，要求用户必须指定
            supported_latin_langs = list(LATIN_LANGUAGES.keys())
            raise ValueError(f"使用拉丁语时必须指定具体的拉丁语种。支持的拉丁语: {supported_latin_langs}")

    # 常规映射
    if paddleocr_code in PADDLEOCR_TO_NLLB_MAPPING:
        return PADDLEOCR_TO_NLLB_MAPPING[paddleocr_code]
    elif paddleocr_code in NLLB_LANGUAGES:
        # 如果已经是NLLB格式，直接返回
        return paddleocr_code
    else:
        supported_codes = list(PADDLEOCR_TO_NLLB_MAPPING.keys()) + ['la']
        raise ValueError(f"无法将PaddleOCR语言代码 '{paddleocr_code}' 转换为NLLB代码。支持的代码: {supported_codes}")


def is_paddleocr_to_nllb_convertible(paddleocr_code: str) -> bool:
    """
    检查PaddleOCR语言代码是否可以转换为NLLB代码

    Args:
        paddleocr_code: PaddleOCR语言代码

    Returns:
        是否可以转换
    """
    return (paddleocr_code in PADDLEOCR_TO_NLLB_MAPPING or
            paddleocr_code in NLLB_LANGUAGES or
            paddleocr_code == 'la')


def get_latin_languages() -> dict:
    """
    获取支持的拉丁语列表

    Returns:
        拉丁语字典 {code: (zh_name, en_name)}
    """
    return LATIN_LANGUAGES.copy()


class LanguageMapper:
    """语言映射器类"""

    def __init__(self):
        self.paddleocr_languages = PADDLEOCR_LANGUAGES
        self.nllb_languages = NLLB_LANGUAGES
        self.mapping = PADDLEOCR_TO_NLLB_MAPPING

    def get_supported_ocr_languages(self):
        """获取支持的OCR语言列表"""
        return [(code, zh_name, en_name) for code, (zh_name, en_name) in self.paddleocr_languages.items()]

    def is_paddleocr_supported(self, lang: str) -> bool:
        """检查PaddleOCR是否支持指定语言"""
        return lang in self.paddleocr_languages

    def get_paddleocr_code(self, lang: str) -> str:
        """获取PaddleOCR语言代码"""
        return lang if lang in self.paddleocr_languages else "en"

    def get_language_info(self, lang: str) -> dict:
        """获取语言信息"""
        if lang in self.paddleocr_languages:
            zh_name, en_name = self.paddleocr_languages[lang]
            return {"zh_name": zh_name, "en_name": en_name}
        return {"zh_name": f"未知语言({lang})", "en_name": f"Unknown({lang})"}


# 全局语言映射器实例
_language_mapper = None


def get_language_mapper() -> LanguageMapper:
    """获取语言映射器实例（单例模式）"""
    global _language_mapper
    if _language_mapper is None:
        _language_mapper = LanguageMapper()
    return _language_mapper

