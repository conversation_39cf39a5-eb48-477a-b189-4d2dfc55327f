#!/usr/bin/env python3
"""
LLM后端抽象基类

定义统一的LLM调用接口，支持本地模型和API调用
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import time


@dataclass
class CompressionResult:
    """压缩结果数据类"""
    original_text: str
    compressed_text: str
    compression_ratio: float
    processing_time: float
    success: bool
    error_message: Optional[str] = None


class BaseLLMCompressor(ABC):
    """LLM压缩器抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LLM压缩器

        Args:
            config: 配置字典
        """
        self.config = config
    
    @abstractmethod
    def _llm_compress(self, text: str, strategy: str = "simple") -> str:
        """
        使用LLM进行单个文本压缩
        
        Args:
            text: 要压缩的文本
            strategy: 压缩策略
            
        Returns:
            压缩后的文本
        """
        pass
    
    @abstractmethod
    def _llm_compress_batch(self, batch_content: str, strategy: str = "simple") -> str:
        """
        使用LLM进行批量文本压缩
        
        Args:
            batch_content: 格式化的批量内容
            strategy: 压缩策略
            
        Returns:
            LLM输出的压缩结果
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查LLM压缩器是否可用"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass
    
    def compress(self, text: str, target_length: Optional[int] = None,
                strategy: str = "simple") -> CompressionResult:
        """
        压缩文本
        
        Args:
            text: 要压缩的文本
            target_length: 目标长度（字符数），None表示自动压缩
            strategy: 压缩策略
            
        Returns:
            压缩结果
        """
        start_time = time.time()
        
        try:
            # 如果文本已经很短，不需要压缩
            if len(text) <= 10:
                return CompressionResult(
                    original_text=text,
                    compressed_text=text,
                    compression_ratio=1.0,
                    processing_time=time.time() - start_time,
                    success=True
                )
            
            # 使用LLM进行压缩
            compressed_text = self._llm_compress(text, strategy)
            
            # 如果指定了目标长度且压缩后仍然过长，进行截断处理
            if target_length and len(compressed_text) > target_length:
                words = compressed_text.split()
                truncated = ""
                for word in words:
                    if len(truncated + word + " ") <= target_length - 3:
                        truncated += word + " "
                    else:
                        break
                compressed_text = truncated.strip() + "..." if truncated.strip() else compressed_text[:target_length-3] + "..."
            
            # 计算压缩比例
            compression_ratio = len(compressed_text) / len(text) if text else 1.0
            
            return CompressionResult(
                original_text=text,
                compressed_text=compressed_text,
                compression_ratio=compression_ratio,
                processing_time=time.time() - start_time,
                success=True
            )
            
        except Exception as e:
            return CompressionResult(
                original_text=text,
                compressed_text=text,
                compression_ratio=1.0,
                processing_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
    
    def should_compress(self, original_text: str, translated_text: str,
                       threshold: float = 1.5) -> bool:
        """
        判断是否需要压缩
        
        Args:
            original_text: 原始文本
            translated_text: 翻译后文本
            threshold: 长度比例阈值
            
        Returns:
            是否需要压缩
        """
        if not original_text or not translated_text:
            return False
        
        length_ratio = len(translated_text) / len(original_text)
        return length_ratio > threshold
    
    def get_compression_strategies(self) -> Dict[str, str]:
        """获取可用的压缩策略（已弃用，保留兼容性）"""
        return {}

    def compress_batch_with_context(self, text_regions, translated_texts: List[str],
                                   target_length: Optional[int] = None,
                                   strategy: str = "simple") -> List[CompressionResult]:
        """
        批量上下文感知压缩文本

        Args:
            text_regions: TextRegion对象列表，包含位置信息
            translated_texts: 对应的翻译文本列表
            target_length: 目标长度
            strategy: 压缩策略

        Returns:
            压缩结果列表
        """
        import time
        start_time = time.time()

        if not text_regions or not translated_texts:
            return []

        if len(text_regions) != len(translated_texts):
            return []

        try:
            # 格式化批量输入
            batch_content = self._format_batch_context_input(text_regions, translated_texts)

            # 使用LLM进行批量压缩
            compressed_batch = self._llm_compress_batch(batch_content, strategy)

            # 解析批量输出
            compressed_texts = self._parse_batch_context_output(compressed_batch, len(text_regions))

            # 构建结果列表
            results = []
            processing_time = time.time() - start_time

            for i, (original_text, compressed_text) in enumerate(zip(translated_texts, compressed_texts)):
                # 如果解析失败（空字符串），使用原译文
                if not compressed_text or not compressed_text.strip():
                    compressed_text = original_text

                # 如果指定了目标长度且压缩后仍然过长，进行截断处理
                if target_length and len(compressed_text) > target_length:
                    words = compressed_text.split()
                    truncated = ""
                    for word in words:
                        if len(truncated + word + " ") <= target_length - 3:
                            truncated += word + " "
                        else:
                            break
                    compressed_text = truncated.strip() + "..." if truncated.strip() else compressed_text[:target_length-3] + "..."

                # 计算压缩比例
                compression_ratio = len(compressed_text) / len(original_text) if original_text else 1.0

                result = CompressionResult(
                    original_text=original_text,
                    compressed_text=compressed_text,
                    compression_ratio=compression_ratio,
                    processing_time=processing_time / len(text_regions),  # 平均分配处理时间
                    success=bool(compressed_text.strip())  # 只有成功解析才算成功
                )
                results.append(result)

            return results

        except Exception as e:
            # 回退到逐个压缩
            results = []
            for text in translated_texts:
                result = self.compress(text, target_length, strategy)
                results.append(result)
            return results

    def _format_batch_context_input(self, text_regions, translated_texts: List[str]) -> str:
        """
        格式化批量上下文输入

        Args:
            text_regions: TextRegion对象列表
            translated_texts: 翻译文本列表

        Returns:
            格式化的批量输入字符串
        """
        batch_lines = []

        for i, (region, translated_text) in enumerate(zip(text_regions, translated_texts), 1):
            # 获取文本框的四角坐标
            bbox_str = f"[{region.bbox[0]}, {region.bbox[1]}, {region.bbox[2]}, {region.bbox[3]}]"

            # 计算中心点位置描述
            center_x, center_y = region.center
            position_desc = f"坐标{bbox_str}, 中心点({center_x}, {center_y})"

            # 格式化单个文本框信息
            batch_lines.append(f"文本框{i} [位置: {position_desc}]")
            batch_lines.append(f"原文: {region.text}")

            # 检查是否为直接翻译模式（译文与原文相同表示跳过了本地翻译）
            if translated_text == region.text:
                # 直接翻译模式：只提供原文，让LLM直接翻译
                pass  # 不添加译文行
            else:
                # 优化模式：提供机器翻译结果让LLM优化
                batch_lines.append(f"译文: {translated_text}")

            batch_lines.append("")  # 空行分隔

        return "\n".join(batch_lines)

    def _parse_batch_context_output(self, llm_output: str, expected_count: int) -> List[str]:
        """
        解析批量上下文输出

        Args:
            llm_output: LLM的输出文本
            expected_count: 期望的文本框数量

        Returns:
            解析出的压缩文本列表
        """
        compressed_texts = []

        # 查找assistant部分的输出
        if "assistant\n" in llm_output:
            # 提取assistant后的内容
            assistant_output = llm_output.split("assistant\n", 1)[-1].strip()
        else:
            # 如果没有找到assistant标记，使用整个输出
            assistant_output = llm_output.strip()

        # 按行分割输出
        lines = assistant_output.split('\n')

        # 解析每个文本框的结果
        for i in range(1, expected_count + 1):
            found = False
            for line in lines:
                line = line.strip()
                # 匹配格式：文本框N: 内容
                if line.startswith(f"文本框{i}:") or line.startswith(f"文本框{i}："):
                    # 提取冒号后的内容
                    if ":" in line:
                        content = line.split(":", 1)[-1].strip()
                    else:
                        content = line.split("：", 1)[-1].strip()
                    compressed_texts.append(content)
                    found = True
                    break

            if not found:
                # 如果没有找到对应的文本框结果，使用空字符串
                compressed_texts.append("")

        # 如果解析的数量不匹配，补齐或截断
        while len(compressed_texts) < expected_count:
            compressed_texts.append("")

        if len(compressed_texts) > expected_count:
            compressed_texts = compressed_texts[:expected_count]

        return compressed_texts
