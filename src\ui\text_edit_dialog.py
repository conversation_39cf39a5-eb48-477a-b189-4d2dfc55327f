#!/usr/bin/env python3
"""
文本编辑对话框模块 - 支持文本编辑和Markdown预览

提供专业的文本编辑体验，包括：
- 双标签页设计（编辑/预览）
- 实时Markdown预览
- 主题样式适配
- 等宽字体编辑器
"""

import sys
from pathlib import Path
from typing import Callable, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QWidget, QLabel, QPushButton, QTextEdit, QTextBrowser,
    QMessageBox, QSizePolicy, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QFontMetrics

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    import markdown
except ImportError:
    markdown = None

from .theme_manager import get_theme_manager


class TextEditDialog(QDialog):
    """文本编辑对话框 - 支持编辑和Markdown预览"""
    
    # 信号
    textSaved = pyqtSignal(str)  # 文本保存信号
    
    def __init__(self, parent=None, title="文本编辑器", initial_text="", 
                 config_key="", save_callback: Optional[Callable[[str], None]] = None):
        """
        初始化文本编辑对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            initial_text: 初始文本内容
            config_key: 配置键名（用于显示）
            save_callback: 保存回调函数
        """
        super().__init__(parent)
        self.title = title
        self.initial_text = initial_text
        self.config_key = config_key
        self.save_callback = save_callback
        self.theme_manager = get_theme_manager()
        
        # 文本内容
        self.current_text = initial_text
        self.text_changed = False
        
        self.init_ui()
        self.setup_connections()
        self.apply_theme()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(self.title)
        self.setModal(True)
        self.resize(900, 700)  # 更大的窗口以适应双标签页
        
        # 设置最小尺寸
        self.setMinimumSize(600, 400)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        self.create_header_area(layout)
        
        # 标签页区域
        self.create_tab_area(layout)
        
        # 按钮区域
        self.create_button_area(layout)
        
    def create_header_area(self, layout):
        """创建标题区域"""
        if self.config_key:
            self.header_label = QLabel(f"编辑配置项: {self.config_key}")
            self.header_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")
            layout.addWidget(self.header_label)
        else:
            self.header_label = None
            
    def create_tab_area(self, layout):
        """创建标签页区域"""
        self.tab_widget = QTabWidget()
        
        # 编辑标签页
        self.edit_tab = self.create_edit_tab()
        self.tab_widget.addTab(self.edit_tab, "📝 编辑")
        
        # 预览标签页
        self.preview_tab = self.create_preview_tab()
        self.tab_widget.addTab(self.preview_tab, "👁️ 预览")
        
        layout.addWidget(self.tab_widget)
        
    def create_edit_tab(self):
        """创建编辑标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 编辑器说明
        self.edit_info_label = QLabel("💡 提示：支持Markdown语法，切换到预览标签页查看渲染效果")
        self.edit_info_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        layout.addWidget(self.edit_info_label)
        
        # 文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setPlainText(self.initial_text)
        
        # 设置等宽字体
        font = QFont("Consolas", 11)
        if not font.exactMatch():
            font = QFont("Monaco", 11)
        if not font.exactMatch():
            font = QFont("Courier New", 11)
        font.setFixedPitch(True)
        self.text_edit.setFont(font)
        
        # 设置编辑器属性
        self.text_edit.setLineWrapMode(QTextEdit.LineWrapMode.WidgetWidth)
        self.text_edit.setAcceptRichText(False)  # 只接受纯文本
        
        layout.addWidget(self.text_edit)
        
        return widget
        
    def create_preview_tab(self):
        """创建预览标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 预览器说明
        self.preview_info_label = QLabel("📖 Markdown预览 - 实时渲染编辑器中的内容")
        self.preview_info_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        layout.addWidget(self.preview_info_label)
        
        # 预览浏览器
        self.preview_browser = QTextBrowser()
        self.preview_browser.setOpenExternalLinks(False)  # 不打开外部链接
        
        layout.addWidget(self.preview_browser)
        
        return widget
        
    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 左侧：重置按钮
        self.reset_btn = QPushButton("🔄 重置")
        self.reset_btn.setToolTip("恢复到初始内容")
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()  # 弹性空间
        
        # 右侧：取消和保存按钮
        self.cancel_btn = QPushButton("❌ 取消")
        self.save_btn = QPushButton("💾 保存")
        
        # 设置按钮样式
        self.save_btn.setDefault(True)  # 默认按钮
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        # 文本变化检测
        self.text_edit.textChanged.connect(self.on_text_changed)
        
        # 标签页切换
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        # 按钮事件
        self.reset_btn.clicked.connect(self.reset_text)
        self.cancel_btn.clicked.connect(self.reject)
        self.save_btn.clicked.connect(self.save_text)
        
    def on_text_changed(self):
        """处理文本变化"""
        self.current_text = self.text_edit.toPlainText()
        self.text_changed = (self.current_text != self.initial_text)
        
        # 更新窗口标题显示变化状态
        title = self.title
        if self.text_changed:
            title += " *"
        self.setWindowTitle(title)
        
    def on_tab_changed(self, index):
        """处理标签页切换"""
        if index == 1:  # 切换到预览标签页
            self.update_preview()
            
    def update_preview(self):
        """更新Markdown预览"""
        text = self.text_edit.toPlainText()

        if markdown is None:
            # 如果没有markdown库，显示纯文本
            self.preview_browser.setPlainText(text)
            return

        try:
            # 预处理文本：处理换行问题
            processed_text = self.preprocess_text_for_markdown(text)

            # 渲染Markdown
            html = markdown.markdown(processed_text, extensions=['extra', 'codehilite', 'nl2br'])

            # 获取当前主题样式
            styled_html = self.get_themed_html(html)

            self.preview_browser.setHtml(styled_html)

        except Exception as e:
            # 渲染失败时显示错误信息
            error_html = f"""
            <div style="color: #e74c3c; padding: 10px; border: 1px solid #e74c3c; border-radius: 4px;">
                <strong>Markdown渲染失败:</strong><br>
                {str(e)}<br><br>
                <strong>原始文本:</strong><br>
                <pre style="background: #f8f9fa; padding: 10px; margin-top: 10px; white-space: pre-wrap;">{text}</pre>
            </div>
            """
            self.preview_browser.setHtml(error_html)

    def preprocess_text_for_markdown(self, text):
        """预处理文本以改善Markdown渲染效果"""
        lines = text.split('\n')
        processed_lines = []

        for i, line in enumerate(lines):
            stripped_line = line.strip()

            # 如果是空行，保持空行
            if not stripped_line:
                processed_lines.append('')
                continue

            # 检查是否是列表项
            is_list_item = (stripped_line.startswith(('- ', '* ', '+ ')) or
                          (len(stripped_line) > 2 and stripped_line[0].isdigit() and
                           stripped_line[1:3] in ['. ', ') ']))

            # 检查是否是标题
            is_heading = stripped_line.startswith('#')

            # 检查是否是代码块
            is_code_block = stripped_line.startswith('```')

            # 如果当前行不是特殊格式，且下一行也不是空行或特殊格式
            # 则在行末添加两个空格以保持换行
            if not (is_list_item or is_heading or is_code_block):
                next_line = lines[i + 1].strip() if i + 1 < len(lines) else ''
                next_is_special = (next_line.startswith(('#', '- ', '* ', '+ ', '```')) or
                                 not next_line or
                                 (len(next_line) > 2 and next_line[0].isdigit() and
                                  next_line[1:3] in ['. ', ') ']))

                if not next_is_special:
                    # 在行末添加两个空格以强制换行
                    processed_lines.append(line.rstrip() + '  ')
                else:
                    processed_lines.append(line)
            else:
                processed_lines.append(line)

        return '\n'.join(processed_lines)

    def get_themed_html(self, html_content):
        """根据当前主题生成带样式的HTML"""
        try:
            from src.utils.config import get_config
            config = get_config()
            theme_name = config.get('ui.theme', 'light')

            if theme_name == 'dark':
                return self.get_dark_themed_html(html_content)
            else:
                return self.get_light_themed_html(html_content)
        except Exception:
            # 如果获取主题失败，使用light主题
            return self.get_light_themed_html(html_content)

    def get_light_themed_html(self, html_content):
        """获取light主题的HTML样式"""
        return f"""
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.8;
                color: #333;
                background-color: #ffffff;
                max-width: none;
                margin: 0;
                padding: 10px;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #2c3e50;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }}
            p {{
                margin-bottom: 1em;
                line-height: 1.8;
            }}
            code {{
                background-color: #f8f9fa;
                color: #e83e8c;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            }}
            pre {{
                background-color: #f8f9fa;
                color: #333;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                white-space: pre-wrap;
                border: 1px solid #e9ecef;
            }}
            blockquote {{
                border-left: 4px solid #3498db;
                margin: 0;
                padding-left: 15px;
                color: #666;
                background-color: #f8f9fa;
                padding: 10px 15px;
                border-radius: 0 4px 4px 0;
            }}
            ul, ol {{
                margin-left: 20px;
                margin-bottom: 1em;
            }}
            li {{
                margin-bottom: 8px;
                line-height: 1.6;
            }}
            br {{
                line-height: 1.8;
            }}
            strong {{
                color: #2c3e50;
            }}
        </style>
        {html_content}
        """

    def get_dark_themed_html(self, html_content):
        """获取dark主题的HTML样式"""
        return f"""
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.8;
                color: #e0e0e0;
                background-color: #2b2b2b;
                max-width: none;
                margin: 0;
                padding: 10px;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #4fc3f7;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }}
            p {{
                margin-bottom: 1em;
                line-height: 1.8;
            }}
            code {{
                background-color: #404040;
                color: #ff9800;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            }}
            pre {{
                background-color: #1e1e1e;
                color: #e0e0e0;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                white-space: pre-wrap;
                border: 1px solid #555555;
            }}
            blockquote {{
                border-left: 4px solid #4fc3f7;
                margin: 0;
                padding-left: 15px;
                color: #b0b0b0;
                background-color: #353535;
                padding: 10px 15px;
                border-radius: 0 4px 4px 0;
            }}
            ul, ol {{
                margin-left: 20px;
                margin-bottom: 1em;
            }}
            li {{
                margin-bottom: 8px;
                line-height: 1.6;
            }}
            br {{
                line-height: 1.8;
            }}
            strong {{
                color: #81c784;
            }}
        </style>
        {html_content}
        """

    def reset_text(self):
        """重置文本到初始状态"""
        if self.text_changed:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("确认重置")
            msg_box.setText("确定要重置到初始内容吗？当前的修改将会丢失。")
            msg_box.setIcon(QMessageBox.Icon.Question)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.No)

            # 应用当前主题的弹窗样式
            styles = self.theme_manager.get_theme_styles()
            msg_box.setStyleSheet(styles.get('message_box', ''))

            reply = msg_box.exec()

            if reply == QMessageBox.StandardButton.Yes:
                self.text_edit.setPlainText(self.initial_text)
                
    def save_text(self):
        """保存文本"""
        text = self.text_edit.toPlainText()
        
        try:
            if self.save_callback:
                self.save_callback(text)
                
            # 发送保存信号
            self.textSaved.emit(text)
            
            # 显示保存成功消息
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("保存成功")
            msg_box.setText(f"配置项 '{self.config_key}' 已成功保存！")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)

            # 应用当前主题的弹窗样式
            styles = self.theme_manager.get_theme_styles()
            msg_box.setStyleSheet(styles.get('message_box', ''))

            msg_box.exec()
            
            self.accept()
            
        except Exception as e:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("保存失败")
            msg_box.setText(f"保存配置时发生错误：\n{str(e)}")
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)

            # 应用当前主题的弹窗样式
            styles = self.theme_manager.get_theme_styles()
            msg_box.setStyleSheet(styles.get('message_box', ''))

            msg_box.exec()
            
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        if self.text_changed:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("确认关闭")
            msg_box.setText("有未保存的修改，确定要关闭吗？")
            msg_box.setIcon(QMessageBox.Icon.Question)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Yes)  # Yes按钮为默认（蓝色）

            # 应用当前主题的弹窗样式
            styles = self.theme_manager.get_theme_styles()
            msg_box.setStyleSheet(styles.get('message_box', ''))

            reply = msg_box.exec()

            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return

        event.accept()
        
    def apply_theme(self):
        """应用主题样式"""
        try:
            from src.utils.config import get_config
            config = get_config()
            theme_name = config.get('ui.theme', 'light')
            styles = self.theme_manager.get_theme_styles(theme_name)
            
            # 应用主窗口样式
            self.setStyleSheet(styles.get('main_window', ''))
            
            # 应用标签页样式
            tab_style = styles.get('tab_widget', '')
            if tab_style:
                self.tab_widget.setStyleSheet(tab_style)
                
            # 应用文本编辑器样式
            edit_style = styles.get('text_edit', '')
            if edit_style:
                self.text_edit.setStyleSheet(edit_style)

            # 应用预览浏览器样式
            self.apply_preview_browser_theme(theme_name)

            # 应用提示标签样式
            self.apply_info_labels_theme(theme_name)

            # 应用按钮样式
            button_style = styles.get('button', '')
            if button_style:
                for btn in [self.reset_btn, self.cancel_btn, self.save_btn]:
                    btn.setStyleSheet(button_style)
                    
        except Exception as e:
            # 主题应用失败时使用默认样式
            print(f"应用主题失败: {e}")

    def apply_preview_browser_theme(self, theme_name):
        """为预览浏览器应用主题样式"""
        if theme_name == 'dark':
            self.preview_browser.setStyleSheet("""
                QTextBrowser {
                    background-color: #2b2b2b;
                    color: #e0e0e0;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 10px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 13px;
                    line-height: 1.6;
                    selection-background-color: #4285f4;
                    selection-color: white;
                }
                QScrollBar:vertical {
                    background-color: #404040;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background-color: #666666;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background-color: #777777;
                }
            """)
        else:
            self.preview_browser.setStyleSheet("""
                QTextBrowser {
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 10px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 13px;
                    line-height: 1.6;
                    selection-background-color: #4285f4;
                    selection-color: white;
                }
                QScrollBar:vertical {
                    background-color: #f1f1f1;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background-color: #c1c1c1;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background-color: #a8a8a8;
                }
            """)

        # 更新预览内容以应用新主题
        if hasattr(self, 'text_edit'):
            self.update_preview()

    def apply_info_labels_theme(self, theme_name):
        """为提示标签应用主题样式"""
        if theme_name == 'dark':
            # Dark主题：使用更亮的颜色以提高可读性
            info_style = "color: #b0b0b0; font-size: 12px; padding: 5px;"
            header_style = "font-weight: bold; font-size: 14px; color: #e0e0e0;"
        else:
            # Light主题：使用原来的颜色
            info_style = "color: #666; font-size: 12px; padding: 5px;"
            header_style = "font-weight: bold; font-size: 14px; color: #333;"

        # 应用样式到标题标签
        if hasattr(self, 'header_label') and self.header_label:
            self.header_label.setStyleSheet(header_style)

        # 应用样式到编辑器提示标签
        if hasattr(self, 'edit_info_label'):
            self.edit_info_label.setStyleSheet(info_style)

        # 应用样式到预览器提示标签
        if hasattr(self, 'preview_info_label'):
            self.preview_info_label.setStyleSheet(info_style)
