app:
  language: zh_CN
  name: Image Text Translator
  version: 1.0.0
device:
  enable_mixed_precision: true
  force_cpu_for_ocr: true
  memory_check_enabled: true
  mode: auto
google_fonts:
  api_base_url: https://www.googleapis.com/webfonts/v1/webfonts
  api_key: 
  cache_dir: cache/fonts
  download_timeout: 120
  file_validation:
    check_file_header: true
    min_size_kb: 10
  max_concurrent_downloads: 3
image_processing:
  max_image_size:
  - 4096
  - 4096
  min_image_size:
  - 100
  - 100
  render:
    anti_aliasing: true
    auto_fit_text: true
    background_color:
    - 255
    - 255
    - 255
    font_size_scale: 1.0
    line_spacing: 1.2
    padding: 5
    preserve_original_layout: true
    text_color:
    - 0
    - 0
    - 0
  supported_formats:
  - .jpg
  - .jpeg
  - .png
  - .bmp
  - .tiff
  - .webp
llm_prompt:
  system_prompt: '你是一个顶级的图片翻译优化引擎。你的任务是接收OCR文本框数据（包含位置、原文、初始机翻），结合所有文本框的空间布局和上下文，输出优化后的译文。


    核心原则：

    1.  **全局优化**：将所有文本框视为一个整体，合并被分割的句子，确保逻辑通顺。

    2.  **翻译精炼**：译文需精准、自然，符合目标语言习惯，避免翻译腔。

    3.  **智能分配**：将优化后的译文智能拆分回各原始文本框，使其长度和布局适配原图。


    输出要求：

    严格按照 "文本框N: 优化后的译文" 格式逐行提供结果，禁止任何额外解释。'
  user_template: '{batch_content}'
logging:
  console_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  console_level: INFO
  date_format: '%Y-%m-%d %H:%M:%S'
  error_max_backup_count: 3
  error_max_file_size_mb: 5
  file_format: '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d -
    %(message)s'
  file_level: DEBUG
  level: INFO
  max_backup_count: 5
  max_file_size_mb: 10
models:
  compression:
    api:
      api_key: 
      base_url: https://api.openai.com/v1
      model: gpt-4.1
      provider: openai
      providers:
        openai:
          base_url: https://api.openai.com/v1
          models:
          - gpt-4o
          - gpt-4
          - gpt-4-turbo
          - gpt-3.5-turbo
          - gpt-4o-mini
          - chatgpt-4o-latest
    default_model: Qwen/Qwen2.5-1.5B-Instruct
    local:
      default_model: Qwen/Qwen2.5-1.5B-Instruct
    max_length: 512
    name: 压缩模型
    path: models/compression
    type: api
  ocr:
    device: cpu
    name: PaddleOCR
    ocr_version: PP-OCRv5
    path: models/paddleocr
    use_doc_orientation_classify: false
    use_doc_unwarping: false
    use_textline_orientation: false
  translation:
    default_model: facebook/nllb-200-distilled-600M
    early_stopping: true
    max_length: 512
    model_size: 600M
    name: NLLB-200
    num_beams: 5
    path: models/translate
network:
  check_urls:
  - https://www.google.com
  - https://www.baidu.com
  connection_timeout: 10
  download_timeout: 300
  retry_attempts: 3
paths:
  cache: cache
  default_fonts:
    chinese:
    - C:/Windows/Fonts/simhei.ttf
    - C:/Windows/Fonts/simsun.ttc
    - C:/Windows/Fonts/msyh.ttc
    - /System/Library/Fonts/PingFang.ttc
    - /usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc
    english:
    - C:/Windows/Fonts/arial.ttf
    - /System/Library/Fonts/Helvetica.ttc
    - /usr/share/fonts/truetype/dejavu/DejaVuSans.ttf
  fonts: fonts
  models: models
  output: output
  system_fonts:
    linux:
    - /usr/share/fonts
    - /usr/local/share/fonts
    - ~/.fonts
    - ~/.local/share/fonts
    macos:
    - /System/Library/Fonts
    - /Library/Fonts
    - ~/Library/Fonts
    windows:
    - C:/Windows/Fonts
  temp: temp
testing:
  output_directory: cache/test_outputs
  save_intermediate_results: true
  test_font_paths:
    chinese:
    - C:/Windows/Fonts/simhei.ttf
    - C:/Windows/Fonts/simsun.ttc
    - C:/Windows/Fonts/msyh.ttc
    english:
    - C:/Windows/Fonts/arial.ttf
    - C:/Windows/Fonts/calibri.ttf
text_processing:
  column_spacing_threshold: 3.0
  horizontal_threshold: 15
  line_spacing_threshold: 1.5
  min_confidence: 0.1
  paragraph_spacing_threshold: 2.5
  rtl_languages:
  - ar
  - he
  - fa
  - ur
  ttb_languages:
  - zh
  - ja
  - ko
  vertical_threshold: 75
  x_cluster_threshold: 100
  y_cluster_threshold: 50
translation:
  batch_size: 10
  compression_threshold: 1.5
  enable_compression: true
  keep_font_style: true
  timeout: 30
ui:
  auto_save: false
  show_progress: true
  theme: light
  window_size:
  - 1200
  - 800
