#!/usr/bin/env python3
"""
API LLM后端实现

基于OpenAI SDK的统一API调用，支持OpenAI兼容的API服务
"""

import os
import time
from typing import Dict, Any, Optional

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from .base_llm import BaseLLMCompressor
from ...utils.logger import get_logger


class APILLMCompressor(BaseLLMCompressor):
    """API LLM压缩器实现"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化API LLM压缩器
        
        Args:
            config: 配置字典，包含API相关配置
        """
        super().__init__(config)
        
        self.logger = get_logger()
        
        # 获取API配置
        api_config = config.get('models', {}).get('compression', {}).get('api', {})
        
        self.provider = api_config.get('provider', 'openai')
        self.base_url = api_config.get('base_url', 'https://api.openai.com/v1')
        self.api_key = self._resolve_api_key(api_config.get('api_key', ''))
        self.model = api_config.get('model', 'gpt-4o')
        
        # 验证配置
        self._validate_config()
        
        # 初始化OpenAI客户端
        self.client = None
        self._init_client()
    
    def _resolve_api_key(self, api_key: str) -> str:
        """
        解析API密钥，支持环境变量
        
        Args:
            api_key: API密钥字符串，可能包含环境变量引用
            
        Returns:
            解析后的API密钥
        """
        if api_key.startswith('${') and api_key.endswith('}'):
            # 环境变量格式: ${ENV_VAR_NAME}
            env_var = api_key[2:-1]
            resolved_key = os.getenv(env_var)
            if not resolved_key:
                self.logger.warning(f"环境变量 {env_var} 未设置")
                return ""
            return resolved_key
        return api_key
    
    def _validate_config(self):
        """验证API配置"""
        if not OPENAI_AVAILABLE:
            raise ImportError("openai库不可用，无法使用API LLM压缩功能。请安装: pip install openai")
        
        if not self.api_key:
            raise ValueError(f"API密钥未配置，请设置 {self.provider} 的API密钥")
        
        if not self.base_url:
            raise ValueError("API端点URL未配置")
        
        if not self.model:
            raise ValueError("模型名称未配置")
    
    def _init_client(self):
        """初始化OpenAI客户端"""
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            self.logger.info(f"✅ API客户端初始化成功: {self.provider} ({self.model})")
        except Exception as e:
            self.logger.error(f"❌ API客户端初始化失败: {e}")
            raise
    
    def _llm_compress(self, text: str, strategy: str = "simple") -> str:
        """
        使用API LLM进行文本压缩

        Args:
            text: 要压缩的文本
            strategy: 压缩策略（已弃用，保留参数兼容性）

        Returns:
            压缩后的文本
        """
        try:
            # 获取固定的提示词模板
            llm_prompt = self.config.get('llm_prompt', {})
            system_prompt = llm_prompt.get('system_prompt', '')
            user_template = llm_prompt.get('user_template', '')

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_template.format(text=text)}
            ]
            
            # 调用API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=512,
                temperature=0.7,
                top_p=0.9
            )
            
            # 提取回复
            compressed_text = response.choices[0].message.content.strip()
            
            # 如果输出为空或过短，返回原文
            if not compressed_text or len(compressed_text) < 3:
                self.logger.warning(f"API LLM压缩输出过短，返回原文: '{compressed_text}'")
                return text
            
            return compressed_text
            
        except Exception as e:
            self.logger.error(f"API LLM文本压缩失败: {e}")
            raise
    
    def _llm_compress_batch(self, batch_content: str, strategy: str = "simple") -> str:
        """
        使用API LLM进行批量压缩

        Args:
            batch_content: 格式化的批量内容
            strategy: 压缩策略（已弃用，保留参数兼容性）

        Returns:
            LLM输出的压缩结果
        """
        try:
            # 获取固定的提示词模板
            llm_prompt = self.config.get('llm_prompt', {})
            system_prompt = llm_prompt.get('system_prompt', '')
            user_template = llm_prompt.get('user_template', '')

            # 格式化用户消息
            user_content = user_template.format(batch_content=batch_content)

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 详细的API调用日志
            self.logger.info(f"🚀 开始API调用: {self.provider.upper()} {self.model}")
            self.logger.info(f"📡 API端点: {self.base_url}")
            self.logger.info(f"📝 输入文本长度: {len(batch_content)} 字符")

            # 输出完整提示词内容
            self.logger.info("📋 完整提示词内容:")
            self.logger.info("=" * 80)
            self.logger.info(f"🔹 System Prompt:")
            self.logger.info(f"{system_prompt}")
            self.logger.info("-" * 80)
            self.logger.info(f"🔹 User Prompt:")
            self.logger.info(f"{user_content}")
            self.logger.info("=" * 80)

            # 调用API
            self.logger.info("📤 正在发送API请求...")
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=2048,
                temperature=0.7,
                top_p=0.9
            )

            # 提取回复
            compressed_text = response.choices[0].message.content.strip()

            # API调用成功日志
            self.logger.info(f"📥 API响应成功，输出长度: {len(compressed_text)} 字符")
            self.logger.info(f"💰 Token使用情况: {getattr(response, 'usage', '未知')}")
            self.logger.info(f"✅ {self.provider.upper()} API调用完成")

            return compressed_text

        except Exception as e:
            self.logger.error(f"❌ {self.provider.upper()} API调用失败: {e}")
            self.logger.error("🔧 请检查API密钥、网络连接和模型权限")
            raise
    
    def is_available(self) -> bool:
        """检查API LLM压缩器是否可用"""
        return OPENAI_AVAILABLE and self.client is not None
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": self.provider,
            "model": self.model,
            "base_url": self.base_url,
            "available": self.is_available(),
            "type": "api_llm",
            "strategies": list(self.compression_strategies.keys())
        }
