#!/usr/bin/env python3
"""
图像渲染引擎模块 - 文本渲染到图像

提供文本渲染、布局优化和图像合成功能
"""

import os
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import math

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import get_config
from .text_region import TextRegion
from .text_processor import TextBlock, TextLayout
from .font_matcher import FontInfo, FontMatchResult
from .translation_pipeline import TranslationPipelineResult


@dataclass
class RenderConfig:
    """渲染配置"""
    background_color: Tuple[int, int, int] = (255, 255, 255)  # 背景颜色 (RGB)
    text_color: Tuple[int, int, int] = (0, 0, 0)              # 文本颜色 (RGB)
    font_size_scale: float = 1.0                              # 字体大小缩放
    line_spacing: float = 1.2                                 # 行间距
    padding: int = 5                                          # 内边距
    anti_aliasing: bool = True                                # 抗锯齿
    preserve_original_layout: bool = True                     # 保持原始布局
    auto_fit_text: bool = True                                # 自动适应文本


@dataclass
class RenderResult:
    """渲染结果"""
    rendered_image: np.ndarray   # 渲染后的图像
    success: bool               # 是否成功
    render_time: float          # 渲染时间
    error_message: str = ""     # 错误信息


class ImageRenderer:
    """图像渲染器类"""
    
    def __init__(self, config: Optional[RenderConfig] = None):
        """
        初始化图像渲染器
        
        Args:
            config: 渲染配置
        """
        self.logger = get_logger()
        self.app_config = get_config()
        
        # 使用默认配置或传入的配置
        self.config = config or RenderConfig()
        
        self.logger.info("图像渲染器初始化完成")
    
    def render_text_on_image(self, original_image: np.ndarray,
                           text_regions: List[TextRegion],
                           translation_results: List[TranslationPipelineResult],
                           font_matches: List[FontMatchResult]) -> RenderResult:
        """
        在图像上渲染翻译后的文本
        
        Args:
            original_image: 原始图像
            text_regions: 原始文本区域
            translation_results: 翻译结果
            font_matches: 字体匹配结果
            
        Returns:
            渲染结果
        """
        import time
        start_time = time.time()
        
        try:
            if not PIL_AVAILABLE:
                raise ImportError("PIL不可用，无法进行文本渲染")
            
            # 转换图像格式
            if len(original_image.shape) == 3:
                pil_image = Image.fromarray(original_image)
            else:
                pil_image = Image.fromarray(cv2.cvtColor(original_image, cv2.COLOR_GRAY2RGB))
            
            # 创建绘图对象
            draw = ImageDraw.Draw(pil_image)
            
            # 渲染每个文本区域
            for i, (region, translation, font_match) in enumerate(zip(text_regions, translation_results, font_matches)):
                if not translation.success:
                    continue

                # 检测该区域的背景颜色
                bg_color = self._detect_background_color(original_image, region.bbox)
                self.logger.debug(f"检测到区域{i+1}背景色: {bg_color}")

                # 用背景色精确填充OCR检测的区域
                self._fill_region(draw, region.bbox, bg_color)

                # 获取最终文本
                final_text = translation.compressed_text if translation.compressed_text else translation.translated_text

                # 在相同区域内渲染译文
                self._render_single_text(draw, region, final_text, font_match)
            
            # 转换回numpy数组
            rendered_array = np.array(pil_image)
            
            render_time = time.time() - start_time
            
            return RenderResult(
                rendered_image=rendered_array,
                success=True,
                render_time=render_time
            )
            
        except Exception as e:
            self.logger.error(f"图像渲染失败: {e}")
            return RenderResult(
                rendered_image=original_image,
                success=False,
                render_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _render_single_text(self, draw: ImageDraw.Draw, region: TextRegion,
                          text: str, font_match: FontMatchResult):
        """
        渲染单个文本区域
        
        Args:
            draw: PIL绘图对象
            region: 文本区域
            text: 要渲染的文本
            font_match: 字体匹配结果
        """
        try:
            # 计算文本区域的边界框
            bbox = region.bbox
            if len(bbox) != 4:
                return
            
            # 获取区域坐标
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            region_width = x_max - x_min
            region_height = y_max - y_min
            
            # 首先用背景色填充原始文本区域
            self._fill_region(draw, bbox, self.config.background_color)
            
            # 加载字体
            font = self._load_font(font_match.font_info, region_height)
            
            # 计算文本尺寸
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # 自动调整字体大小以适应区域
            if self.config.auto_fit_text and text_width > region_width:
                scale_factor = (region_width - 2 * self.config.padding) / text_width
                new_font_size = max(8, int(font_match.font_info.size * scale_factor))
                font = self._load_font(font_match.font_info, new_font_size)
                
                # 重新计算文本尺寸
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
            
            # 计算文本位置（居中对齐）
            text_x = x_min + (region_width - text_width) // 2
            text_y = y_min + (region_height - text_height) // 2
            
            # 确保文本在区域内
            text_x = max(x_min + self.config.padding, text_x)
            text_y = max(y_min + self.config.padding, text_y)
            
            # 渲染文本
            draw.text(
                (text_x, text_y),
                text,
                font=font,
                fill=self.config.text_color
            )
            
            self.logger.debug(f"渲染文本: '{text}' 在位置 ({text_x}, {text_y})")
            
        except Exception as e:
            self.logger.error(f"渲染单个文本失败: {e}")
    
    def _detect_background_color(self, image: np.ndarray, bbox: List[List[int]]) -> Tuple[int, int, int]:
        """
        检测文本区域周围的背景颜色

        Args:
            image: 原始图像
            bbox: 文本边界框

        Returns:
            背景颜色RGB值
        """
        try:
            # 获取边界框坐标
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]

            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)

            # 扩展采样区域到文本周围
            padding = 5
            sample_x_min = max(0, x_min - padding)
            sample_y_min = max(0, y_min - padding)
            sample_x_max = min(image.shape[1], x_max + padding)
            sample_y_max = min(image.shape[0], y_max + padding)

            # 采样文本周围的像素
            background_samples = []

            # 上边缘
            if sample_y_min < y_min:
                background_samples.extend(image[sample_y_min:y_min, sample_x_min:sample_x_max].reshape(-1, 3))

            # 下边缘
            if sample_y_max > y_max:
                background_samples.extend(image[y_max:sample_y_max, sample_x_min:sample_x_max].reshape(-1, 3))

            # 左边缘
            if sample_x_min < x_min:
                background_samples.extend(image[sample_y_min:sample_y_max, sample_x_min:x_min].reshape(-1, 3))

            # 右边缘
            if sample_x_max > x_max:
                background_samples.extend(image[sample_y_min:sample_y_max, x_max:sample_x_max].reshape(-1, 3))

            if background_samples:
                background_samples = np.array(background_samples)
                # 计算中位数颜色（比平均值更稳定）
                bg_color = np.median(background_samples, axis=0).astype(int)
                return tuple(bg_color)
            else:
                # 如果无法检测，返回白色
                return (255, 255, 255)

        except Exception as e:
            self.logger.debug(f"背景色检测失败: {e}")
            return (255, 255, 255)

    def _fill_region(self, draw: ImageDraw.Draw, bbox: List[List[int]], color: Tuple[int, int, int]):
        """
        精确填充Paddle OCR检测的文本区域

        Args:
            draw: PIL绘图对象
            bbox: Paddle OCR检测的精确边界框坐标
            color: 背景填充颜色
        """
        try:
            # 转换为PIL多边形坐标
            points = [(point[0], point[1]) for point in bbox]

            # 精确填充OCR检测的区域
            draw.polygon(points, fill=color, outline=color)

            # 为了确保完全覆盖，再填充一次稍微扩展的区域
            expanded_points = []
            for point in bbox:
                expanded_points.append((point[0] - 1, point[1] - 1))
                expanded_points.append((point[0] + 1, point[1] + 1))

            if len(expanded_points) >= 6:
                draw.polygon(expanded_points[:len(bbox)*2], fill=color, outline=color)

        except Exception as e:
            self.logger.debug(f"填充区域失败: {e}")
    
    def _load_font(self, font_info: FontInfo, size: int) -> ImageFont.FreeTypeFont:
        """
        加载字体

        Args:
            font_info: 字体信息
            size: 字体大小

        Returns:
            PIL字体对象
        """
        try:
            # 应用字体大小缩放
            scaled_size = int(size * self.config.font_size_scale)

            # 尝试加载指定字体
            if font_info and font_info.path and os.path.exists(font_info.path):
                font = ImageFont.truetype(font_info.path, scaled_size)
                # 测试字体是否支持中文
                if self._test_font_chinese_support(font):
                    return font

            # 如果指定字体不支持中文，尝试加载中文字体
            chinese_font = self._load_chinese_font(scaled_size)
            if chinese_font:
                return chinese_font

            # 回退到默认字体
            return ImageFont.load_default()

        except Exception as e:
            self.logger.debug(f"加载字体失败: {e}")
            return ImageFont.load_default()

    def _test_font_chinese_support(self, font: ImageFont.FreeTypeFont) -> bool:
        """
        测试字体是否支持中文

        Args:
            font: PIL字体对象

        Returns:
            是否支持中文
        """
        try:
            # 测试常见中文字符
            test_chars = ['中', '文', '测', '试']
            for char in test_chars:
                bbox = font.getbbox(char)
                if bbox[2] - bbox[0] <= 0:  # 宽度为0说明不支持
                    return False
            return True
        except Exception:
            return False

    def _load_chinese_font(self, size: int) -> Optional[ImageFont.FreeTypeFont]:
        """
        加载中文字体

        Args:
            size: 字体大小

        Returns:
            中文字体对象
        """
        # 常见的中文字体路径
        chinese_fonts = [
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/simkai.ttf",      # 楷体
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",  # Linux
        ]

        for font_path in chinese_fonts:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, size)
                    if self._test_font_chinese_support(font):
                        self.logger.debug(f"使用中文字体: {font_path}")
                        return font
            except Exception as e:
                self.logger.debug(f"加载中文字体失败 {font_path}: {e}")

        return None
    
    def render_layout(self, original_image: np.ndarray,
                     layout: TextLayout,
                     translation_results: Dict[str, Any],
                     font_matches: Dict[str, FontMatchResult]) -> RenderResult:
        """
        渲染文本布局
        
        Args:
            original_image: 原始图像
            layout: 文本布局
            translation_results: 翻译结果
            font_matches: 字体匹配结果
            
        Returns:
            渲染结果
        """
        import time
        start_time = time.time()
        
        try:
            if not PIL_AVAILABLE:
                raise ImportError("PIL不可用，无法进行布局渲染")
            
            # 转换图像格式
            if len(original_image.shape) == 3:
                pil_image = Image.fromarray(original_image)
            else:
                pil_image = Image.fromarray(cv2.cvtColor(original_image, cv2.COLOR_GRAY2RGB))
            
            # 创建绘图对象
            draw = ImageDraw.Draw(pil_image)
            
            # 渲染每个文本块
            for block in layout.blocks:
                self._render_text_block(draw, block, translation_results, font_matches)
            
            # 转换回numpy数组
            rendered_array = np.array(pil_image)
            
            render_time = time.time() - start_time
            
            return RenderResult(
                rendered_image=rendered_array,
                success=True,
                render_time=render_time
            )
            
        except Exception as e:
            self.logger.error(f"布局渲染失败: {e}")
            return RenderResult(
                rendered_image=original_image,
                success=False,
                render_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _render_text_block(self, draw: ImageDraw.Draw, block: TextBlock,
                         translation_results: Dict[str, Any],
                         font_matches: Dict[str, FontMatchResult]):
        """
        渲染文本块
        
        Args:
            draw: PIL绘图对象
            block: 文本块
            translation_results: 翻译结果
            font_matches: 字体匹配结果
        """
        try:
            # 获取块的翻译结果
            block_translations = translation_results.get('translations', [])
            
            # 渲染块中的每个区域
            for region in block.regions:
                # 查找对应的翻译结果
                translation = None
                font_match = None
                
                # 简单匹配（基于文本内容）
                for block_data, translations in block_translations:
                    for trans in translations:
                        if trans.original_text == region.text:
                            translation = trans
                            break
                    if translation:
                        break
                
                # 获取字体匹配
                if region.text in font_matches:
                    font_match = font_matches[region.text]
                
                # 如果有翻译结果和字体匹配，进行渲染
                if translation and font_match:
                    final_text = translation.compressed_text if translation.compressed_text else translation.translated_text
                    self._render_single_text(draw, region, final_text, font_match)
                
        except Exception as e:
            self.logger.error(f"渲染文本块失败: {e}")
    
    def create_text_overlay(self, image_shape: Tuple[int, int],
                          text_regions: List[TextRegion],
                          translation_results: List[TranslationPipelineResult],
                          font_matches: List[FontMatchResult]) -> np.ndarray:
        """
        创建文本覆盖层
        
        Args:
            image_shape: 图像形状 (height, width)
            text_regions: 文本区域
            translation_results: 翻译结果
            font_matches: 字体匹配结果
            
        Returns:
            文本覆盖层图像
        """
        try:
            # 创建透明背景
            height, width = image_shape[:2]
            overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # 渲染每个文本区域
            for region, translation, font_match in zip(text_regions, translation_results, font_matches):
                if not translation.success:
                    continue
                
                final_text = translation.compressed_text if translation.compressed_text else translation.translated_text
                self._render_overlay_text(draw, region, final_text, font_match)
            
            # 转换为numpy数组
            return np.array(overlay)
            
        except Exception as e:
            self.logger.error(f"创建文本覆盖层失败: {e}")
            return np.zeros((image_shape[0], image_shape[1], 4), dtype=np.uint8)
    
    def _render_overlay_text(self, draw: ImageDraw.Draw, region: TextRegion,
                           text: str, font_match: FontMatchResult):
        """
        在覆盖层上渲染文本
        
        Args:
            draw: PIL绘图对象
            region: 文本区域
            text: 要渲染的文本
            font_match: 字体匹配结果
        """
        try:
            # 计算文本位置
            bbox = region.bbox
            if len(bbox) != 4:
                return
            
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            # 加载字体
            font = self._load_font(font_match.font_info, region.height)
            
            # 渲染半透明背景
            background_color = (*self.config.background_color, 200)  # 80% 不透明度
            draw.rectangle([x_min, y_min, x_max, y_max], fill=background_color)
            
            # 渲染文本
            text_color = (*self.config.text_color, 255)  # 完全不透明
            draw.text((x_min + self.config.padding, y_min + self.config.padding), 
                     text, font=font, fill=text_color)
            
        except Exception as e:
            self.logger.debug(f"渲染覆盖层文本失败: {e}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新渲染配置
        
        Args:
            new_config: 新配置字典
        """
        for key, value in new_config.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.logger.debug(f"更新渲染配置: {key} = {value}")
            else:
                self.logger.warning(f"未知配置项: {key}")
    
    def is_available(self) -> bool:
        """检查图像渲染器是否可用"""
        return PIL_AVAILABLE
    
    def get_renderer_info(self) -> Dict[str, Any]:
        """获取渲染器信息"""
        return {
            "pil_available": PIL_AVAILABLE,
            "config": {
                "background_color": self.config.background_color,
                "text_color": self.config.text_color,
                "font_size_scale": self.config.font_size_scale,
                "line_spacing": self.config.line_spacing,
                "padding": self.config.padding,
                "anti_aliasing": self.config.anti_aliasing,
                "preserve_original_layout": self.config.preserve_original_layout,
                "auto_fit_text": self.config.auto_fit_text
            },
            "available": self.is_available()
        }


# 全局图像渲染器实例
_image_renderer = None


def get_image_renderer(config: Optional[RenderConfig] = None) -> ImageRenderer:
    """获取全局图像渲染器实例"""
    global _image_renderer
    if _image_renderer is None:
        _image_renderer = ImageRenderer(config)
    elif config is not None:
        _image_renderer.update_config(vars(config))
    return _image_renderer
