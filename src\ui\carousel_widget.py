#!/usr/bin/env python3
"""
轮播图组件 - 支持多图片展示和导航控制

提供轮播图功能，支持多个翻译结果的预览展示
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QStackedWidget, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QFont
import cv2
import numpy as np
from PyQt6.QtGui import QImage


class CarouselWidget(QWidget):
    """轮播图组件"""
    
    # 信号定义
    currentChanged = pyqtSignal(int)  # 当前页面变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_index = 0
        self.translation_results = {}  # 存储多语言翻译结果 {lang_code: {image, results, etc}}
        self.language_names = {}  # 存储语言代码到显示名称的映射
        self.theme_manager = None  # 将在主窗口中设置

        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 主容器框架
        self.main_frame = QFrame()
        self.main_frame.setMinimumHeight(280)  # 与原预览区域相同高度
        # 样式将在apply_theme中设置
        
        main_layout = QVBoxLayout(self.main_frame)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(12)
        
        # 移除语言标识标签，改为在状态栏显示
        
        # 图片显示区域
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)
        
        # 默认占位符
        self.placeholder = QLabel("📄\n\n翻译结果预览\n\n请先上传图片并开始翻译")
        self.placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 样式将在apply_theme中设置
        self.stacked_widget.addWidget(self.placeholder)
        
        # 导航控制区域
        self.create_navigation_controls(main_layout)
        
        layout.addWidget(self.main_frame)

    def set_theme_manager(self, theme_manager):
        """设置主题管理器"""
        self.theme_manager = theme_manager
        self.apply_theme()

    def apply_theme(self, theme_name: str = None):
        """应用主题"""
        if not self.theme_manager:
            return

        if theme_name is None:
            theme_name = self.theme_manager.get_current_theme()

        styles = self.theme_manager.get_theme_styles(theme_name)

        # 应用主容器框架样式
        self.main_frame.setStyleSheet(styles.get('frame_preview', ''))

        # 应用占位符样式
        self.placeholder.setStyleSheet(styles.get('label_placeholder', ''))

    def create_navigation_controls(self, layout):
        """创建导航控制"""
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(8)
        
        # 左箭头
        self.prev_btn = QPushButton("◀")
        self.prev_btn.setFixedSize(32, 32)
        self.prev_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #dadce0;
                border-radius: 16px;
                background-color: white;
                color: #5f6368;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f1f3f4;
                border-color: #1a73e8;
                color: #1a73e8;
            }
            QPushButton:disabled {
                background-color: #f8f9fa;
                color: #dadce0;
                border-color: #f1f3f4;
            }
        """)
        self.prev_btn.clicked.connect(self.previous_image)
        self.prev_btn.hide()
        
        # 指示器容器
        self.indicator_container = QWidget()
        self.indicator_layout = QHBoxLayout(self.indicator_container)
        self.indicator_layout.setContentsMargins(0, 0, 0, 0)
        self.indicator_layout.setSpacing(4)
        self.indicator_container.hide()
        
        # 右箭头
        self.next_btn = QPushButton("▶")
        self.next_btn.setFixedSize(32, 32)
        self.next_btn.setStyleSheet(self.prev_btn.styleSheet())
        self.next_btn.clicked.connect(self.next_image)
        self.next_btn.hide()
        
        nav_layout.addWidget(self.prev_btn)
        nav_layout.addStretch()
        nav_layout.addWidget(self.indicator_container)
        nav_layout.addStretch()
        nav_layout.addWidget(self.next_btn)
        
        layout.addWidget(nav_container)
        
    def add_translation_result(self, lang_code, lang_name, image, results=None, regions=None, colors=None):
        """添加翻译结果"""
        # 存储结果数据
        self.translation_results[lang_code] = {
            'image': image,
            'results': results,
            'regions': regions,
            'colors': colors
        }
        self.language_names[lang_code] = lang_name
        
        # 创建图片标签
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 转换并显示图片
        if image is not None:
            pixmap = self.convert_cv_to_pixmap(image)
            scaled_pixmap = pixmap.scaled(
                250, 200,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            image_label.setPixmap(scaled_pixmap)
        
        # 添加到堆叠组件
        self.stacked_widget.addWidget(image_label)
        
        # 更新UI状态
        self.update_ui_state()
        
    def convert_cv_to_pixmap(self, cv_image):
        """将OpenCV图像转换为QPixmap"""
        # 转换颜色格式 (BGR -> RGB)
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        
        # 转换为QImage
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
        
        # 转换为QPixmap
        return QPixmap.fromImage(qt_image)
        
    def remove_translation_result(self, lang_code):
        """移除翻译结果"""
        if lang_code in self.translation_results:
            del self.translation_results[lang_code]
            del self.language_names[lang_code]
            
            # 重建UI
            self.rebuild_ui()
            
    def clear_all_results(self):
        """清空所有结果"""
        self.translation_results.clear()
        self.language_names.clear()
        self.current_index = 0
        
        # 清空堆叠组件（保留占位符）
        while self.stacked_widget.count() > 1:
            widget = self.stacked_widget.widget(1)
            self.stacked_widget.removeWidget(widget)
            widget.deleteLater()
            
        # 重置到占位符
        self.stacked_widget.setCurrentIndex(0)
        self.update_ui_state()
        
    def rebuild_ui(self):
        """重建UI"""
        # 保存当前语言代码
        current_lang_codes = list(self.translation_results.keys())
        
        # 清空现有的图片组件（保留占位符）
        while self.stacked_widget.count() > 1:
            widget = self.stacked_widget.widget(1)
            self.stacked_widget.removeWidget(widget)
            widget.deleteLater()
        
        # 重新添加所有结果
        for lang_code in current_lang_codes:
            data = self.translation_results[lang_code]
            lang_name = self.language_names[lang_code]
            
            # 创建新的图片标签
            image_label = QLabel()
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            if data['image'] is not None:
                pixmap = self.convert_cv_to_pixmap(data['image'])
                scaled_pixmap = pixmap.scaled(
                    250, 200,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                image_label.setPixmap(scaled_pixmap)
            
            self.stacked_widget.addWidget(image_label)
        
        # 调整当前索引
        if self.current_index >= len(current_lang_codes):
            self.current_index = max(0, len(current_lang_codes) - 1)
        
        # 更新UI状态
        self.update_ui_state()
        
    def update_ui_state(self):
        """更新UI状态"""
        result_count = len(self.translation_results)
        
        if result_count == 0:
            # 没有结果，显示占位符
            self.stacked_widget.setCurrentIndex(0)
            self.prev_btn.hide()
            self.next_btn.hide()
            self.indicator_container.hide()
        else:
            # 有结果，显示轮播图
            # 调整当前索引到结果页面（跳过占位符）
            display_index = self.current_index + 1
            self.stacked_widget.setCurrentIndex(display_index)

            # 更新导航控件
            if result_count > 1:
                self.prev_btn.show()
                self.next_btn.show()
                self.update_indicators()
                self.indicator_container.show()
            else:
                self.prev_btn.hide()
                self.next_btn.hide()
                self.indicator_container.hide()

            # 更新按钮状态
            self.prev_btn.setEnabled(self.current_index > 0)
            self.next_btn.setEnabled(self.current_index < result_count - 1)
            
    def update_indicators(self):
        """更新指示器"""
        # 清空现有指示器
        while self.indicator_layout.count():
            child = self.indicator_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 创建新指示器
        result_count = len(self.translation_results)
        for i in range(result_count):
            indicator = QLabel("●")
            indicator.setFixedSize(12, 12)
            indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            if i == self.current_index:
                # 当前页面指示器
                indicator.setStyleSheet("""
                    QLabel {
                        color: #1a73e8;
                        font-size: 8px;
                    }
                """)
            else:
                # 非当前页面指示器
                indicator.setStyleSheet("""
                    QLabel {
                        color: #dadce0;
                        font-size: 8px;
                    }
                """)
            
            self.indicator_layout.addWidget(indicator)
            
    def previous_image(self):
        """上一张图片"""
        if self.current_index > 0:
            self.current_index -= 1
            self.update_ui_state()
            self.currentChanged.emit(self.current_index)
            
    def next_image(self):
        """下一张图片"""
        if self.current_index < len(self.translation_results) - 1:
            self.current_index += 1
            self.update_ui_state()
            self.currentChanged.emit(self.current_index)
            
    def get_current_result(self):
        """获取当前显示的翻译结果"""
        if not self.translation_results:
            return None
            
        current_lang_codes = list(self.translation_results.keys())
        if self.current_index < len(current_lang_codes):
            lang_code = current_lang_codes[self.current_index]
            return self.translation_results[lang_code]
        
        return None
        
    def get_result_count(self):
        """获取结果数量"""
        return len(self.translation_results)

    def get_current_language_info(self):
        """获取当前显示的语言信息"""
        if not self.translation_results:
            return None, None

        current_lang_codes = list(self.translation_results.keys())
        if self.current_index < len(current_lang_codes):
            lang_code = current_lang_codes[self.current_index]
            lang_name = self.language_names[lang_code]
            return lang_code, lang_name

        return None, None
