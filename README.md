# Image Text Translator

A powerful local image text translation tool with cross-platform GPU acceleration support.

## Features

- **OCR Text Recognition**: Extract text from images using PaddleOCR
- **Machine Translation**: Translate text between 200+ languages using NLLB-200
- **Text Compression**: Optimize translations using LLM-based compression
- **Cross-Platform GPU Support**: Automatic GPU acceleration detection (CUDA/CPU)
- **PyQt6 GUI**: User-friendly graphical interface
- **Font Management**: Support for multilingual text rendering with Noto fonts
- **Configurable Pipeline**: Flexible configuration for different use cases

## Supported Languages

- **OCR Languages**: Chinese, English, French, German, Japanese, Korean, and more
- **Translation Languages**: 200+ languages supported by NLLB-200 model
- **Font Support**: Full Unicode coverage with Noto font family

## Requirements

- Python 3.11+
- NVIDIA GPU (recommended for Windows/Linux) or CPU fallback
- 8GB+ RAM (16GB+ recommended for large models)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/image-text-translator.git
cd image-text-translator
```

2. Install dependencies using uv:
```bash
uv sync
```

3. Run the application:
```bash
uv run image-text-translator
```

## Usage

### Command Line
```bash
# Run the GUI application
uv run image-text-translator

# Or run directly with Python
uv run python src/main.py
```

### Configuration

Edit `config.yaml` to customize:
- OCR settings and language preferences
- Translation model configurations
- GPU/CPU preferences
- Font management options
- Text compression settings

## Project Structure

```
src/
├── core/           # Core translation pipeline
├── ui/             # PyQt6 GUI components
├── models/         # Model management and caching
├── utils/          # Utility functions
└── main.py         # Application entry point
```

## Development

Install development dependencies:
```bash
uv sync --extra dev
```

Run tests:
```bash
uv run pytest
```

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
