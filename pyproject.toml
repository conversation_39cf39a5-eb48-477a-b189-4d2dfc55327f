[project]
name = "image-text-translator"
version = "1.0.0"
description = "Local Image Text Translation Tool with Cross-Platform GPU Acceleration"
readme = "README.md"
requires-python = ">=3.11"
license = { text = "MIT" }
authors = [
    { name = "ImageTextTranslator Team", email = "<EMAIL>" }
]
keywords = ["image", "translation", "ocr", "pytorch", "ai", "multilingual"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Graphics",
    "Topic :: Text Processing :: Linguistic",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

# Core dependencies (PyTorch handled via tool.uv.sources for cross-platform support)
# Using exact versions for maximum stability and reproducibility
dependencies = [
    # PyTorch Ecosystem (cross-platform via uv.sources)
    "torch==2.7.1",
    "torchvision==0.22.1",
    "torchaudio==2.7.1",

    # Machine Learning & AI
    "transformers==4.53.2",
    "huggingface-hub==0.33.2",
    "safetensors==0.5.3",
    "accelerate==1.8.1",
    "tokenizers==0.21.2",
    
    # OCR & Computer Vision
    "paddleocr==3.1.0",
    "paddlepaddle==3.1.0",
    "paddlex==3.1.2",
    "opencv-contrib-python==*********",

    # GUI Framework
    "PyQt6==6.9.1",
    "PyQt6-Qt6==6.9.1",
    "PyQt6-sip==13.10.2",
    
    # LLM Integration
    "langchain==0.3.26",
    "langchain-community==0.3.27",
    "langchain-core==0.3.68",
    "langchain-openai==0.3.27",
    "langchain-text-splitters==0.3.8",
    "langsmith==0.4.4",
    "openai==1.93.3",
    "google-genai==1.26.0",
    
    # Data Processing
    "numpy==2.1.2",
    "pandas==2.3.1",
    "pillow==11.0.0",
    "scipy==1.16.0",
    "scikit-learn==1.7.0",
    
    # Network & HTTP
    "requests==2.32.4",
    "httpx==0.28.1",
    "httpcore==1.0.9",
    "aiohttp==3.12.13",
    "beautifulsoup4==4.13.4",

    # Configuration & Validation
    "pydantic==2.11.7",
    "pydantic-settings==2.10.1",
    "python-dotenv==1.1.1",
    "PyYAML==6.0.2",
    
    # Utilities
    "tqdm==4.67.1",
    "colorlog==6.9.0",
    "psutil==7.0.0",
    "filelock==3.13.1",
    "packaging==24.2",
    "typing-extensions==4.12.2",

    # Document Processing
    "lxml==6.0.0",
    "openpyxl==3.1.5",
    "pypdfium2==4.30.1",

    # Additional ML Tools
    "einops==0.8.1",
    "joblib==1.5.1",
    "threadpoolctl==3.6.0",
    "GPUtil==1.4.0",
]

[project.optional-dependencies]
# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

# Testing dependencies
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
]

[project.scripts]
image-text-translator = "src.main:main"

[project.urls]
Homepage = "https://github.com/yourusername/image-text-translator"
Repository = "https://github.com/yourusername/image-text-translator"
Issues = "https://github.com/yourusername/image-text-translator/issues"

# PyTorch Cross-Platform Configuration
[tool.uv.sources]
torch = [
    { index = "pytorch-cu128", marker = "sys_platform == 'win32'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
    { index = "pytorch-cu128", marker = "sys_platform == 'linux'" },
]
torchvision = [
    { index = "pytorch-cu128", marker = "sys_platform == 'win32'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
    { index = "pytorch-cu128", marker = "sys_platform == 'linux'" },
]
torchaudio = [
    { index = "pytorch-cu128", marker = "sys_platform == 'win32'" },
    { index = "pytorch-cpu", marker = "sys_platform == 'darwin'" },
    { index = "pytorch-cu128", marker = "sys_platform == 'linux'" },
]

# PyTorch Index Sources
[[tool.uv.index]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[[tool.uv.index]]
name = "pytorch-cu128"
url = "https://download.pytorch.org/whl/cu128"
explicit = true

# Build system configuration
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# Hatchling configuration
[tool.hatch.build.targets.wheel]
packages = ["src"]

# UV Configuration
[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]
