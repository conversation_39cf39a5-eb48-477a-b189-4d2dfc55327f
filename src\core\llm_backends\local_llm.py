#!/usr/bin/env python3
"""
本地LLM后端实现

基于Transformers的本地大语言模型调用
"""

import os
import time
from typing import Dict, Any, Optional

try:
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

from .base_llm import BaseLLMCompressor
from ...utils.logger import get_logger
from ...utils.device_manager import get_device_manager


class LocalLLMCompressor(BaseLLMCompressor):
    """本地LLM压缩器实现"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        """
        初始化本地LLM压缩器
        
        Args:
            model_name: 模型名称
            config: 配置字典
        """
        super().__init__(config)
        
        self.logger = get_logger()
        self.model_name = model_name
        
        # 设备管理器
        self.device_manager = get_device_manager()
        self.device = self.device_manager.get_device_for_model('compression')
        self.dtype = self.device_manager.get_optimal_dtype()
        
        # 模型和分词器
        self.model = None
        self.tokenizer = None
        
        # 从配置文件获取模型存储路径
        base_compression_path = config.get('models.compression.path', 'models/compression')
        
        # 为每个模型创建子目录
        model_dir_name = model_name.replace('/', '--').replace('\\', '--')
        self.model_cache_dir = os.path.join(base_compression_path, model_dir_name)
        
        # 确保模型缓存目录存在
        os.makedirs(self.model_cache_dir, exist_ok=True)
        
        # 检查设备是否适合推理
        self._validate_device()
    
    def _validate_device(self):
        """验证设备配置"""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers库不可用，无法使用本地LLM压缩功能")
        
        if self.device.type == 'cuda' and not torch.cuda.is_available():
            self.logger.warning("CUDA不可用，将使用CPU进行推理")
            self.device = torch.device('cpu')
    
    def _load_model(self):
        """加载模型和分词器"""
        if self.model is not None and self.tokenizer is not None:
            return
        
        start_time = time.time()
        
        try:
            self.logger.info(f"正在加载本地文本压缩模型: {self.model_name}")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                cache_dir=self.model_cache_dir,
                trust_remote_code=True
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.logger.info("✅ 分词器加载完成")
            
            # 加载模型
            self.logger.info(f"📥 正在加载压缩模型: {self.model_name}")
            
            if self.device.type == 'cpu':
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    cache_dir=self.model_cache_dir,
                    torch_dtype=self.dtype,
                    device_map="cpu",
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    cache_dir=self.model_cache_dir,
                    torch_dtype=self.dtype,
                    trust_remote_code=True
                )
                self.model = self.model.to(self.device)
            
            self.logger.info("✅ 模型加载完成")
            
            # 设置为评估模式
            self.model.eval()
            
            load_time = time.time() - start_time
            self.logger.info(f"🎉 本地压缩模型加载成功: {self.model_name}，设备: {self.device}，总耗时: {load_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"❌ 本地压缩模型加载失败: {self.model_name}")
            self.logger.error(f"错误详情: {e}")
            raise
    
    def _llm_compress(self, text: str, strategy: str = "simple") -> str:
        """
        使用本地LLM进行文本压缩
        
        Args:
            text: 要压缩的文本
            strategy: 压缩策略
            
        Returns:
            压缩后的文本
        """
        try:
            # 加载模型
            self._load_model()
            
            # 获取策略配置
            default_strategy = "simple"
            if strategy not in self.compression_strategies:
                self.logger.warning(f"未知的压缩策略: {strategy}，使用默认策略 '{default_strategy}'")
                strategy = default_strategy
            
            strategy_config = self.compression_strategies[strategy]
            
            # 构建对话格式的输入
            system_prompt = strategy_config.get("system_prompt", strategy_config.get("system", ""))
            user_template = strategy_config.get("user_template", "")
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_template.format(text=text)}
            ]
            
            # 使用分词器构建输入
            input_text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # 编码输入
            inputs = self.tokenizer(
                input_text,
                return_tensors="pt",
                truncation=True,
                max_length=2048
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成配置
            generation_config = {
                "max_new_tokens": 512,
                "do_sample": True,
                "temperature": 0.7,
                "top_p": 0.9,
                "pad_token_id": self.tokenizer.eos_token_id
            }
            
            # 生成回复
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs['input_ids'],
                    attention_mask=inputs['attention_mask'],
                    **generation_config
                )
            
            # 解码输出
            generated_text = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )
            
            # 清理输出
            compressed_text = generated_text.strip()
            
            # 如果输出为空或过短，返回原文
            if not compressed_text or len(compressed_text) < 3:
                self.logger.warning(f"本地LLM压缩输出过短，返回原文: '{generated_text}'")
                return text
            
            return compressed_text
            
        except Exception as e:
            self.logger.error(f"本地LLM文本压缩失败: {e}")
            raise
    
    def _llm_compress_batch(self, batch_content: str, strategy: str = "simple") -> str:
        """
        使用本地LLM进行批量压缩

        Args:
            batch_content: 格式化的批量内容
            strategy: 压缩策略（已弃用，保留参数兼容性）

        Returns:
            LLM输出的压缩结果
        """
        # 加载模型
        self._load_model()

        # 获取固定的提示词模板
        llm_prompt = self.config.get('llm_prompt', {})
        system_prompt = llm_prompt.get('system_prompt', '')
        user_template = llm_prompt.get('user_template', '')

        # 格式化用户消息
        user_content = user_template.format(batch_content=batch_content)

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content}
        ]

        # 输出完整提示词内容
        self.logger.info("📋 完整提示词内容:")
        self.logger.info("=" * 80)
        self.logger.info("🔹 System Prompt:")
        self.logger.info(f"{system_prompt}")
        self.logger.info("-" * 80)
        self.logger.info("🔹 User Prompt:")
        self.logger.info(f"{user_content}")
        self.logger.info("=" * 80)
        
        # 使用分词器构建输入
        input_text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        
        # 编码输入
        inputs = self.tokenizer(
            input_text,
            return_tensors="pt",
            truncation=True,
            max_length=4096
        )
        
        # 移动到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # 生成输出
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=2048,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        # 解码输出
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # 提取生成的部分（去除输入部分）
        if input_text in generated_text:
            compressed_text = generated_text.replace(input_text, "").strip()
        else:
            compressed_text = generated_text.strip()
        
        return compressed_text
    
    def is_available(self) -> bool:
        """检查本地LLM压缩器是否可用"""
        return TRANSFORMERS_AVAILABLE
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "device": str(self.device),
            "loaded": self.model is not None and self.tokenizer is not None,
            "available": self.is_available(),
            "type": "local_llm",
            "strategies": list(self.compression_strategies.keys())
        }
