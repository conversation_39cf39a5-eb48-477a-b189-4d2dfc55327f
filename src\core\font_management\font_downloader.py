"""
字体下载器

负责从Google Fonts下载Noto字体文件
支持断点续传、错误重试、完整性验证
"""

import os
import hashlib
import requests
import zipfile
import tempfile
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List
from dataclasses import dataclass
import time
import threading
from concurrent.futures import ThreadPoolExecutor, Future
from urllib.parse import urlparse

from ...utils.logger import get_logger
from ...utils.config import get_config
from .noto_font_registry import NotoFontInfo
from .google_fonts_downloader import get_google_fonts_downloader

@dataclass
class DownloadProgress:
    """下载进度信息"""
    font_name: str
    total_size: int
    downloaded_size: int
    speed: float  # bytes/second
    eta: float    # seconds
    status: str   # 'downloading', 'completed', 'failed', 'paused'

class FontDownloader:
    """字体下载器"""
    
    def __init__(self, cache_dir: Path, max_concurrent_downloads: int = 3):
        self.logger = get_logger()
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_concurrent_downloads = max_concurrent_downloads
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_downloads)
        
        # 下载状态管理
        self._active_downloads: Dict[str, Future] = {}
        self._download_progress: Dict[str, DownloadProgress] = {}
        self._download_callbacks: Dict[str, Callable[[DownloadProgress], None]] = {}
        
        # 下载配置
        self.chunk_size = 8192
        self.timeout = 30
        self.max_retries = 3
        self.retry_delay = 1.0
        
        self.logger.info("字体下载器初始化完成")
    
    def download_font_async(self, font_info: NotoFontInfo, 
                           progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> Future:
        """异步下载字体"""
        if font_info.name in self._active_downloads:
            self.logger.warning(f"字体 {font_info.name} 正在下载中")
            return self._active_downloads[font_info.name]
        
        if progress_callback:
            self._download_callbacks[font_info.name] = progress_callback
        
        future = self.executor.submit(self._download_font_sync, font_info)
        self._active_downloads[font_info.name] = future
        
        # 下载完成后清理
        def cleanup(fut):
            self._active_downloads.pop(font_info.name, None)
            self._download_progress.pop(font_info.name, None)
            self._download_callbacks.pop(font_info.name, None)
        
        future.add_done_callback(cleanup)
        return future
    
    def download_font_sync(self, font_info: NotoFontInfo) -> Optional[Path]:
        """同步下载字体"""
        return self._download_font_sync(font_info)
    
    def _download_font_sync(self, font_info: NotoFontInfo) -> Optional[Path]:
        """内部同步下载实现"""
        font_path = self.cache_dir / font_info.filename

        # 检查字体是否已存在且有效
        if self._is_font_valid(font_path, font_info):
            self.logger.info(f"字体 {font_info.name} 已存在，跳过下载")
            return font_path

        self.logger.info(f"开始下载字体: {font_info.name}")

        # 检查字体是否标记为使用Google Fonts API
        if font_info.download_url == "google_fonts_api":
            # 使用Google Fonts API下载
            google_downloader = get_google_fonts_downloader(self.cache_dir)

            # 查找对应的语言代码
            language_code = self._find_language_code_for_font(font_info.name)
            if language_code:
                self.logger.info(f"使用Google Fonts API下载字体: {font_info.name}")

                def api_progress_callback(progress: DownloadProgress):
                    self._download_progress[font_info.name] = progress
                    callback = self._download_callbacks.get(font_info.name)
                    if callback:
                        callback(progress)

                success, message, api_font_path = google_downloader.download_font_for_language(
                    language_code, api_progress_callback
                )

                if success and api_font_path:
                    self.logger.info(f"Google Fonts API下载成功: {font_info.name}")
                    return api_font_path
                else:
                    self.logger.error(f"Google Fonts API下载失败: {message}")
                    return None
            else:
                self.logger.error(f"未找到字体 {font_info.name} 对应的语言代码")
                return None
        else:
            # 传统下载方式（已弃用，仅作为回退）
            self.logger.warning(f"字体 {font_info.name} 使用传统下载方式，建议更新到Google Fonts API")
            return self._download_font_traditional(font_info)

    def _find_language_code_for_font(self, font_name: str) -> Optional[str]:
        """根据字体名称查找对应的语言代码"""
        google_downloader = get_google_fonts_downloader()
        for lang_code, mapped_font_name in google_downloader.language_to_font.items():
            if mapped_font_name == font_name:
                return lang_code
        return None

    def _download_font_traditional(self, font_info: NotoFontInfo) -> Optional[Path]:
        """传统下载方式（原有逻辑）"""
        font_path = self.cache_dir / font_info.filename

        # 初始化进度信息
        progress = DownloadProgress(
            font_name=font_info.name,
            total_size=font_info.file_size,
            downloaded_size=0,
            speed=0.0,
            eta=0.0,
            status='downloading'
        )
        self._download_progress[font_info.name] = progress

        # 获取备用下载源
        download_urls = self._get_download_urls(font_info)

        for url_index, download_url in enumerate(download_urls):
            self.logger.info(f"尝试下载源 {url_index + 1}/{len(download_urls)}: {download_url}")

            # 创建临时字体信息对象
            temp_font_info = NotoFontInfo(
                name=font_info.name,
                filename=font_info.filename,
                script=font_info.script,
                download_url=download_url,
                file_size=font_info.file_size,
                version=font_info.version,
                supports_languages=font_info.supports_languages
            )

            try:
                # 尝试下载
                for attempt in range(self.max_retries):
                    try:
                        if self._download_with_progress(temp_font_info, font_path, progress):
                            progress.status = 'completed'
                            self._notify_progress(font_info.name, progress)
                            self.logger.info(f"字体 {font_info.name} 传统方式下载完成")
                            return font_path
                    except Exception as e:
                        self.logger.warning(f"字体 {font_info.name} 下载失败 (源 {url_index + 1}, 尝试 {attempt + 1}/{self.max_retries}): {e}")
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                        else:
                            break  # 尝试下一个下载源

            except Exception as e:
                self.logger.warning(f"下载源 {url_index + 1} 失败: {e}")
                continue

        # 所有下载源都失败了
        progress.status = 'failed'
        self._notify_progress(font_info.name, progress)
        self.logger.error(f"字体 {font_info.name} 所有下载源都失败")

        # 清理不完整的文件
        if font_path.exists():
            font_path.unlink()

        return None

    def _get_download_urls(self, font_info: NotoFontInfo) -> List[str]:
        """获取字体的所有可能下载URL"""
        urls = [font_info.download_url]  # 主要下载源

        # 添加备用下载源
        if 'github.com/notofonts/noto-cjk' in font_info.download_url:
            # CJK字体的备用源
            if 'CJKsc' in font_info.filename:
                urls.append("https://github.com/notofonts/noto-cjk/releases/latest/download/09_NotoSansCJKsc.zip")
            elif 'CJKtc' in font_info.filename:
                urls.append("https://github.com/notofonts/noto-cjk/releases/latest/download/10_NotoSansCJKtc.zip")
            elif 'CJKjp' in font_info.filename:
                urls.append("https://github.com/notofonts/noto-cjk/releases/latest/download/07_NotoSansCJKjp.zip")
            elif 'CJKkr' in font_info.filename:
                urls.append("https://github.com/notofonts/noto-cjk/releases/latest/download/08_NotoSansCJKkr.zip")

        elif 'github.com/notofonts/noto-fonts' in font_info.download_url:
            # 普通字体的备用源
            font_name = font_info.filename.replace('-Regular.ttf', '').replace('-Regular.otf', '')
            urls.append(f"https://fonts.gstatic.com/s/{font_name.lower()}/v1/{font_info.filename}")

        return urls
    
    def _download_with_progress(self, font_info: NotoFontInfo, font_path: Path,
                               progress: DownloadProgress) -> bool:
        """带进度的下载实现"""
        # 检查是否为zip文件下载
        if font_info.download_url.endswith('.zip'):
            return self._download_zip_font(font_info, font_path, progress)
        else:
            return self._download_direct_font(font_info, font_path, progress)
    
    def _download_direct_font(self, font_info: NotoFontInfo, font_path: Path,
                             progress: DownloadProgress) -> bool:
        """直接下载字体文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # 支持断点续传
        if font_path.exists():
            existing_size = font_path.stat().st_size
            headers['Range'] = f'bytes={existing_size}-'
            progress.downloaded_size = existing_size

        try:
            # 先发送HEAD请求检查内容类型
            head_response = requests.head(font_info.download_url, headers=headers, timeout=self.timeout)
            head_response.raise_for_status()

            content_type = head_response.headers.get('content-type', '')

            # 验证内容类型
            valid_font_types = [
                'font/ttf', 'font/otf', 'font/woff', 'font/woff2',
                'application/x-font-ttf', 'application/x-font-otf',
                'application/font-sfnt', 'application/font-woff',
                'application/octet-stream', 'binary/octet-stream'
            ]

            if content_type and not any(valid_type in content_type.lower() for valid_type in valid_font_types):
                self.logger.warning(f"警告：内容类型 '{content_type}' 可能不是字体文件")

            # 获取文件
            response = requests.get(font_info.download_url, headers=headers,
                                  stream=True, timeout=self.timeout)
            response.raise_for_status()

            # 获取总大小
            if 'content-length' in response.headers:
                content_length = int(response.headers['content-length'])
                if 'content-range' in response.headers:
                    # 断点续传情况
                    progress.total_size = progress.downloaded_size + content_length
                else:
                    progress.total_size = content_length

            # 下载文件
            mode = 'ab' if font_path.exists() else 'wb'
            start_time = time.time()

            with open(font_path, mode) as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        f.write(chunk)
                        progress.downloaded_size += len(chunk)

                        # 更新进度
                        elapsed = time.time() - start_time
                        if elapsed > 0:
                            progress.speed = progress.downloaded_size / elapsed
                            remaining = progress.total_size - progress.downloaded_size
                            progress.eta = remaining / progress.speed if progress.speed > 0 else 0

                        self._notify_progress(font_info.name, progress)

            # 验证下载的文件是否为有效的字体文件
            if not self._is_font_valid(font_path, font_info):
                self.logger.error(f"下载的文件不是有效的字体文件: {font_path}")
                if font_path.exists():
                    font_path.unlink()
                return False

            return True

        except requests.exceptions.RequestException as e:
            self.logger.error(f"下载请求失败: {e}")
            return False
    
    def _download_zip_font(self, font_info: NotoFontInfo, font_path: Path,
                          progress: DownloadProgress) -> bool:
        """下载zip格式的字体文件"""
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            temp_path = Path(temp_file.name)

        try:
            # 下载zip文件
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 先发送HEAD请求检查内容类型
            head_response = requests.head(font_info.download_url, headers=headers, timeout=self.timeout)
            head_response.raise_for_status()

            content_type = head_response.headers.get('content-type', '')

            # 验证内容类型
            valid_zip_types = [
                'application/zip', 'application/x-zip-compressed',
                'application/x-zip', 'application/octet-stream'
            ]

            if content_type and not any(valid_type in content_type.lower() for valid_type in valid_zip_types):
                self.logger.warning(f"警告：内容类型 '{content_type}' 可能不是ZIP文件")

            # 下载zip文件
            response = requests.get(font_info.download_url, headers=headers, stream=True, timeout=self.timeout)
            response.raise_for_status()

            if 'content-length' in response.headers:
                progress.total_size = int(response.headers['content-length'])

            start_time = time.time()

            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        f.write(chunk)
                        progress.downloaded_size += len(chunk)

                        # 更新进度
                        elapsed = time.time() - start_time
                        if elapsed > 0:
                            progress.speed = progress.downloaded_size / elapsed
                            remaining = progress.total_size - progress.downloaded_size
                            progress.eta = remaining / progress.speed if progress.speed > 0 else 0

                        self._notify_progress(font_info.name, progress)

            # 验证下载的文件是否为有效的ZIP文件
            if not self._is_valid_zip_file(temp_path):
                self.logger.error(f"下载的文件不是有效的ZIP文件: {temp_path}")
                return False

            # 解压字体文件
            try:
                with zipfile.ZipFile(temp_path, 'r') as zip_file:
                    # 查找目标字体文件
                    font_files = [name for name in zip_file.namelist()
                                if name.lower().endswith(('.ttf', '.otf'))]

                    if not font_files:
                        self.logger.warning("ZIP文件中没有找到字体文件")
                        return False

                    # 1. 尝试精确匹配
                    exact_matches = [name for name in font_files
                                   if os.path.basename(name).lower() == font_info.filename.lower()]

                    # 2. 尝试部分匹配
                    if not exact_matches:
                        partial_matches = [name for name in font_files
                                         if font_info.filename.lower() in name.lower()]
                        if partial_matches:
                            exact_matches = [partial_matches[0]]

                    # 3. 使用第一个字体文件
                    target_font = exact_matches[0] if exact_matches else font_files[0]

                    # 提取字体文件
                    with zip_file.open(target_font) as source:
                        with open(font_path, 'wb') as target:
                            target.write(source.read())

                    self.logger.info(f"从ZIP文件中提取字体: {target_font} -> {font_path}")
                    return True

            except zipfile.BadZipFile:
                self.logger.error(f"无效的ZIP文件: {temp_path}")
                return False
            except IndexError:
                self.logger.error(f"在ZIP文件中未找到字体文件: {font_info.filename}")
                return False

        except requests.exceptions.RequestException as e:
            self.logger.error(f"下载请求失败: {e}")
            return False
        finally:
            # 清理临时文件
            if temp_path.exists():
                temp_path.unlink()
    
    def _is_valid_zip_file(self, file_path: Path) -> bool:
        """验证文件是否为有效的ZIP文件"""
        if not file_path.exists():
            return False

        # 检查文件大小
        file_size = file_path.stat().st_size
        if file_size == 0:
            return False

        # 检查文件头是否为ZIP格式
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                # ZIP文件头: PK\x03\x04
                if header == b'PK\x03\x04':
                    return True
                else:
                    self.logger.warning(f"文件不是ZIP格式: {file_path}, 头部: {header}")
        except Exception as e:
            self.logger.warning(f"ZIP文件验证失败: {file_path}, 错误: {e}")

        # 尝试使用zipfile模块验证
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # 测试ZIP文件完整性
                test_result = zip_file.testzip()
                if test_result is None:
                    return True
                else:
                    self.logger.warning(f"ZIP文件损坏: {file_path}, 损坏文件: {test_result}")
        except zipfile.BadZipFile:
            self.logger.warning(f"无效的ZIP文件: {file_path}")
        except Exception as e:
            self.logger.warning(f"ZIP文件验证失败: {file_path}, 错误: {e}")

        return False

    def _is_font_valid(self, font_path: Path, font_info: NotoFontInfo) -> bool:
        """验证字体文件是否有效"""
        if not font_path.exists():
            return False

        # 检查文件大小
        file_size = font_path.stat().st_size
        if file_size == 0:
            return False

        # 对于已知大小的字体，检查大小是否合理（允许20%误差）
        if font_info.file_size > 0:
            size_diff = abs(file_size - font_info.file_size) / font_info.file_size
            if size_diff > 0.2:
                self.logger.warning(f"字体文件大小异常: {font_path}, 期望: {font_info.file_size}, 实际: {file_size}")
                # 不返回False，因为文件大小可能不准确

        # 检查文件头是否为有效的字体格式
        try:
            with open(font_path, 'rb') as f:
                header = f.read(4)
                # TTF: 0x00010000 或 'true', OTF: 'OTTO'
                if header in [b'\x00\x01\x00\x00', b'true', b'OTTO']:
                    return True
                else:
                    self.logger.warning(f"文件不是字体格式: {font_path}, 头部: {header}")
        except Exception as e:
            self.logger.warning(f"字体文件验证失败: {font_path}, 错误: {e}")

        return False
    
    def _notify_progress(self, font_name: str, progress: DownloadProgress):
        """通知下载进度"""
        callback = self._download_callbacks.get(font_name)
        if callback:
            try:
                callback(progress)
            except Exception as e:
                self.logger.warning(f"进度回调执行失败: {e}")
    
    def get_download_progress(self, font_name: str) -> Optional[DownloadProgress]:
        """获取下载进度"""
        return self._download_progress.get(font_name)
    
    def cancel_download(self, font_name: str) -> bool:
        """取消下载"""
        future = self._active_downloads.get(font_name)
        if future:
            return future.cancel()
        return False
    
    def is_downloading(self, font_name: str) -> bool:
        """检查是否正在下载"""
        return font_name in self._active_downloads
    
    def cleanup(self):
        """清理资源"""
        # 取消所有活动下载
        for future in self._active_downloads.values():
            future.cancel()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("字体下载器已清理")

# 全局字体下载器实例
_font_downloader = None

def get_font_downloader(cache_dir: Optional[Path] = None) -> FontDownloader:
    """获取全局字体下载器实例"""
    global _font_downloader
    if _font_downloader is None:
        if cache_dir is None:
            cache_dir = Path("cache/fonts")
        _font_downloader = FontDownloader(cache_dir)
    return _font_downloader
