#!/usr/bin/env python3
"""
运行时路径处理模块

解决PyInstaller打包后路径计算问题，确保在开发环境和打包环境中都能正确找到外部文件
"""

import sys
import os
from pathlib import Path
from typing import Optional


def get_runtime_base_path() -> Path:
    """
    获取运行时基础路径

    在开发环境中返回项目根目录
    在PyInstaller打包环境中返回可执行文件所在目录（因为所有文件都在exe同目录）

    Returns:
        Path: 运行时基础路径
    """
    if getattr(sys, 'frozen', False):
        # PyInstaller打包环境
        # 现在所有外部文件都在exe同目录，直接返回exe目录
        base_path = Path(sys.executable).parent
    else:
        # 开发环境：使用项目根目录
        # 从当前文件位置推算项目根目录
        current_file = Path(__file__).resolve()
        # src/utils/runtime_path.py -> 项目根目录
        base_path = current_file.parent.parent.parent

    return base_path.resolve()


def get_config_file_path(config_filename: str = "config.yaml") -> Path:
    """
    获取配置文件路径
    
    Args:
        config_filename: 配置文件名，默认为config.yaml
        
    Returns:
        Path: 配置文件的完整路径
    """
    base_path = get_runtime_base_path()
    return base_path / config_filename


def get_external_path(relative_path: str) -> Path:
    """
    获取外部文件/目录的绝对路径
    
    Args:
        relative_path: 相对于运行时基础路径的相对路径
        
    Returns:
        Path: 外部文件/目录的绝对路径
    """
    base_path = get_runtime_base_path()
    return base_path / relative_path


def ensure_external_directories(*relative_paths: str) -> None:
    """
    确保外部目录存在
    
    Args:
        *relative_paths: 相对路径列表
    """
    base_path = get_runtime_base_path()
    
    for relative_path in relative_paths:
        dir_path = base_path / relative_path
        dir_path.mkdir(parents=True, exist_ok=True)


def is_packaged_environment() -> bool:
    """
    检查是否在PyInstaller打包环境中运行
    
    Returns:
        bool: True表示在打包环境中，False表示在开发环境中
    """
    return getattr(sys, 'frozen', False)


def get_resource_path(relative_path: str) -> Path:
    """
    获取资源文件路径（用于打包进可执行文件的资源）
    
    Args:
        relative_path: 资源文件的相对路径
        
    Returns:
        Path: 资源文件的绝对路径
    """
    if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
        # PyInstaller临时目录中的资源
        return Path(sys._MEIPASS) / relative_path
    else:
        # 开发环境中的资源
        return get_runtime_base_path() / relative_path


def get_executable_dir() -> Path:
    """
    获取可执行文件所在目录
    
    Returns:
        Path: 可执行文件所在目录
    """
    if getattr(sys, 'frozen', False):
        return Path(sys.executable).parent
    else:
        # 开发环境返回项目根目录
        return get_runtime_base_path()


def validate_external_files(*file_paths: str) -> dict:
    """
    验证外部文件是否存在
    
    Args:
        *file_paths: 要验证的文件路径列表（相对路径）
        
    Returns:
        dict: 验证结果，格式为 {file_path: exists}
    """
    base_path = get_runtime_base_path()
    results = {}
    
    for file_path in file_paths:
        full_path = base_path / file_path
        results[file_path] = full_path.exists()
    
    return results


def get_runtime_info() -> dict:
    """
    获取运行时环境信息
    
    Returns:
        dict: 运行时信息
    """
    return {
        'is_packaged': is_packaged_environment(),
        'base_path': str(get_runtime_base_path()),
        'executable_dir': str(get_executable_dir()),
        'python_executable': sys.executable,
        'has_meipass': hasattr(sys, '_MEIPASS'),
        'meipass': getattr(sys, '_MEIPASS', None),
        'frozen': getattr(sys, 'frozen', False)
    }


# 兼容性函数，用于替换原有的路径计算逻辑
def get_project_root() -> Path:
    """
    获取项目根目录（兼容性函数）
    
    Returns:
        Path: 项目根目录路径
    """
    return get_runtime_base_path()
