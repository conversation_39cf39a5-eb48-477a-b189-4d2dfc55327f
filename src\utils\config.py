#!/usr/bin/env python3
"""
配置管理模块

管理应用程序的配置信息，包括模型路径、语言设置等
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from .logger import get_logger
from .runtime_path import get_runtime_base_path, get_config_file_path, get_external_path


class Config:
    """配置管理类 - 纯粹从config.yaml读取配置，不包含硬编码默认值"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径，默认为运行时基础目录下的config.yaml
        """
        self.project_root = get_runtime_base_path()
        self.config_file = Path(config_file) if config_file else get_config_file_path()
        self.config_data = {}
        self.logger = get_logger()

        # 加载配置文件
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        try:
            if not self.config_file.exists():
                raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")

            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)

            if not self.config_data:
                raise ValueError(f"配置文件 {self.config_file} 为空或格式错误")

        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")


    def reload_config(self):
        """重新加载配置文件"""
        try:
            self._load_config()
            self.logger.info("配置文件重新加载成功")
        except Exception as e:
            self.logger.error(f"重新加载配置文件失败: {e}")
            raise
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)

            self.logger.info(f"正在保存配置到: {self.config_file}")

            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.json':
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)
                else:  # yaml
                    yaml.dump(self.config_data, f, default_flow_style=False,
                             allow_unicode=True, indent=2)

            self.logger.info("配置文件保存成功")

        except Exception as e:
            error_msg = f"保存配置文件失败: {e}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值（支持默认值）

        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'models.ocr.path'
            default: 默认值，如果配置项不存在则返回此值

        Returns:
            配置值或默认值
        """
        keys = key.split('.')
        value = self.config_data

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def get_required(self, key: str) -> Any:
        """
        获取必需的配置值（配置项不存在时抛出错误）

        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'models.ocr.path'

        Returns:
            配置值

        Raises:
            KeyError: 当配置项不存在时
        """
        keys = key.split('.')
        value = self.config_data

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError) as e:
            raise KeyError(f"配置项 '{key}' 不存在于配置文件中") from e
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config_data
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_model_path(self, model_type: str) -> Path:
        """
        获取模型路径
        
        Args:
            model_type: 模型类型 ('ocr', 'translation', 'compression')
            
        Returns:
            模型路径
        """
        model_path = self.get(f'models.{model_type}.path', f'models/{model_type}')
        return self.project_root / model_path
    
    def get_absolute_path(self, relative_path: str) -> Path:
        """
        获取相对于运行时基础目录的绝对路径

        Args:
            relative_path: 相对路径

        Returns:
            绝对路径
        """
        return get_external_path(relative_path)
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.get('paths.models', 'models'),
            self.get('paths.fonts', 'fonts'),
            self.get('paths.temp', 'temp'),
            self.get('paths.output', 'output')
        ]
        
        for directory in directories:
            dir_path = self.get_absolute_path(directory)
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "zh_CN": "中文 (简体)",
            "zh_TW": "中文 (繁體)",
            "en": "English",
            "ja": "日本語",
            "ko": "한국어",
            "fr": "Français",
            "de": "Deutsch",
            "es": "Español",
            "ru": "Русский",
            "ar": "العربية",
            "hi": "हिन्दी",
            "pt": "Português",
            "it": "Italiano",
            "th": "ไทย",
            "vi": "Tiếng Việt"
        }
    
    def get_language_name(self, lang_code: str) -> str:
        """获取语言显示名称"""
        languages = self.get_supported_languages()
        return languages.get(lang_code, lang_code)
    
    def update_from_dict(self, updates: Dict[str, Any]):
        """
        从字典批量更新配置
        
        Args:
            updates: 更新的配置字典
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config_data, updates)
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config_data = self.default_config.copy()
        self.save_config()

    # 便捷方法访问新配置项
    def get_text_processing_config(self) -> Dict[str, Any]:
        """获取文本处理配置"""
        return self.get('text_processing', {})

    def get_image_processing_config(self) -> Dict[str, Any]:
        """获取图像处理配置"""
        return self.get('image_processing', {})

    def get_device_config(self) -> Dict[str, Any]:
        """获取设备配置"""
        return self.get('device', {})

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})

    def get_compression_strategies(self) -> Dict[str, Any]:
        """获取压缩策略配置"""
        return self.get('compression_strategies', {})

    def get_network_config(self) -> Dict[str, Any]:
        """获取网络配置"""
        return self.get('network', {})

    def get_testing_config(self) -> Dict[str, Any]:
        """获取测试配置"""
        return self.get('testing', {})

    def get_font_paths(self, language: str = 'english') -> List[str]:
        """获取指定语言的字体路径列表"""
        font_config = self.get('paths.default_fonts', {})
        return font_config.get(language, font_config.get('english', []))

    def get_system_font_paths(self) -> List[str]:
        """获取当前系统的字体路径"""
        import platform
        system = platform.system().lower()

        system_fonts = self.get('paths.system_fonts', {})

        if 'windows' in system:
            return system_fonts.get('windows', [])
        elif 'darwin' in system:  # macOS
            return system_fonts.get('macos', [])
        else:  # Linux and others
            return system_fonts.get('linux', [])

    def get_google_fonts_config(self) -> Dict[str, Any]:
        """获取Google Fonts配置"""
        return self.get('google_fonts', {})

    def get_google_fonts_api_key(self) -> Optional[str]:
        """获取Google Fonts API密钥"""
        return self.get('google_fonts.api_key')

    def get_google_fonts_cache_dir(self) -> str:
        """获取Google Fonts缓存目录"""
        return self.get('google_fonts.cache_dir', 'cache/fonts')
    
    def __getitem__(self, key):
        """支持字典式访问"""
        return self.get(key)
    
    def __setitem__(self, key, value):
        """支持字典式设置"""
        self.set(key, value)


# 全局配置实例
config = Config()


def get_config() -> Config:
    """获取全局配置实例"""
    return config


def init_config(config_file: Optional[str] = None) -> Config:
    """
    初始化配置

    Args:
        config_file: 配置文件路径

    Returns:
        配置实例
    """
    global config
    config = Config(config_file)
    config.ensure_directories()
    return config


def update_config(key: str, value: Any):
    """
    更新配置项并保存到文件

    Args:
        key: 配置键，支持点号分隔的嵌套键
        value: 配置值
    """
    global config
    logger = get_logger()

    try:
        logger.info(f"更新配置项: {key} = {value}")

        # 设置配置值
        config.set(key, value)

        # 保存到文件
        config.save_config()

        logger.info(f"配置项 {key} 更新成功")

    except Exception as e:
        error_msg = f"更新配置项 {key} 失败: {e}"
        logger.error(error_msg)
        raise RuntimeError(error_msg) from e
