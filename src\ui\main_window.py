#!/usr/bin/env python3
"""
主窗口模块 - Google翻译风格的图像文本翻译工具界面

提供简洁直观的翻译体验，支持拖拽上传、语言选择、实时翻译等功能
"""

import os
import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QProgressBar,
    QMenuBar, QToolBar, QStatusBar, QFileDialog,
    QSplitter, QGroupBox, QComboBox, QCheckBox,
    QMessageBox, QApplication, QTabWidget, QScrollArea,
    QFrame, QGridLayout, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QThread, QTimer, QObject
from PyQt6.QtGui import QAction, QIcon, QPixmap, QDragEnterEvent, QDropEvent, QFont, QImage

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.language_mapping import PADDLEOCR_LANGUAGES, NLLB_LANGUAGES, LATIN_LANGUAGES, PADDLEOCR_TO_NLLB_MAPPING, get_language_mapper
from src.utils.config import get_config
from src.utils.device_manager import get_device_manager
from src.utils.logger import get_logger
from .theme_manager import get_theme_manager
from .carousel_widget import CarouselWidget


class TranslationWorker(QThread):
    """翻译工作线程"""

    # 定义信号
    progress_updated = pyqtSignal(str)  # 进度更新信号
    status_updated = pyqtSignal(str)    # 状态更新信号
    api_status_updated = pyqtSignal(str, bool)  # API状态更新信号 (status_text, show)
    translation_finished = pyqtSignal(object, object, object, object)  # 翻译完成信号 (results, regions, colors, image)
    translation_error = pyqtSignal(str)  # 翻译错误信号

    def __init__(self, image_path, source_lang, target_lang, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.logger = get_logger()

    def run(self):
        """执行翻译流程"""
        try:
            self.perform_translation()
        except Exception as e:
            self.logger.error(f"翻译线程异常: {e}")
            self.translation_error.emit(str(e))

    def perform_translation(self):
        """执行实际的翻译流程"""
        from src.core.translation_pipeline import TranslationPipeline, TranslationPipelineConfig
        from src.core.ocr_factory import get_ocr_factory, OCRFactoryConfig
        from src.utils.config import init_config
        import cv2
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        import os
        import time

        # 初始化配置
        init_config()

        # 获取OCR语言代码（拉丁语需要转换为'la'）
        ocr_lang = self.get_ocr_language_code(self.source_lang)

        # 获取翻译配置参数
        translation_params = self.get_translation_config_params(self.source_lang, self.target_lang)

        self.logger.info(f"开始翻译流程: {self.source_lang} -> {self.target_lang}")
        self.logger.info(f"OCR语言代码: {ocr_lang}")
        self.logger.info(f"翻译参数: {translation_params}")

        # 检查中断
        if self.isInterruptionRequested():
            return

        # 步骤1: 读取和OCR识别
        self.status_updated.emit("正在进行OCR识别...")
        self.progress_updated.emit("OCR识别中...")

        # 读取图像
        image = cv2.imread(self.image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {self.image_path}")

        # OCR识别 - 每次都重新初始化以避免状态冲突
        factory_config = OCRFactoryConfig(default_language=ocr_lang)

        # 重置OCR引擎和工厂以确保干净的状态
        from src.core.ocr_factory import set_ocr_factory_config
        from src.core.ocr_engine import set_ocr_engine_config, OCREngineConfig
        from src.core.paddle_ocr_adapter import set_paddle_ocr_config, PaddleOCRConfig

        # 重置PaddleOCR适配器
        paddle_config = PaddleOCRConfig(
            lang=ocr_lang,
            ocr_version="PP-OCRv5",
            device="cpu"
        )
        set_paddle_ocr_config(paddle_config)

        # 重置OCR引擎
        engine_config = OCREngineConfig(
            default_language=ocr_lang,
            device="cpu",
            ocr_version="PP-OCRv5"
        )
        set_ocr_engine_config(engine_config)

        # 重置OCR工厂
        set_ocr_factory_config(factory_config)

        ocr_factory = get_ocr_factory(factory_config)
        text_regions = ocr_factory.detect_and_recognize(self.image_path, ocr_lang)

        if not text_regions:
            raise ValueError("未识别到任何文本内容")

        self.logger.info(f"OCR识别完成: {len(text_regions)} 个文本区域")

        # 检查中断
        if self.isInterruptionRequested():
            return

        # 步骤2: 背景颜色检测
        self.status_updated.emit("正在检测背景颜色...")
        self.progress_updated.emit("背景颜色检测中...")
        bg_colors = []
        for region in text_regions:
            if self.isInterruptionRequested():
                return
            bg_color = self.detect_background_color(image, region.bbox)
            bg_colors.append(bg_color)

        self.logger.info("背景颜色检测完成")

        # 检查中断
        if self.isInterruptionRequested():
            return

        # 步骤3: 文本翻译
        self.status_updated.emit("正在翻译文本...")
        self.progress_updated.emit("文本翻译中...")
        pipeline_config = TranslationPipelineConfig(**translation_params)
        translation_pipeline = TranslationPipeline(config=pipeline_config)

        # 检查是否使用API模式
        is_api_mode = (translation_pipeline.compressor and
                      hasattr(translation_pipeline.compressor.backend, 'api_client'))

        if is_api_mode:
            # 获取API配置信息
            api_config = self.config.get('models.compression.api', {})
            provider = api_config.get('provider', 'unknown')
            model = api_config.get('model', 'unknown')
            self.logger.info(f"🤖 检测到API模式: {provider} ({model})")

        # 提取文本列表
        texts = [region.text for region in text_regions]

        # 批量翻译
        translation_results = translation_pipeline.translate_batch(
            texts,
            translation_params['source_language'],
            self.target_lang,
            regions=text_regions
        )

        self.logger.info(f"文本翻译完成: {len(translation_results)} 个结果")

        # 如果使用API模式，更新状态显示
        if is_api_mode:
            api_config = self.config.get('models.compression.api', {})
            provider = api_config.get('provider', 'API')
            model = api_config.get('model', 'unknown')
            self.status_updated.emit(f"正在使用{provider.upper()}模型优化翻译...")
            self.progress_updated.emit(f"AI优化中 ({model})...")
            self.api_status_updated.emit(f"🤖 {provider.upper()} {model}", True)
            self.logger.info(f"🚀 开始API文本优化: {provider} {model}")

        # 检查中断
        if self.isInterruptionRequested():
            return

        # 步骤4: 字体匹配
        self.status_updated.emit("正在匹配字体...")
        self.progress_updated.emit("字体匹配中...")
        default_font = self.get_default_font(self.target_lang)

        # 检查中断
        if self.isInterruptionRequested():
            return

        # 步骤5: 图像渲染
        self.status_updated.emit("正在渲染翻译结果...")
        self.progress_updated.emit("图像渲染中...")
        final_image = self.render_translation_result(
            image, text_regions, translation_results, bg_colors,
            default_font, translation_pipeline
        )

        # 检查中断
        if self.isInterruptionRequested():
            return

        self.logger.info("翻译流程完成")

        # 发出完成信号
        self.translation_finished.emit(translation_results, text_regions, bg_colors, final_image)

    def get_ocr_language_code(self, selected_code):
        """获取OCR使用的语言代码"""
        if self.is_latin_language(selected_code):
            return 'la'  # 所有拉丁语在OCR阶段都使用'la'
        return selected_code

    def is_latin_language(self, code):
        """判断是否为拉丁语代码"""
        return code in LATIN_LANGUAGES

    def get_translation_config_params(self, source_code, target_code):
        """获取翻译配置参数"""
        from src.utils.language_mapping import convert_paddleocr_to_nllb

        params = {
            'source_language': source_code,
            'target_language': target_code,
            'specific_latin_language': None
        }

        # 如果源语言是拉丁语，需要特殊处理
        if self.is_latin_language(source_code):
            # 对于拉丁语，OCR使用'la'，但需要记录具体的拉丁语代码
            params['source_language'] = convert_paddleocr_to_nllb('la', source_code)
            params['specific_latin_language'] = source_code
        else:
            # 非拉丁语，直接转换
            params['source_language'] = convert_paddleocr_to_nllb(source_code)

        # 转换目标语言
        params['target_language'] = convert_paddleocr_to_nllb(target_code)

        return params

    def detect_background_color(self, image, bbox):
        """
        检测文本区域周围的背景颜色

        Args:
            image: 图像
            bbox: 文本区域坐标

        Returns:
            背景颜色RGB值
        """
        import cv2
        import numpy as np

        # 创建掩码
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        points = np.array(bbox, dtype=np.int32)
        cv2.fillPoly(mask, [points], 255)

        # 扩展掩码（向外扩展5像素）
        kernel = np.ones((5, 5), np.uint8)
        expanded_mask = cv2.dilate(mask, kernel, iterations=1)

        # 反转掩码（获取背景区域）
        bg_mask = cv2.bitwise_not(mask)
        bg_mask = cv2.bitwise_and(bg_mask, expanded_mask)

        # 提取背景像素
        bg_pixels = image[bg_mask > 0]

        if len(bg_pixels) > 0:
            # 计算背景颜色（中位数）
            bg_color = np.median(bg_pixels, axis=0).astype(int)

            # BGR转RGB
            bg_color_rgb = (int(bg_color[2]), int(bg_color[1]), int(bg_color[0]))
            return bg_color_rgb
        else:
            # 默认白色
            return (255, 255, 255)

    def get_default_font(self, target_lang):
        """获取默认字体"""
        try:
            # 尝试使用新字体管理系统
            from src.core.font_management import get_font_manager
            from src.core.font_matcher import FontInfo

            font_manager = get_font_manager()

            try:
                # 尝试获取Noto字体
                font_info = font_manager.get_font_for_language(target_lang, auto_download=True)

                if font_info:
                    self.logger.info(f"成功获取Noto字体: {font_info.name}")
                    return FontInfo(
                        name=font_info.name,
                        path=font_info.path,
                        family=font_info.name,
                        style=font_info.style,
                        size=24,
                        weight=font_info.weight
                    )
            except RuntimeError as e:
                self.logger.warning(f"Noto字体获取失败: {e}")
                self.logger.warning("使用系统字体作为临时回退...")
        except Exception as e:
            self.logger.warning(f"新字体管理系统初始化失败: {e}")
            self.logger.warning("使用系统字体作为临时回退...")

        # 回退到系统字体
        config = get_config()

        # 从配置文件获取字体路径
        if target_lang in ['zh', 'zho_Hans', 'zho_Hant', 'chinese']:
            font_paths = config.get_font_paths('chinese')
            font_name = "Chinese Font"
        else:
            font_paths = config.get_font_paths('english')
            font_name = "Default Font"

        # 查找可用的字体
        for path in font_paths:
            if os.path.exists(path):
                self.logger.info(f"使用系统字体: {path}")
                from src.core.font_matcher import FontInfo
                return FontInfo(
                    name=font_name,
                    path=path,
                    family=font_name,
                    style="normal",
                    size=24,
                    weight="normal"
                )

        # 如果找不到任何字体，返回None
        self.logger.warning("无法找到任何可用字体")
        return None

    def render_translation_result(self, image, text_regions, translation_results,
                                bg_colors, default_font, translation_pipeline):
        """
        渲染翻译结果到图像上

        Args:
            image: 原始图像
            text_regions: 文本区域列表
            translation_results: 翻译结果列表
            bg_colors: 背景颜色列表
            default_font: 默认字体
            translation_pipeline: 翻译管道

        Returns:
            渲染后的图像
        """
        import cv2
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont

        # 创建PIL图像用于最终渲染
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)

        # 尝试加载字体
        try:
            if default_font and os.path.exists(default_font.path):
                font = ImageFont.truetype(default_font.path, 20)
                self.logger.info(f"使用字体: {default_font.path}")
            else:
                font = ImageFont.load_default()
                self.logger.warning("使用默认字体")
        except Exception as e:
            font = ImageFont.load_default()
            self.logger.warning(f"字体加载失败，使用默认字体: {e}")

        # 渲染每个文本区域
        for i, (region, result, bg_color) in enumerate(zip(text_regions, translation_results, bg_colors)):
            if self.isInterruptionRequested():
                return None

            try:
                # 转换为PIL多边形格式
                pil_box = [(point[0], point[1]) for point in region.bbox]

                # 填充背景
                draw.polygon(pil_box, fill=bg_color)

                # 计算文本位置（多边形中心）
                x_coords = [point[0] for point in region.bbox]
                y_coords = [point[1] for point in region.bbox]
                x_center = sum(x_coords) / len(x_coords)
                y_center = sum(y_coords) / len(y_coords)

                # 获取最终文本
                final_text = translation_pipeline.get_final_text(result)

                # 获取文本尺寸
                bbox = draw.textbbox((0, 0), final_text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                # 计算文本位置（居中）
                text_x = x_center - text_width / 2
                text_y = y_center - text_height / 2

                # 渲染文本
                draw.text((text_x, text_y), final_text, font=font, fill=(0, 0, 0))

                self.logger.debug(f"渲染文本 {i+1}: '{result.original_text}' -> '{final_text}'")

            except Exception as e:
                self.logger.warning(f"渲染文本 {i+1} 失败: {e}")

        # 转换回OpenCV格式
        final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        return final_image


class ImageUploadArea(QWidget):
    """Google翻译风格的图片上传区域"""

    imageDropped = pyqtSignal(str)  # 图像拖拽信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.current_image_path = None
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 上传区域
        self.upload_frame = QFrame()
        self.upload_frame.setFrameStyle(QFrame.Shape.Box)
        self.upload_frame.setStyleSheet("""
            QFrame {
                border: 2px dashed #dadce0;
                border-radius: 8px;
                background-color: #fafbfc;
                min-height: 200px;
            }
            QFrame:hover {
                border-color: #4285f4;
                background-color: #f8f9ff;
            }
        """)

        upload_layout = QVBoxLayout(self.upload_frame)
        upload_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 上传图标和文字
        self.upload_label = QLabel("📷")
        self.upload_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.upload_label.setStyleSheet("font-size: 48px; color: #5f6368; border: none;")
        upload_layout.addWidget(self.upload_label)

        self.upload_text = QLabel("拖拽图片到此处或点击选择")
        self.upload_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.upload_text.setStyleSheet("font-size: 16px; color: #5f6368; border: none; margin-top: 10px;")
        upload_layout.addWidget(self.upload_text)

        # 选择文件按钮
        self.select_btn = QPushButton("选择文件")
        self.select_btn.setStyleSheet("""
            QPushButton {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                margin-top: 15px;
            }
            QPushButton:hover {
                background-color: #3367d6;
            }
            QPushButton:pressed {
                background-color: #2a56c6;
            }
        """)
        self.select_btn.clicked.connect(self.select_file)
        upload_layout.addWidget(self.select_btn, alignment=Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(self.upload_frame)

        # 图片预览区域
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 1px solid #dadce0;
                border-radius: 8px;
                background-color: white;
                min-height: 200px;
            }
        """)
        self.preview_label.hide()
        layout.addWidget(self.preview_label)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = [u.toLocalFile() for u in event.mimeData().urls()]
        if files:
            # 检查是否为图片文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
            for file_path in files:
                if any(file_path.lower().endswith(ext) for ext in image_extensions):
                    self.load_image(file_path)
                    break

    def select_file(self):
        """选择文件对话框"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片文件",
            "",
            "图片文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.webp);;所有文件 (*)"
        )
        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path):
        """加载图片"""
        try:
            self.current_image_path = file_path
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 缩放图片 - 与结果预览保持一致的尺寸
                scaled_pixmap = pixmap.scaled(
                    250, 200,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.preview_label.setPixmap(scaled_pixmap)
                self.preview_label.show()
                self.upload_frame.hide()

                # 发送信号
                self.imageDropped.emit(file_path)
            else:
                raise ValueError("无法加载图片文件")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载图片失败: {str(e)}")

    def clear_image(self):
        """清除图片"""
        self.current_image_path = None
        self.preview_label.clear()
        self.preview_label.hide()
        self.upload_frame.show()





class MainWindow(QMainWindow):
    """Google翻译风格的主窗口"""

    def __init__(self):
        super().__init__()
        self.current_image_path = None
        self.translation_result_path = None

        # 多语言翻译结果存储
        self.multi_translation_results = {}  # {lang_code: {results, regions, colors, image}}

        # 保持向后兼容的单语言结果存储
        self.translation_results = None  # 存储翻译结果
        self.text_regions = None  # 存储OCR识别的文本区域
        self.bg_colors = None  # 存储背景颜色
        self.final_translated_image = None  # 存储最终翻译图像

        self.config = get_config()
        self.theme_manager = get_theme_manager()
        self.logger = get_logger()  # 初始化日志记录器

        # 翻译线程管理
        self.translation_worker = None
        self.is_translating = False

        # 批量翻译管理
        self.batch_translation_queue = []  # 待翻译的目标语言队列
        self.current_batch_index = 0  # 当前翻译的语言索引
        self.is_batch_translating = False  # 是否正在批量翻译

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Image Text Translator - 图像文本翻译工具")
        self.setGeometry(100, 100, 900, 600)  # 更紧凑的窗口尺寸
        self.setMinimumSize(800, 550)  # 设置最小尺寸

        # 创建菜单栏
        self.create_menu_bar()

        # 创建状态栏
        self.create_status_bar()

        # 创建中央部件
        self.create_central_widget()

        # 应用当前主题
        self.apply_theme()

    def create_menu_bar(self):
        """创建简洁的菜单栏"""
        menubar = self.menuBar()
        # 样式将在apply_theme中设置

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        open_action = QAction('打开图片', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_image)
        file_menu.addAction(open_action)

        save_action = QAction('保存结果', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_result)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_action = QAction('设置', self)
        settings_action.triggered.connect(self.open_settings)
        menubar.addAction(settings_action)

        # 帮助菜单
        help_action = QAction('帮助', self)
        help_action.triggered.connect(self.show_about)
        menubar.addAction(help_action)

    def create_central_widget(self):
        """创建Google翻译风格的中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # 样式将在apply_theme中设置

        # 滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(24, 20, 24, 20)  # 更紧凑的边距
        scroll_layout.setSpacing(16)  # 更紧凑的间距



        # 语言选择器区域（与下方图片框对齐）
        self.create_language_selection_area(scroll_layout)

        # 主功能区域（左右分栏布局）
        self.create_main_work_area(scroll_layout)

        # 操作按钮区域
        self.create_action_buttons(scroll_layout)

        # 添加弹性空间
        scroll_layout.addStretch()

        self.scroll_area.setWidget(scroll_content)
        main_layout.addWidget(self.scroll_area)

    def create_language_selection_area(self, layout):
        """创建与图片框对齐的语言选择区域"""
        self.lang_frame = QFrame()
        # 样式将在apply_theme中设置

        lang_layout = QHBoxLayout(self.lang_frame)
        lang_layout.setSpacing(24)  # 与下方图片框间距保持一致

        # 左侧语言选择器（源语言）
        left_lang_container = QWidget()
        left_lang_layout = QVBoxLayout(left_lang_container)
        left_lang_layout.setSpacing(8)
        left_lang_layout.setContentsMargins(0, 0, 0, 0)



        # 源语言选择器
        self.source_combo = QComboBox()
        # 样式将在apply_theme中设置
        left_lang_layout.addWidget(self.source_combo)

        # 右侧语言选择器（目标语言）
        right_lang_container = QWidget()
        right_lang_layout = QVBoxLayout(right_lang_container)
        right_lang_layout.setSpacing(8)
        right_lang_layout.setContentsMargins(0, 0, 0, 0)



        # 目标语言选择器
        self.target_combo = QComboBox()
        # 样式将在apply_theme中设置
        right_lang_layout.addWidget(self.target_combo)

        # 目标语言容器
        self.create_target_language_container(right_lang_layout)

        # 添加到主布局，确保与下方图片框对齐
        lang_layout.addWidget(left_lang_container, 1)  # 拉伸因子1，与左侧图片框对齐
        lang_layout.addWidget(right_lang_container, 1)  # 拉伸因子1，与右侧图片框对齐

        layout.addWidget(self.lang_frame)

        # 加载语言列表
        self.load_languages()

        # 连接信号
        self.source_combo.currentTextChanged.connect(self.on_source_language_changed)
        self.target_combo.currentTextChanged.connect(self.on_target_language_changed)

    def create_target_language_container(self, layout):
        """创建目标语言容器"""
        # 容器标题
        self.container_label = QLabel("已选择的目标语言:")
        self.container_label.setStyleSheet("font-size: 12px; color: #5f6368; margin-top: 8px;")
        layout.addWidget(self.container_label)

        # 滚动区域容器
        self.target_lang_scroll = QScrollArea()
        self.target_lang_scroll.setMaximumHeight(80)
        self.target_lang_scroll.setWidgetResizable(True)
        self.target_lang_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.target_lang_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.target_lang_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: #fafbfc;
            }
        """)

        # 容器内容widget
        self.target_lang_container = QWidget()
        self.target_lang_layout = QVBoxLayout(self.target_lang_container)
        self.target_lang_layout.setContentsMargins(8, 8, 8, 8)
        self.target_lang_layout.setSpacing(4)

        # 流式布局容器
        self.target_lang_flow_widget = QWidget()
        self.target_lang_flow_layout = QHBoxLayout(self.target_lang_flow_widget)
        self.target_lang_flow_layout.setContentsMargins(0, 0, 0, 0)
        self.target_lang_flow_layout.setSpacing(6)
        self.target_lang_flow_layout.addStretch()  # 添加弹性空间

        self.target_lang_layout.addWidget(self.target_lang_flow_widget)
        self.target_lang_layout.addStretch()  # 添加垂直弹性空间

        self.target_lang_scroll.setWidget(self.target_lang_container)
        layout.addWidget(self.target_lang_scroll)

        # 初始化选中的目标语言列表
        self.selected_target_languages = []  # 存储选中的目标语言代码和名称

        # 初始隐藏容器
        self.container_label.hide()
        self.target_lang_scroll.hide()

    def load_languages(self):
        """加载语言列表"""
        # 加载OCR支持的语言（源语言）
        self.source_combo.clear()

        # 添加默认选项
        self.source_combo.addItem("请选择语言", None)

        # 添加普通语言（跳过拉丁语'la'，因为需要特殊处理）
        for code, (zh_name, en_name) in PADDLEOCR_LANGUAGES.items():
            if code != 'la':  # 跳过笼统的拉丁语选项
                display_name = f"{zh_name}"
                self.source_combo.addItem(display_name, code)

        # 单独添加4个具体的拉丁语选项
        for latin_code, (zh_name, en_name) in LATIN_LANGUAGES.items():
            display_name = f"{zh_name}"
            self.source_combo.addItem(display_name, latin_code)

        # 初始化目标语言ComboBox（初始为禁用状态）
        self.target_combo.clear()
        self.target_combo.addItem("请选择语言", None)
        self.target_combo.setEnabled(False)  # 初始禁用，直到选择源语言

        # 设置默认选择为"请选择语言"
        self.source_combo.setCurrentIndex(0)
        self.target_combo.setCurrentIndex(0)

    def add_target_language(self, lang_code, lang_name):
        """添加目标语言到容器"""
        # 检查是否已经存在
        for existing_code, existing_name in self.selected_target_languages:
            if existing_code == lang_code:
                return  # 已存在，不重复添加

        # 添加到列表
        self.selected_target_languages.append((lang_code, lang_name))

        # 创建语言标签
        lang_tag = self.create_language_tag(lang_code, lang_name)

        # 插入到布局中（在stretch之前）
        count = self.target_lang_flow_layout.count()
        self.target_lang_flow_layout.insertWidget(count - 1, lang_tag)

        # 显示容器
        self.container_label.show()  # 显示标题
        self.target_lang_scroll.show()

        self.logger.info(f"添加目标语言: {lang_name} ({lang_code})")

    def create_language_tag(self, lang_code, lang_name):
        """创建语言标签"""
        tag_widget = QWidget()
        tag_layout = QHBoxLayout(tag_widget)
        tag_layout.setContentsMargins(8, 4, 8, 4)
        tag_layout.setSpacing(4)

        # 语言名称标签
        name_label = QLabel(lang_name)
        # 样式将在apply_theme中设置
        name_label.setObjectName("language_tag_label")

        # 删除按钮
        remove_btn = QPushButton("×")
        remove_btn.setFixedSize(16, 16)
        # 样式将在apply_theme中设置
        remove_btn.setObjectName("language_tag_remove_btn")

        # 连接删除事件
        remove_btn.clicked.connect(lambda: self.remove_target_language(lang_code))

        tag_layout.addWidget(name_label)
        tag_layout.addWidget(remove_btn)

        # 设置标签样式
        # 样式将在apply_theme中设置
        tag_widget.setObjectName("language_tag_widget")

        # 立即应用当前主题样式
        self._apply_single_language_tag_style(tag_widget, name_label, remove_btn)

        return tag_widget

    def remove_target_language(self, lang_code):
        """从容器中移除目标语言"""
        # 从列表中移除
        self.selected_target_languages = [
            (code, name) for code, name in self.selected_target_languages
            if code != lang_code
        ]

        # 重建UI
        self.rebuild_target_language_container()

        self.logger.info(f"移除目标语言: {lang_code}")

    def rebuild_target_language_container(self):
        """重建目标语言容器UI"""
        # 清空现有的标签
        while self.target_lang_flow_layout.count() > 1:  # 保留最后的stretch
            child = self.target_lang_flow_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 重新添加所有标签
        for lang_code, lang_name in self.selected_target_languages:
            lang_tag = self.create_language_tag(lang_code, lang_name)
            count = self.target_lang_flow_layout.count()
            self.target_lang_flow_layout.insertWidget(count - 1, lang_tag)

        # 如果没有语言，隐藏容器
        if not self.selected_target_languages:
            self.container_label.hide()  # 隐藏标题
            self.target_lang_scroll.hide()

    def clean_invalid_target_languages(self, new_source_nllb):
        """清理容器中与新源语言相同的目标语言"""
        # 获取当前目标语言下拉框中的有效语言代码
        valid_target_codes = set()
        for i in range(1, self.target_combo.count()):  # 跳过"请选择语言"
            code = self.target_combo.itemData(i)
            if code:
                valid_target_codes.add(code)

        # 过滤掉无效的目标语言
        original_count = len(self.selected_target_languages)
        self.selected_target_languages = [
            (code, name) for code, name in self.selected_target_languages
            if code in valid_target_codes and code != new_source_nllb
        ]

        # 如果有语言被移除，重建容器
        if len(self.selected_target_languages) != original_count:
            self.rebuild_target_language_container()
            removed_count = original_count - len(self.selected_target_languages)
            self.logger.info(f"源语言变更，移除了 {removed_count} 个无效的目标语言")

    def get_corresponding_nllb_code(self, paddleocr_code):
        """获取PaddleOCR代码对应的NLLB代码"""
        if paddleocr_code is None:
            return None

        # 检查是否在映射表中
        if paddleocr_code in PADDLEOCR_TO_NLLB_MAPPING:
            return PADDLEOCR_TO_NLLB_MAPPING[paddleocr_code]

        # 检查是否是拉丁语
        if paddleocr_code in LATIN_LANGUAGES:
            return paddleocr_code  # 拉丁语代码直接对应NLLB代码

        return None

    def update_target_languages(self, source_code):
        """根据源语言更新目标语言列表"""
        # 清空目标语言列表
        self.target_combo.clear()

        # 添加默认选项
        self.target_combo.addItem("请选择语言", None)

        # 获取源语言对应的NLLB代码（用于过滤）
        source_nllb_code = self.get_corresponding_nllb_code(source_code)

        # 常用语言放在前面
        common_languages = [
            ('zho_Hans', '中文简体'),
            ('eng_Latn', '英语'),
            ('jpn_Jpan', '日语'),
            ('kor_Hang', '韩语'),
            ('fra_Latn', '法语'),
            ('deu_Latn', '德语'),
            ('spa_Latn', '西班牙语'),
            ('rus_Cyrl', '俄语')
        ]

        # 添加常用语言（排除与源语言相同的）
        added_common = 0
        for code, name in common_languages:
            if code in NLLB_LANGUAGES and code != source_nllb_code:
                zh_name, _ = NLLB_LANGUAGES[code]
                display_name = f"{zh_name}"
                self.target_combo.addItem(display_name, code)
                added_common += 1

        # 如果有常用语言被添加，添加分隔符
        if added_common > 0:
            self.target_combo.insertSeparator(added_common + 1)  # +1 因为有默认选项

        # 添加其他语言（排除与源语言相同的和已添加的常用语言）
        common_codes = [lang[0] for lang in common_languages]
        for code, (zh_name, en_name) in NLLB_LANGUAGES.items():
            if code not in common_codes and code != source_nllb_code:
                display_name = f"{zh_name}"
                self.target_combo.addItem(display_name, code)

        # 启用目标语言选择器并重置为默认选项
        self.target_combo.setEnabled(True)
        self.target_combo.setCurrentIndex(0)

    def on_source_language_changed(self):
        """源语言选择改变处理"""
        source_code = self.source_combo.currentData()

        if source_code is None:
            # 如果选择了"请选择语言"，禁用目标语言选择器
            self.target_combo.clear()
            self.target_combo.addItem("请选择语言", None)
            self.target_combo.setEnabled(False)
            self.target_combo.setCurrentIndex(0)

            # 清空目标语言容器
            self.selected_target_languages = []
            self.rebuild_target_language_container()

            self.update_status_bar("请选择源语言")
        else:
            # 选择了具体语言，更新目标语言列表
            old_source_code = None
            if self.selected_target_languages:
                # 获取当前源语言对应的NLLB代码
                old_source_code = self.get_corresponding_nllb_code(source_code)

            self.update_target_languages(source_code)

            source_text = self.source_combo.currentText()
            self.logger.info(f"源语言选择变更: {source_text} (代码: {source_code})")

            # 如果是拉丁语，记录特殊处理信息
            if self.is_latin_language(source_code):
                ocr_code = self.get_ocr_language_code(source_code)
                self.logger.info(f"拉丁语特殊处理: OCR使用'{ocr_code}', 翻译使用'{source_code}'")

            # 清理容器中无效的目标语言
            if old_source_code:
                new_source_nllb = self.get_corresponding_nllb_code(source_code)
                self.clean_invalid_target_languages(new_source_nllb)

            # 更新状态栏
            selected_count = len(self.selected_target_languages)
            if selected_count > 0:
                self.update_status_bar(f"已选择源语言: {source_text}，已选择 {selected_count} 个目标语言")
            else:
                self.update_status_bar(f"已选择源语言: {source_text}")

    def on_target_language_changed(self):
        """目标语言选择改变处理"""
        source_code = self.source_combo.currentData()
        target_code = self.target_combo.currentData()

        if source_code and target_code:
            target_text = self.target_combo.currentText()

            # 添加到目标语言容器
            self.add_target_language(target_code, target_text)

            # 重置下拉框为"请选择语言"
            self.target_combo.setCurrentIndex(0)

            # 更新状态栏
            selected_count = len(self.selected_target_languages)
            self.update_status_bar(f"已选择 {selected_count} 个目标语言")

            self.logger.info(f"目标语言已添加: {target_text} (代码: {target_code})")

        elif target_code is None:
            # 选择了"请选择语言"
            if source_code:
                source_text = self.source_combo.currentText()
                selected_count = len(self.selected_target_languages)
                if selected_count > 0:
                    self.update_status_bar(f"已选择源语言: {source_text}，已选择 {selected_count} 个目标语言")
                else:
                    self.update_status_bar(f"已选择源语言: {source_text}，请选择目标语言")
            else:
                self.update_status_bar("请选择语言")

    def create_main_work_area(self, layout):
        """创建主功能区域（左右完全对称的分栏布局）"""
        self.work_frame = QFrame()
        # 样式将在apply_theme中设置

        work_layout = QHBoxLayout(self.work_frame)
        work_layout.setSpacing(24)  # 左右区域间距

        # 创建左右两个容器，确保完全对称
        left_container = QWidget()
        right_container = QWidget()

        # 创建左侧布局
        left_layout = QVBoxLayout()
        left_container.setLayout(left_layout)
        self.create_upload_section(left_layout)

        # 创建右侧布局
        right_layout = QVBoxLayout()
        right_container.setLayout(right_layout)
        self.create_preview_section(right_layout)

        # 添加到主布局，设置相等的拉伸因子确保完全对称
        work_layout.addWidget(left_container, 1)  # 拉伸因子1
        work_layout.addWidget(right_container, 1)  # 拉伸因子1

        layout.addWidget(self.work_frame)

    def create_upload_section(self, layout):
        """创建左侧上传区域"""
        upload_container = QWidget()
        upload_layout = QVBoxLayout(upload_container)
        upload_layout.setSpacing(8)
        upload_layout.setContentsMargins(0, 0, 0, 0)



        # 拖拽上传区域 - 完全对称的大小
        self.upload_area = QFrame()
        self.upload_area.setAcceptDrops(True)
        self.upload_area.setMinimumHeight(280)  # 更大的高度，完全对称
        self.upload_area.setStyleSheet("""
            QFrame {
                border: 2px dashed #dadce0;
                border-radius: 8px;
                background-color: #fafbfc;
            }
            QFrame:hover {
                border-color: #4285f4;
                background-color: #f8f9ff;
            }
        """)

        upload_area_layout = QVBoxLayout(self.upload_area)
        upload_area_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        upload_area_layout.setSpacing(12)

        # 上传的图片显示区域
        self.upload_image_label = QLabel()
        self.upload_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.upload_image_label.setStyleSheet("""
            QLabel {
                border: none;
                background-color: transparent;
            }
        """)
        self.upload_image_label.hide()
        upload_area_layout.addWidget(self.upload_image_label)

        # 默认上传提示
        self.upload_placeholder = QLabel("📷\n\n拖拽图片到此处\n或点击选择文件")
        self.upload_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.upload_placeholder.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #9aa0a6;
                border: none;
                line-height: 1.4;
            }
        """)
        upload_area_layout.addWidget(self.upload_placeholder)

        # 选择文件按钮
        self.select_file_btn = QPushButton("选择文件")
        self.select_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #3367d6;
            }
            QPushButton:pressed {
                background-color: #2a56c6;
            }
        """)
        self.select_file_btn.clicked.connect(self.select_image_file)
        upload_area_layout.addWidget(self.select_file_btn, alignment=Qt.AlignmentFlag.AlignCenter)

        upload_layout.addWidget(self.upload_area)

        # 当前图片信息
        self.image_info_label = QLabel("")
        self.image_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_info_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #34a853;
                margin-top: 6px;
                padding: 4px 8px;
                background-color: #e8f5e8;
                border-radius: 4px;
            }
        """)
        self.image_info_label.hide()
        upload_layout.addWidget(self.image_info_label)

        layout.addWidget(upload_container)
        layout.addStretch()  # 添加弹性空间

    def create_preview_section(self, layout):
        """创建右侧预览区域"""
        preview_container = QWidget()
        preview_layout = QVBoxLayout(preview_container)
        preview_layout.setSpacing(8)
        preview_layout.setContentsMargins(0, 0, 0, 0)

        # 轮播图组件 - 替换原来的预览区域
        self.carousel_widget = CarouselWidget()
        # 连接轮播图切换信号
        self.carousel_widget.currentChanged.connect(self.on_carousel_changed)
        preview_layout.addWidget(self.carousel_widget)

        # 翻译状态和进度
        self.create_translation_status(preview_layout)

        layout.addWidget(preview_container)

    def create_translation_status(self, layout):
        """创建翻译状态区域"""
        self.status_frame = QFrame()
        # 样式将在apply_theme中设置
        self.status_frame.hide()  # 初始时隐藏整个状态框架

        status_layout = QVBoxLayout(self.status_frame)
        status_layout.setSpacing(4)
        status_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距

        # 状态标签容器
        status_container = QHBoxLayout()
        status_container.setContentsMargins(0, 0, 0, 0)
        status_container.setSpacing(8)

        # 状态标签（初始时隐藏）
        self.translation_status_label = QLabel("")
        # 样式将在apply_theme中设置
        self.translation_status_label.hide()  # 初始时隐藏
        status_container.addWidget(self.translation_status_label)

        # API状态指示器（初始时隐藏）
        self.api_status_label = QLabel("")
        self.api_status_label.hide()  # 初始时隐藏
        self.api_status_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                color: #1976d2;
                border: 1px solid #bbdefb;
                border-radius: 12px;
                padding: 2px 8px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        status_container.addWidget(self.api_status_label)

        status_container.addStretch()  # 添加弹性空间

        # 将容器添加到状态布局
        status_widget = QWidget()
        status_widget.setLayout(status_container)
        status_layout.addWidget(status_widget)

        # 进度条
        self.translation_progress = QProgressBar()
        self.translation_progress.setFixedHeight(4)
        # 样式将在apply_theme中设置
        self.translation_progress.setVisible(False)
        status_layout.addWidget(self.translation_progress)

        layout.addWidget(self.status_frame)

    def create_action_buttons(self, layout):
        """创建操作按钮区域"""
        self.button_frame = QFrame()
        # 样式将在apply_theme中设置

        button_layout = QHBoxLayout(self.button_frame)
        button_layout.setSpacing(12)

        # 重新识别并渲染按钮
        self.retranslate_btn = QPushButton("🚀 开始翻译")
        self.retranslate_btn.setEnabled(False)
        # 初始样式（蓝色）
        self.retranslate_btn_normal_style = """
            QPushButton {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #3367d6;
            }
            QPushButton:pressed {
                background-color: #2a56c6;
            }
            QPushButton:disabled {
                background-color: #dadce0;
                color: #9aa0a6;
            }
        """
        # 停止状态样式（红色）
        self.retranslate_btn_stop_style = """
            QPushButton {
                background-color: #ea4335;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #d33b2c;
            }
            QPushButton:pressed {
                background-color: #b52d20;
            }
        """
        self.retranslate_btn.setStyleSheet(self.retranslate_btn_normal_style)
        self.retranslate_btn.clicked.connect(self.on_retranslate_btn_clicked)

        # 保存翻译图按钮
        self.save_result_btn = QPushButton("💾 保存翻译图")
        self.save_result_btn.setEnabled(False)
        self.save_result_btn.setStyleSheet("""
            QPushButton {
                background-color: #34a853;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
                min-width: 110px;
            }
            QPushButton:hover {
                background-color: #2d8f47;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #dadce0;
                color: #9aa0a6;
            }
        """)
        self.save_result_btn.clicked.connect(self.save_result)

        # 清除按钮
        self.clear_btn = QPushButton("🗑 清除")
        self.clear_btn.setEnabled(False)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #ea4335;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #d33b2c;
            }
            QPushButton:pressed {
                background-color: #b52d20;
            }
            QPushButton:disabled {
                background-color: #dadce0;
                color: #9aa0a6;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_all)

        # 添加按钮到布局
        button_layout.addWidget(self.retranslate_btn)
        button_layout.addWidget(self.save_result_btn)
        button_layout.addStretch()  # 添加弹性空间
        button_layout.addWidget(self.clear_btn)

        layout.addWidget(self.button_frame)



    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        # 样式将在apply_theme中设置

        # 获取设备信息
        device_manager = get_device_manager()
        device_info = device_manager.get_device_info()

        # 创建设备信息标签
        device_text = f"推理设备: {device_info.device_name} ({device_info.device_type.upper()})"

        # 添加总内存信息（转换为GB）
        if device_info.memory_total:
            memory_total_gb = device_info.memory_total / 1024  # MB转GB
            device_text += f" | 总内存: {memory_total_gb:.1f}GB"

        # 保存设备信息文本
        self.device_text = device_text

        # 创建永久标签而不是使用临时消息
        self.status_label = QLabel(f"就绪 | {device_text}")
        self.status_bar.addWidget(self.status_label)

    def update_status_bar(self, message, timeout=0):
        """更新状态栏消息，保持设备信息显示

        Args:
            message: 要显示的消息
            timeout: 消息显示超时时间（毫秒），0表示永久显示
        """
        full_message = f"{message} | {self.device_text}"
        # 更新永久标签的文本
        if hasattr(self, 'status_label'):
            self.status_label.setText(full_message)

    def setup_connections(self):
        """设置信号连接"""
        # 设置拖拽事件
        self.upload_area.dragEnterEvent = self.dragEnterEvent
        self.upload_area.dropEvent = self.dropEvent
        self.upload_area.mousePressEvent = self.upload_area_clicked

    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """拖拽放下事件"""
        files = [u.toLocalFile() for u in event.mimeData().urls()]
        if files:
            # 检查是否为图片文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp', '.gif'}
            for file_path in files:
                if any(file_path.lower().endswith(ext) for ext in image_extensions):
                    self.load_image(file_path)
                    break

    def upload_area_clicked(self, event):
        """上传区域点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.select_image_file()

    def select_image_file(self):
        """选择图片文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片文件",
            "",
            "图片文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.webp *.gif);;所有文件 (*)"
        )
        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path):
        """加载图片"""
        try:
            self.current_image_path = file_path

            # 显示图片信息
            import os
            file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
            file_name = os.path.basename(file_path)
            self.image_info_label.setText(f"✓ {file_name} ({file_size:.1f} MB)")
            self.image_info_label.show()

            # 在左侧上传区域显示上传的图片
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 缩放图片以适应上传区域
                scaled_pixmap = pixmap.scaled(
                    250, 200,  # 适合上传区域的尺寸
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.upload_image_label.setPixmap(scaled_pixmap)
                self.upload_image_label.show()
                self.upload_placeholder.hide()  # 隐藏上传提示
                self.select_file_btn.hide()  # 隐藏选择文件按钮

            # 启用按钮
            self.retranslate_btn.setEnabled(True)
            self.clear_btn.setEnabled(True)

            # 更新状态
            self.translation_status_label.setText(f"已加载图片: {file_name}")
            self.translation_status_label.show()  # 显示状态标签
            self.update_status_bar(f"已加载图片: {file_name}")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载图片失败: {str(e)}")
            self.translation_status_label.setText("图片加载失败")

    def clear_all(self):
        """清除所有内容"""
        # 清除图片路径和翻译结果
        self.current_image_path = None
        self.translation_result_path = None
        self.translation_results = None
        self.text_regions = None
        self.bg_colors = None
        self.final_translated_image = None

        # 重置按钮文本为初始状态
        self.retranslate_btn.setText("🚀 开始翻译")

        # 重置左侧上传区域
        self.upload_image_label.clear()
        self.upload_image_label.hide()
        self.upload_placeholder.show()
        self.select_file_btn.show()
        self.image_info_label.hide()

        # 清空轮播图
        self.carousel_widget.clear_all_results()

        # 清空多语言翻译结果
        self.multi_translation_results.clear()

        # 重置批量翻译状态
        self.batch_translation_queue = []
        self.current_batch_index = 0
        self.is_batch_translating = False

        # 禁用按钮
        self.retranslate_btn.setEnabled(False)
        self.save_result_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)

        # 重置状态
        self.translation_status_label.setText("")
        self.translation_status_label.hide()  # 隐藏状态标签
        self.translation_progress.setVisible(False)
        self.status_frame.hide()  # 隐藏整个状态框架
        self.update_status_bar("就绪")



    def is_latin_language(self, code):
        """判断是否为拉丁语代码"""
        return code in LATIN_LANGUAGES

    def get_ocr_language_code(self, selected_code):
        """获取OCR使用的语言代码"""
        if self.is_latin_language(selected_code):
            return 'la'  # 所有拉丁语在OCR阶段都使用'la'
        return selected_code

    def get_translation_config_params(self, source_code, target_code):
        """获取翻译配置参数"""
        from src.utils.language_mapping import convert_paddleocr_to_nllb

        params = {
            'source_language': source_code,
            'target_language': target_code,
            'specific_latin_language': None
        }

        # 如果源语言是拉丁语，需要特殊处理
        if self.is_latin_language(source_code):
            # 对于拉丁语，OCR使用'la'，但需要记录具体的拉丁语代码
            params['source_language'] = convert_paddleocr_to_nllb('la', source_code)
            params['specific_latin_language'] = source_code
        else:
            # 对于普通语言，转换PaddleOCR语言代码为NLLB格式
            try:
                nllb_source_lang = convert_paddleocr_to_nllb(source_code)
                params['source_language'] = nllb_source_lang
                self.logger.info(f"语言代码转换: {source_code} -> {nllb_source_lang}")
            except ValueError as e:
                self.logger.warning(f"语言代码转换失败: {e}")
                # 如果转换失败，保持原代码（可能已经是NLLB格式）
                params['source_language'] = source_code

        # 添加压缩配置参数（从应用配置中获取）
        config = self.config
        params.update({
            'compression_enabled': config.get('translation.enable_compression', True),
            'compression_threshold': config.get('translation.compression_threshold', 1.5),
            'compression_model': config.get('models.compression.default_model'),
            'preserve_formatting': config.get('translation.preserve_formatting', True),
            'batch_size': config.get('translation.batch_size', 32),
            'timeout': config.get('translation.timeout', 300)
        })

        return params

    def get_selected_languages(self):
        """获取选中的语言"""
        source_code = self.source_combo.currentData()
        target_code = self.target_combo.currentData()

        # 如果任一语言为None（即选择了"请选择语言"），返回None
        if source_code is None or target_code is None:
            return None, None

        return source_code, target_code

    def open_image(self):
        """打开图片文件"""
        self.select_image_file()

    def on_retranslate_btn_clicked(self):
        """重新识别并渲染按钮点击处理"""
        if self.is_translating:
            # 当前正在翻译，执行停止操作
            self.stop_translation()
        else:
            # 当前未在翻译，开始翻译
            self.start_translation()

    def start_translation(self):
        """开始翻译"""
        if not self.current_image_path:
            QMessageBox.warning(self, "警告", "请先选择要翻译的图片")
            return

        # 获取源语言
        source_lang = self.source_combo.currentData()
        if not source_lang:
            QMessageBox.warning(self, "警告", "请选择源语言")
            return

        # 检查是否有选中的目标语言
        if not self.selected_target_languages:
            QMessageBox.warning(self, "警告", "请至少选择一个目标语言")
            return

        # 清空轮播图中的旧结果
        self.carousel_widget.clear_all_results()

        # 开始批量翻译
        self.start_batch_translation(source_lang, self.selected_target_languages)

    def start_batch_translation(self, source_lang, target_languages):
        """开始批量翻译"""
        # 初始化批量翻译状态
        self.batch_translation_queue = target_languages.copy()
        self.current_batch_index = 0
        self.is_batch_translating = True

        # 设置翻译状态
        self.set_translation_running(True)

        self.logger.info(f"开始批量翻译: {len(target_languages)} 个目标语言")

        # 开始第一个翻译任务
        self.start_next_translation_task(source_lang)

    def start_next_translation_task(self, source_lang):
        """开始下一个翻译任务"""
        if self.current_batch_index >= len(self.batch_translation_queue):
            # 所有任务完成
            self.finish_batch_translation()
            return

        # 获取当前要翻译的目标语言
        current_target_lang, current_target_name = self.batch_translation_queue[self.current_batch_index]

        # 更新状态显示
        progress_text = f"正在翻译 {self.current_batch_index + 1}/{len(self.batch_translation_queue)}: {self.source_combo.currentText()} → {current_target_name}"
        self.translation_status_label.setText(progress_text)
        self.update_status_bar(progress_text)

        self.logger.info(f"开始翻译任务 {self.current_batch_index + 1}/{len(self.batch_translation_queue)}: {source_lang} -> {current_target_lang}")

        # 创建并启动翻译工作线程
        self.translation_worker = TranslationWorker(
            self.current_image_path, source_lang, current_target_lang, self
        )

        # 连接信号
        self.translation_worker.progress_updated.connect(self.on_progress_updated)
        self.translation_worker.status_updated.connect(self.on_status_updated)
        self.translation_worker.api_status_updated.connect(self.on_api_status_updated)
        self.translation_worker.translation_finished.connect(self.on_batch_translation_finished)
        self.translation_worker.translation_error.connect(self.on_batch_translation_error)

        # 启动线程
        self.translation_worker.start()

    def finish_batch_translation(self):
        """完成批量翻译"""
        self.is_batch_translating = False
        self.batch_translation_queue = []
        self.current_batch_index = 0

        # 重置翻译状态
        self.set_translation_running(False)

        # 更新状态 - 显示当前语言信息
        self.update_translation_status_display()

        result_count = self.carousel_widget.get_result_count()
        self.update_status_bar(f"批量翻译完成，共 {result_count} 个结果")

        # 启用保存按钮
        self.save_result_btn.setEnabled(True)

        self.logger.info(f"批量翻译完成，共生成 {result_count} 个翻译结果")

    def update_translation_status_display(self):
        """更新翻译状态显示"""
        result_count = self.carousel_widget.get_result_count()

        if result_count == 0:
            self.translation_status_label.setText("")
        elif result_count == 1:
            # 单个结果，显示语言信息
            _, lang_name = self.carousel_widget.get_current_language_info()
            if lang_name:
                self.translation_status_label.setText(f"翻译完成: {lang_name}")
            else:
                self.translation_status_label.setText("翻译完成")
        else:
            # 多个结果，显示当前语言和总数
            _, lang_name = self.carousel_widget.get_current_language_info()
            current_index = self.carousel_widget.current_index + 1
            if lang_name:
                self.translation_status_label.setText(f"当前显示: {lang_name} ({current_index}/{result_count})")
            else:
                self.translation_status_label.setText(f"翻译完成 ({current_index}/{result_count})")

    def on_carousel_changed(self, index):
        """处理轮播图切换事件"""
        # 更新状态显示
        self.update_translation_status_display()

    def on_batch_translation_finished(self, translation_results, text_regions, bg_colors, final_image):
        """处理批量翻译中单个任务完成"""
        # 获取当前翻译的目标语言信息
        current_target_lang, current_target_name = self.batch_translation_queue[self.current_batch_index]

        # 保存当前结果（保持向后兼容）
        self.translation_results = translation_results
        self.text_regions = text_regions
        self.bg_colors = bg_colors
        self.final_translated_image = final_image

        # 添加到轮播图
        if final_image is not None:
            self.carousel_widget.add_translation_result(
                current_target_lang,
                current_target_name,
                final_image,
                translation_results,
                text_regions,
                bg_colors
            )
            self.logger.info(f"翻译结果已添加到轮播图: {current_target_name}")

        # 移动到下一个任务
        self.current_batch_index += 1

        # 继续下一个翻译任务
        source_lang = self.source_combo.currentData()
        self.start_next_translation_task(source_lang)

    def on_batch_translation_error(self, error_message):
        """处理批量翻译中单个任务错误"""
        _, current_target_name = self.batch_translation_queue[self.current_batch_index]

        self.logger.error(f"翻译任务失败 ({current_target_name}): {error_message}")

        # 询问用户是否继续
        reply = QMessageBox.question(
            self,
            "翻译错误",
            f"翻译到 {current_target_name} 时发生错误:\n{error_message}\n\n是否继续翻译其他语言？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 继续下一个任务
            self.current_batch_index += 1
            source_lang = self.source_combo.currentData()
            self.start_next_translation_task(source_lang)
        else:
            # 停止批量翻译
            self.finish_batch_translation()

    def stop_translation(self):
        """停止翻译"""
        if self.translation_worker and self.translation_worker.isRunning():
            self.logger.info("正在停止翻译...")
            self.translation_status_label.setText("正在停止翻译...")

            # 请求线程中断
            self.translation_worker.requestInterruption()

            # 等待线程结束（最多等待3秒）
            if self.translation_worker.wait(3000):
                self.logger.info("翻译已成功停止")
            else:
                self.logger.warning("翻译线程停止超时，强制终止")
                self.translation_worker.terminate()
                self.translation_worker.wait()

            # 如果是批量翻译，重置批量翻译状态
            if self.is_batch_translating:
                self.is_batch_translating = False
                self.batch_translation_queue = []
                self.current_batch_index = 0
                self.logger.info("批量翻译已停止")

            # 重置状态
            self.set_translation_running(False)
            self.translation_status_label.setText("翻译已停止")
            self.update_status_bar("翻译已停止")

    def set_translation_running(self, running):
        """设置翻译运行状态"""
        self.is_translating = running

        if running:
            # 翻译开始状态
            self.retranslate_btn.setText("⏹ 停止")
            self.retranslate_btn.setStyleSheet(self.retranslate_btn_stop_style)
            self.retranslate_btn.setEnabled(True)  # 停止按钮始终可用

            # 禁用其他按钮
            self.save_result_btn.setEnabled(False)
            self.clear_btn.setEnabled(False)
            self.select_file_btn.setEnabled(False)

            # 显示进度
            self.status_frame.show()
            self.translation_progress.setVisible(True)
            self.translation_progress.setRange(0, 0)  # 不确定进度
            self.translation_status_label.setText("正在翻译，请稍候...")
            self.translation_status_label.show()
            self.update_status_bar("正在翻译...")

        else:
            # 翻译结束状态 - 根据是否有翻译结果决定按钮文本
            if self.final_translated_image is not None:
                self.retranslate_btn.setText("🔄 重新识别并渲染")
            else:
                self.retranslate_btn.setText("🚀 开始翻译")

            self.retranslate_btn.setStyleSheet(self.retranslate_btn_normal_style)
            self.retranslate_btn.setEnabled(True)

            # 恢复其他按钮状态
            self.clear_btn.setEnabled(True)
            self.select_file_btn.setEnabled(True)

            # 隐藏进度
            self.translation_progress.setVisible(False)

    def on_progress_updated(self, progress_text):
        """处理进度更新信号"""
        # 可以在这里更新进度条或其他进度指示器
        pass

    def on_status_updated(self, status_text):
        """处理状态更新信号"""
        self.translation_status_label.setText(status_text)
        self.update_status_bar(status_text)

    def on_api_status_updated(self, status_text, show):
        """处理API状态更新信号"""
        if show:
            self.api_status_label.setText(status_text)
            self.api_status_label.show()
        else:
            self.api_status_label.hide()

    def on_translation_finished(self, translation_results, text_regions, bg_colors, final_image):
        """处理翻译完成信号"""
        # 保存结果
        self.translation_results = translation_results
        self.text_regions = text_regions
        self.bg_colors = bg_colors
        self.final_translated_image = final_image

        # 重置翻译状态
        self.set_translation_running(False)

        # 更新状态
        self.translation_status_label.setText("翻译完成！")
        self.update_status_bar("翻译完成")

        # 隐藏API状态指示器
        self.api_status_label.hide()

        # 启用保存按钮
        self.save_result_btn.setEnabled(True)

        # 显示翻译结果预览
        self.display_translation_result()

        self.logger.info("翻译流程完成")

    def on_translation_error(self, error_message):
        """处理翻译错误信号"""
        # 重置翻译状态
        self.set_translation_running(False)

        # 更新状态
        self.translation_status_label.setText(f"翻译失败: {error_message}")
        self.update_status_bar(f"翻译失败: {error_message}")

        # 显示错误消息
        QMessageBox.critical(self, "翻译错误", f"翻译过程中发生错误:\n{error_message}")

        self.logger.error(f"翻译失败: {error_message}")

    def display_translation_result(self):
        """显示翻译结果预览"""
        try:
            if self.final_translated_image is not None:
                # 将OpenCV图像转换为QPixmap
                import cv2

                # 转换颜色格式 (BGR -> RGB)
                rgb_image = cv2.cvtColor(self.final_translated_image, cv2.COLOR_BGR2RGB)

                # 转换为QImage
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)

                # 转换为QPixmap并缩放
                pixmap = QPixmap.fromImage(qt_image)
                scaled_pixmap = pixmap.scaled(
                    250, 200,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                # 获取当前目标语言信息（用于单语言兼容）
                current_target_lang = None
                current_target_name = None

                # 如果有选中的目标语言，使用第一个作为当前语言
                if self.selected_target_languages:
                    current_target_lang, current_target_name = self.selected_target_languages[0]
                else:
                    # 回退到下拉框选择的语言
                    current_target_lang = self.target_combo.currentData()
                    current_target_name = self.target_combo.currentText()

                if current_target_lang and current_target_name:
                    # 添加到轮播图
                    self.carousel_widget.add_translation_result(
                        current_target_lang,
                        current_target_name,
                        self.final_translated_image,
                        self.translation_results,
                        self.text_regions,
                        self.bg_colors
                    )

                    self.logger.info("翻译结果已添加到轮播图")

                    # 显示翻译统计信息
                    if self.translation_results and self.text_regions:
                        stats_text = f"识别文本: {len(self.text_regions)} 个区域, 翻译: {len(self.translation_results)} 个结果"
                        self.translation_status_label.setText(f"翻译完成！{stats_text}")
                else:
                    self.logger.warning("无法确定目标语言，无法显示结果")

            else:
                self.logger.warning("没有翻译结果图像可显示")

        except Exception as e:
            self.logger.error(f"显示翻译预览失败: {e}")
            # 回退显示原图
            if self.current_image_path:
                pixmap = QPixmap(self.current_image_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(
                        250, 200,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.preview_image_label.setPixmap(scaled_pixmap)
                    self.preview_image_label.show()
                    self.preview_placeholder.hide()








    def save_result(self):
        """保存翻译结果"""
        result_count = self.carousel_widget.get_result_count()

        if result_count == 0:
            QMessageBox.information(self, "提示", "暂无翻译结果可保存")
            return

        if result_count == 1:
            # 单个结果，直接保存
            self.save_single_result()
        else:
            # 多个结果，询问保存方式
            reply = QMessageBox.question(
                self,
                "保存选项",
                f"检测到 {result_count} 个翻译结果。\n\n选择保存方式：",
                QMessageBox.StandardButton.Save | QMessageBox.StandardButton.SaveAll | QMessageBox.StandardButton.Cancel,
                QMessageBox.StandardButton.SaveAll
            )

            if reply == QMessageBox.StandardButton.Save:
                # 保存当前显示的结果
                self.save_current_result()
            elif reply == QMessageBox.StandardButton.SaveAll:
                # 保存所有结果
                self.save_all_results()

    def save_single_result(self):
        """保存单个翻译结果"""
        current_result = self.carousel_widget.get_current_result()
        if not current_result or current_result['image'] is None:
            QMessageBox.information(self, "提示", "暂无翻译结果可保存")
            return

        # 生成默认文件名
        import os
        from datetime import datetime

        if self.current_image_path:
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            default_name = f"{base_name}_translated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        else:
            default_name = f"translated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存翻译结果",
            default_name,
            "PNG图片 (*.png);;JPEG图片 (*.jpg);;所有文件 (*)"
        )

        if file_path:
            try:
                import cv2

                # 保存图像
                success = cv2.imwrite(file_path, current_result['image'])

                if success:
                    self.logger.info(f"翻译结果已保存到: {file_path}")
                    QMessageBox.information(self, "保存成功", f"翻译结果已保存到:\n{file_path}")

                    # 更新翻译结果路径
                    self.translation_result_path = file_path
                else:
                    raise IOError("图像保存失败")

            except Exception as e:
                error_msg = f"保存翻译结果失败: {str(e)}"
                self.logger.error(error_msg)
                QMessageBox.critical(self, "保存失败", error_msg)

    def save_current_result(self):
        """保存当前显示的翻译结果"""
        current_result = self.carousel_widget.get_current_result()
        if not current_result or current_result['image'] is None:
            QMessageBox.information(self, "提示", "暂无翻译结果可保存")
            return

        # 获取当前语言信息
        current_lang_codes = list(self.carousel_widget.translation_results.keys())
        current_index = self.carousel_widget.current_index
        if current_index < len(current_lang_codes):
            lang_code = current_lang_codes[current_index]
            lang_name = self.carousel_widget.language_names[lang_code]
        else:
            lang_name = "unknown"

        # 生成默认文件名
        import os
        from datetime import datetime

        if self.current_image_path:
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
            default_name = f"{base_name}_{lang_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        else:
            default_name = f"translated_{lang_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            f"保存翻译结果 - {lang_name}",
            default_name,
            "PNG图片 (*.png);;JPEG图片 (*.jpg);;所有文件 (*)"
        )

        if file_path:
            try:
                import cv2
                success = cv2.imwrite(file_path, current_result['image'])

                if success:
                    self.logger.info(f"翻译结果已保存到: {file_path}")
                    QMessageBox.information(self, "保存成功", f"翻译结果已保存到:\n{file_path}")
                else:
                    raise IOError("图像保存失败")

            except Exception as e:
                error_msg = f"保存翻译结果失败: {str(e)}"
                self.logger.error(error_msg)
                QMessageBox.critical(self, "保存失败", error_msg)

    def save_all_results(self):
        """保存所有翻译结果"""
        result_count = self.carousel_widget.get_result_count()
        if result_count == 0:
            QMessageBox.information(self, "提示", "暂无翻译结果可保存")
            return

        # 选择保存目录
        save_dir = QFileDialog.getExistingDirectory(
            self,
            "选择保存目录",
            ""
        )

        if not save_dir:
            return

        # 生成基础文件名
        import os
        from datetime import datetime

        if self.current_image_path:
            base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
        else:
            base_name = "translated"

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存所有结果
        saved_count = 0
        failed_count = 0

        for lang_code, result_data in self.carousel_widget.translation_results.items():
            lang_name = self.carousel_widget.language_names[lang_code]

            # 生成文件名
            filename = f"{base_name}_{lang_name}_{timestamp}.png"
            file_path = os.path.join(save_dir, filename)

            try:
                import cv2
                success = cv2.imwrite(file_path, result_data['image'])

                if success:
                    saved_count += 1
                    self.logger.info(f"翻译结果已保存: {file_path}")
                else:
                    failed_count += 1
                    self.logger.error(f"保存失败: {file_path}")

            except Exception as e:
                failed_count += 1
                self.logger.error(f"保存翻译结果失败 ({lang_name}): {e}")

        # 显示保存结果
        if failed_count == 0:
            QMessageBox.information(
                self,
                "保存完成",
                f"成功保存 {saved_count} 个翻译结果到:\n{save_dir}"
            )
        else:
            QMessageBox.warning(
                self,
                "保存完成",
                f"保存完成！\n成功: {saved_count} 个\n失败: {failed_count} 个\n\n保存目录: {save_dir}"
            )

    def open_settings(self):
        """打开设置对话框"""
        from .settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        dialog.settingsChanged.connect(self.on_settings_changed)
        dialog.exec()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 Image Text Translator",
            """
            <h3>Image Text Translator v1.0.0</h3>
            <p>一个基于开源技术栈的本地化图像文本翻译工具</p>
            <p><b>主要特性:</b></p>
            <ul>
            <li>支持80+种语言的OCR识别</li>
            <li>支持200种语言互译</li>
            <li>智能文本压缩</li>
            <li>保持原文字体样式</li>
            <li>完全本地化处理</li>
            </ul>
            <p><b>技术栈:</b> PyQt6, PaddleOCR, NLLB-200, Qwen2.5</p>
            """
        )

    def apply_theme(self, theme_name: str = None):
        """应用主题"""
        if theme_name is None:
            theme_name = self.config.get('ui.theme', 'light')

        # 更新主题管理器的当前主题
        self.theme_manager.set_current_theme(theme_name)

        # 获取主题样式
        styles = self.theme_manager.get_theme_styles(theme_name)

        # 应用主窗口样式
        self.setStyleSheet(styles['main_window'])

        # 应用菜单栏样式
        if hasattr(self, 'menuBar'):
            self.menuBar().setStyleSheet(styles['menu_bar'])

        # 应用全局弹窗样式
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            app.setStyleSheet(app.styleSheet() + styles['message_box'])

        # 应用状态栏样式
        if hasattr(self, 'status_bar'):
            self.status_bar.setStyleSheet(styles['status_bar'])

        # 应用状态标签样式
        if hasattr(self, 'status_label'):
            # 使用状态栏的文字颜色
            status_color = '#5f6368' if theme_name == 'light' else '#cccccc'
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    color: {status_color};
                    background-color: transparent;
                    font-size: 12px;
                    padding: 4px 8px;
                }}
            """)

        # 应用滚动区域样式
        if hasattr(self, 'scroll_area'):
            self.scroll_area.setStyleSheet(styles['scroll_area'])

        # 应用中央部件样式
        central_widget = self.centralWidget()
        if central_widget:
            central_widget.setStyleSheet(f"background-color: {styles.get('background_color', '#f8f9fa' if theme_name == 'light' else '#3c3c3c')};")

        # 应用滚动内容样式
        scroll_content = self.scroll_area.widget() if hasattr(self, 'scroll_area') else None
        if scroll_content:
            scroll_content.setStyleSheet(f"background-color: {styles.get('background_color', '#f8f9fa' if theme_name == 'light' else '#3c3c3c')};")

        # 应用各种框架样式
        self._apply_frame_styles(styles)

        # 应用控件样式
        self._apply_widget_styles(styles)

        # 应用新增组件的样式
        self._apply_additional_styles(styles)

    def _apply_frame_styles(self, styles):
        """应用框架样式"""
        # 语言选择框架
        if hasattr(self, 'lang_frame'):
            self.lang_frame.setStyleSheet(styles['frame_white'])

        # 工作区域框架
        if hasattr(self, 'work_frame'):
            self.work_frame.setStyleSheet(styles['frame_white'])

        # 上传区域
        if hasattr(self, 'upload_area'):
            self.upload_area.setStyleSheet(styles['frame_upload'])

        # 预览框架
        if hasattr(self, 'preview_frame'):
            self.preview_frame.setStyleSheet(styles['frame_preview'])

        # 状态框架
        if hasattr(self, 'status_frame'):
            self.status_frame.setStyleSheet(styles['frame_status'])

        # 按钮框架
        if hasattr(self, 'button_frame'):
            self.button_frame.setStyleSheet(styles['frame_white'])

    def _apply_widget_styles(self, styles):
        """应用控件样式"""
        # 下拉框
        if hasattr(self, 'source_combo'):
            self.source_combo.setStyleSheet(styles['combo_box'])
        if hasattr(self, 'target_combo'):
            self.target_combo.setStyleSheet(styles['combo_box'])

        # 按钮
        if hasattr(self, 'select_file_btn'):
            self.select_file_btn.setStyleSheet(styles['button_primary'])
        if hasattr(self, 'retranslate_btn'):
            self.retranslate_btn.setStyleSheet(styles['button_primary'])
        if hasattr(self, 'save_result_btn'):
            self.save_result_btn.setStyleSheet(styles['button_success'])
        if hasattr(self, 'clear_btn'):
            self.clear_btn.setStyleSheet(styles['button_danger'])

        # 标签
        if hasattr(self, 'upload_image_label'):
            self.upload_image_label.setStyleSheet(styles['label_transparent'])
        if hasattr(self, 'preview_image_label'):
            self.preview_image_label.setStyleSheet(styles['label_transparent'])
        if hasattr(self, 'upload_placeholder'):
            self.upload_placeholder.setStyleSheet(styles['label_placeholder'])
        if hasattr(self, 'preview_placeholder'):
            self.preview_placeholder.setStyleSheet(styles['label_placeholder'])
        if hasattr(self, 'image_info_label'):
            self.image_info_label.setStyleSheet(styles['label_info'])
        if hasattr(self, 'translation_status_label'):
            self.translation_status_label.setStyleSheet(styles['label_status'])

        # 进度条
        if hasattr(self, 'translation_progress'):
            self.translation_progress.setStyleSheet(styles['progress_bar'])

    def _apply_additional_styles(self, styles):
        """应用额外组件的样式"""
        # 应用预览框架样式
        if hasattr(self, 'preview_frame'):
            self.preview_frame.setStyleSheet(styles['frame_white'])

        # 应用预览图片标签样式
        if hasattr(self, 'preview_image_label'):
            self.preview_image_label.setStyleSheet(styles.get('label_secondary', ''))

        # 应用状态框架样式（透明背景）
        if hasattr(self, 'status_frame'):
            self.status_frame.setStyleSheet("QFrame { background-color: transparent; border: none; }")

        # 应用按钮框架样式
        if hasattr(self, 'button_frame'):
            self.button_frame.setStyleSheet(styles['frame_white'])

        # 应用轮播图样式
        if hasattr(self, 'carousel_widget'):
            self.carousel_widget.set_theme_manager(self.theme_manager)
            self.carousel_widget.apply_theme()

        # 应用语言标签样式
        self._apply_language_tag_styles(styles)

    def _apply_language_tag_styles(self, styles):
        """应用语言标签样式"""
        # 获取当前主题
        current_theme = self.theme_manager.get_current_theme()

        # 查找所有语言标签组件并应用样式
        language_tag_widgets = self.findChildren(QWidget, "language_tag_widget")
        for widget in language_tag_widgets:
            if current_theme == 'dark':
                widget.setStyleSheet("""
                    QWidget {
                        background-color: #2d3748;
                        border: 1px solid #4a5568;
                        border-radius: 12px;
                    }
                """)
            else:
                widget.setStyleSheet("""
                    QWidget {
                        background-color: #e8f0fe;
                        border: 1px solid #dadce0;
                        border-radius: 12px;
                    }
                """)

        # 查找所有语言标签文本并应用样式
        language_tag_labels = self.findChildren(QLabel, "language_tag_label")
        for label in language_tag_labels:
            if current_theme == 'dark':
                label.setStyleSheet("""
                    QLabel {
                        color: #a78bfa;
                        font-size: 12px;
                        font-weight: 500;
                    }
                """)
            else:
                label.setStyleSheet("""
                    QLabel {
                        color: #1a73e8;
                        font-size: 12px;
                        font-weight: 500;
                    }
                """)

        # 查找所有删除按钮并应用样式
        remove_buttons = self.findChildren(QPushButton, "language_tag_remove_btn")
        for btn in remove_buttons:
            if current_theme == 'dark':
                btn.setStyleSheet("""
                    QPushButton {
                        border: none;
                        background-color: transparent;
                        color: #a0aec0;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #4a5568;
                        border-radius: 8px;
                        color: #fc8181;
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        border: none;
                        background-color: transparent;
                        color: #5f6368;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #f1f3f4;
                        border-radius: 8px;
                        color: #d93025;
                    }
                """)

    def _apply_single_language_tag_style(self, tag_widget, name_label, remove_btn):
        """为单个语言标签应用当前主题样式"""
        current_theme = self.theme_manager.get_current_theme()

        # 应用标签容器样式
        if current_theme == 'dark':
            tag_widget.setStyleSheet("""
                QWidget {
                    background-color: #2d3748;
                    border: 1px solid #4a5568;
                    border-radius: 12px;
                }
            """)
        else:
            tag_widget.setStyleSheet("""
                QWidget {
                    background-color: #e8f0fe;
                    border: 1px solid #dadce0;
                    border-radius: 12px;
                }
            """)

        # 应用标签文字样式
        if current_theme == 'dark':
            name_label.setStyleSheet("""
                QLabel {
                    color: #a78bfa;
                    font-size: 12px;
                    font-weight: 500;
                }
            """)
        else:
            name_label.setStyleSheet("""
                QLabel {
                    color: #1a73e8;
                    font-size: 12px;
                    font-weight: 500;
                }
            """)

        # 应用删除按钮样式
        if current_theme == 'dark':
            remove_btn.setStyleSheet("""
                QPushButton {
                    border: none;
                    background-color: transparent;
                    color: #a0aec0;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4a5568;
                    border-radius: 8px;
                    color: #fc8181;
                }
            """)
        else:
            remove_btn.setStyleSheet("""
                QPushButton {
                    border: none;
                    background-color: transparent;
                    color: #5f6368;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #f1f3f4;
                    border-radius: 8px;
                    color: #d93025;
                }
            """)

    def on_settings_changed(self):
        """设置变更处理"""
        # 重新加载配置
        self.config = get_config()

        # 应用新主题
        new_theme = self.config.get('ui.theme', 'light')
        self.apply_theme(new_theme)

        # 更新状态栏消息
        self.update_status_bar(f"主题已切换为: {new_theme}")



# 移除主函数，因为 src/main.py 是程序的真正入口点
# 如果需要单独测试此窗口，请运行 src/main.py
