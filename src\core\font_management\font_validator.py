"""
字体验证器

验证字体文件的完整性和可用性
检查字体对特定字符的支持情况
"""

import os
from pathlib import Path
from typing import Optional, Set, List, Dict
import struct

from ...utils.logger import get_logger

class FontValidator:
    """字体验证器"""
    
    def __init__(self):
        self.logger = get_logger()
        
        # 字体文件头签名
        self.font_signatures = {
            b'\x00\x01\x00\x00': 'TTF',  # TrueType
            b'true': 'TTF',              # TrueType (Mac)
            b'OTTO': 'OTF',              # OpenType
            b'wOFF': 'WOFF',             # Web Open Font Format
            b'wOF2': 'WOFF2',            # Web Open Font Format 2.0
        }
        
        self.logger.debug("字体验证器初始化完成")
    
    def validate_font_file(self, font_path: Path) -> bool:
        """
        验证字体文件是否有效
        
        Args:
            font_path: 字体文件路径
            
        Returns:
            是否为有效的字体文件
        """
        if not font_path.exists():
            return False
        
        try:
            # 检查文件大小
            file_size = font_path.stat().st_size
            if file_size == 0:
                self.logger.warning(f"字体文件为空: {font_path}")
                return False
            
            # 检查文件头
            with open(font_path, 'rb') as f:
                header = f.read(4)
                if header not in self.font_signatures:
                    self.logger.warning(f"无效的字体文件头: {font_path}, 头部: {header}")
                    return False
            
            # 基本结构验证
            if not self._validate_font_structure(font_path):
                return False
            
            self.logger.debug(f"字体文件验证通过: {font_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"字体文件验证失败: {font_path}, 错误: {e}")
            return False
    
    def _validate_font_structure(self, font_path: Path) -> bool:
        """验证字体文件的基本结构"""
        try:
            with open(font_path, 'rb') as f:
                # 读取字体头
                header = f.read(4)
                font_type = self.font_signatures.get(header)
                
                if font_type in ['TTF', 'OTF']:
                    return self._validate_opentype_structure(f)
                elif font_type in ['WOFF', 'WOFF2']:
                    # WOFF格式的基本验证
                    return True
                
        except Exception as e:
            self.logger.debug(f"字体结构验证失败: {font_path}, 错误: {e}")
            return False
        
        return False
    
    def _validate_opentype_structure(self, file_handle) -> bool:
        """验证OpenType/TrueType字体结构"""
        try:
            # 重置到文件开头
            file_handle.seek(0)
            
            # 读取字体头 (12字节)
            header = file_handle.read(12)
            if len(header) < 12:
                return False
            
            # 解析表数量
            sfnt_version, num_tables, search_range, entry_selector, range_shift = struct.unpack('>IHHHH', header)
            
            if num_tables == 0 or num_tables > 100:  # 合理的表数量范围
                return False
            
            # 验证表目录
            required_tables = {b'cmap', b'head', b'hhea', b'hmtx', b'maxp', b'name', b'OS/2', b'post'}
            found_tables = set()
            
            for i in range(num_tables):
                table_entry = file_handle.read(16)
                if len(table_entry) < 16:
                    return False
                
                tag, checksum, offset, length = struct.unpack('>4sIII', table_entry)
                found_tables.add(tag)
            
            # 检查必需的表是否存在
            if not required_tables.issubset(found_tables):
                missing = required_tables - found_tables
                self.logger.debug(f"缺少必需的字体表: {missing}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f"OpenType结构验证失败: {e}")
            return False
    
    def check_character_support(self, font_path: Path, characters: str) -> Dict[str, bool]:
        """
        检查字体对特定字符的支持情况
        
        Args:
            font_path: 字体文件路径
            characters: 要检查的字符串
            
        Returns:
            字符支持情况的字典 {字符: 是否支持}
        """
        result = {}
        
        if not self.validate_font_file(font_path):
            # 如果字体文件无效，所有字符都不支持
            return {char: False for char in characters}
        
        try:
            # 尝试使用PIL进行字符支持检查
            try:
                from PIL import Image, ImageDraw, ImageFont
                
                font = ImageFont.truetype(str(font_path), size=24)
                
                for char in characters:
                    try:
                        # 尝试获取字符的边界框
                        bbox = font.getbbox(char)
                        # 如果边界框有效，说明字体支持该字符
                        result[char] = bbox is not None and bbox[2] > bbox[0]
                    except Exception:
                        result[char] = False
                        
            except ImportError:
                # 如果PIL不可用，使用基本的Unicode范围检查
                result = self._check_unicode_ranges(characters)
                
        except Exception as e:
            self.logger.warning(f"字符支持检查失败: {font_path}, 错误: {e}")
            result = {char: False for char in characters}
        
        return result
    
    def _check_unicode_ranges(self, characters: str) -> Dict[str, bool]:
        """基于Unicode范围的字符支持检查"""
        result = {}
        
        # 定义常见的Unicode范围
        unicode_ranges = {
            'latin': (0x0000, 0x007F),
            'latin_extended': (0x0080, 0x024F),
            'cjk': (0x4E00, 0x9FFF),
            'arabic': (0x0600, 0x06FF),
            'devanagari': (0x0900, 0x097F),
            'cyrillic': (0x0400, 0x04FF),
            'thai': (0x0E00, 0x0E7F),
            'hebrew': (0x0590, 0x05FF),
            'bengali': (0x0980, 0x09FF),
            'tamil': (0x0B80, 0x0BFF),
        }
        
        for char in characters:
            char_code = ord(char)
            supported = False
            
            # 检查字符是否在任何已知范围内
            for range_name, (start, end) in unicode_ranges.items():
                if start <= char_code <= end:
                    supported = True
                    break
            
            result[char] = supported
        
        return result
    
    def get_font_metadata(self, font_path: Path) -> Optional[Dict[str, any]]:
        """
        获取字体文件的元数据
        
        Args:
            font_path: 字体文件路径
            
        Returns:
            字体元数据字典，失败时返回None
        """
        if not self.validate_font_file(font_path):
            return None
        
        try:
            metadata = {
                'file_path': str(font_path),
                'file_size': font_path.stat().st_size,
                'file_type': None,
                'family_name': None,
                'style_name': None,
                'version': None,
                'supported_scripts': []
            }
            
            # 检测字体类型
            with open(font_path, 'rb') as f:
                header = f.read(4)
                metadata['file_type'] = self.font_signatures.get(header, 'Unknown')
            
            # 尝试使用PIL获取更多信息
            try:
                from PIL import ImageFont
                font = ImageFont.truetype(str(font_path), size=24)
                
                # 获取字体名称（如果可用）
                if hasattr(font, 'getname'):
                    font_name = font.getname()
                    if isinstance(font_name, tuple) and len(font_name) >= 2:
                        metadata['family_name'] = font_name[0]
                        metadata['style_name'] = font_name[1]
                
            except (ImportError, Exception) as e:
                self.logger.debug(f"无法获取字体详细信息: {e}")
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"获取字体元数据失败: {font_path}, 错误: {e}")
            return None
    
    def test_rendering(self, font_path: Path, test_text: str = "Hello 世界 مرحبا") -> bool:
        """
        测试字体的渲染能力
        
        Args:
            font_path: 字体文件路径
            test_text: 测试文本
            
        Returns:
            是否能够成功渲染
        """
        if not self.validate_font_file(font_path):
            return False
        
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建测试图像
            img = Image.new('RGB', (200, 50), color='white')
            draw = ImageDraw.Draw(img)
            
            # 加载字体
            font = ImageFont.truetype(str(font_path), size=16)
            
            # 尝试渲染文本
            draw.text((10, 10), test_text, font=font, fill='black')
            
            return True
            
        except ImportError:
            # PIL不可用，假设渲染成功
            return True
        except Exception as e:
            self.logger.debug(f"字体渲染测试失败: {font_path}, 错误: {e}")
            return False
    
    def cleanup_invalid_fonts(self, font_directory: Path) -> List[Path]:
        """
        清理无效的字体文件
        
        Args:
            font_directory: 字体目录
            
        Returns:
            被删除的无效字体文件列表
        """
        removed_files = []
        
        if not font_directory.exists():
            return removed_files
        
        for font_file in font_directory.glob('*.{ttf,otf,woff,woff2}'):
            if not self.validate_font_file(font_file):
                try:
                    font_file.unlink()
                    removed_files.append(font_file)
                    self.logger.info(f"删除无效字体文件: {font_file}")
                except Exception as e:
                    self.logger.error(f"删除字体文件失败: {font_file}, 错误: {e}")
        
        return removed_files

# 全局字体验证器实例
_font_validator = None

def get_font_validator() -> FontValidator:
    """获取全局字体验证器实例"""
    global _font_validator
    if _font_validator is None:
        _font_validator = FontValidator()
    return _font_validator
