"""
Noto字体注册表

维护NLLB-200所有语言到Noto字体的完整映射关系
"""

from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

class ScriptType(Enum):
    """文字系统类型"""
    LATIN = "latin"
    CJK = "cjk" 
    ARABIC = "arabic"
    DEVANAGARI = "devanagari"
    CYRILLIC = "cyrillic"
    THAI = "thai"
    HEBREW = "hebrew"
    GREEK = "greek"
    BENGALI = "bengali"
    TAMIL = "tamil"
    GUJARATI = "gujarati"
    KANNADA = "kannada"
    MALAYALAM = "malayalam"
    ORIYA = "oriya"
    PUNJABI = "punjabi"
    TELUGU = "telugu"
    SINHALA = "sinhala"
    MYANMAR = "myanmar"
    KHMER = "khmer"
    LAO = "lao"
    TIBETAN = "tibetan"
    GEORGIAN = "georgian"
    ARMENIAN = "armenian"
    ETHIOPIC = "ethiopic"
    CHEROKEE = "cherokee"
    MONGOLIAN = "mongolian"
    OTHER = "other"

@dataclass
class NotoFontInfo:
    """Noto字体信息"""
    name: str                    # 字体名称
    filename: str               # 文件名
    script: ScriptType          # 文字系统
    download_url: str           # 下载URL
    file_size: int             # 文件大小(字节)
    version: str               # 版本号
    supports_languages: Set[str] # 支持的语言代码
    is_variable: bool = False   # 是否为可变字体
    weight_range: tuple = (400, 400)  # 字重范围

class NotoFontRegistry:
    """Noto字体注册表"""
    
    def __init__(self):
        self._fonts: Dict[str, NotoFontInfo] = {}
        self._language_to_fonts: Dict[str, List[str]] = {}
        self._script_to_fonts: Dict[ScriptType, List[str]] = {}
        self._initialize_registry()
    
    def _initialize_registry(self):
        """初始化字体注册表 - 基于Google Fonts API支持的字体"""
        # 使用Google Fonts API支持的Noto字体定义
        fonts = [
            # 拉丁文字系统 - Noto Sans (支持拉丁文、西里尔文、希腊文)
            NotoFontInfo(
                name="Noto Sans",
                filename="NotoSans-Regular.ttf",
                script=ScriptType.LATIN,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=570000,
                version="v39",
                supports_languages={
                    "eng_Latn", "fra_Latn", "deu_Latn", "spa_Latn", "ita_Latn",
                    "por_Latn", "nld_Latn", "pol_Latn", "ces_Latn", "hun_Latn",
                    "ron_Latn", "hrv_Latn", "slv_Latn", "est_Latn", "lav_Latn",
                    "lit_Latn", "fin_Latn", "swe_Latn", "nor_Latn", "dan_Latn",
                    "isl_Latn", "tur_Latn", "ind_Latn", "msa_Latn", "vie_Latn",
                    "fil_Latn", "swh_Latn", "afr_Latn", "cat_Latn", "eus_Latn",
                    "glg_Latn", "ast_Latn", "oci_Latn", "fur_Latn", "lij_Latn",
                    "lmo_Latn", "vec_Latn", "scn_Latn", "nap_Latn", "lld_Latn",
                    "rus_Cyrl", "ukr_Cyrl", "bel_Cyrl", "bul_Cyrl", "mkd_Cyrl",
                    "srp_Cyrl", "bos_Cyrl", "ell_Grek"
                }
            ),

            # 中日韩文字系统 - 使用Google Fonts API支持的字体
            NotoFontInfo(
                name="Noto Sans SC",
                filename="NotoSansSC-Regular.ttf",
                script=ScriptType.CJK,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=16800000,
                version="v36",
                supports_languages={"zho_Hans"}
            ),

            NotoFontInfo(
                name="Noto Sans TC",
                filename="NotoSansTC-Regular.ttf",
                script=ScriptType.CJK,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=17200000,
                version="v35",
                supports_languages={"zho_Hant"}
            ),

            NotoFontInfo(
                name="Noto Sans JP",
                filename="NotoSansJP-Regular.ttf",
                script=ScriptType.CJK,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=15600000,
                version="v52",
                supports_languages={"jpn_Jpan"}
            ),

            NotoFontInfo(
                name="Noto Sans KR",
                filename="NotoSansKR-Regular.ttf",
                script=ScriptType.CJK,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=11800000,
                version="v25",
                supports_languages={"kor_Hang"}
            ),

            # 阿拉伯文字系统
            NotoFontInfo(
                name="Noto Sans Arabic",
                filename="NotoSansArabic-Regular.ttf",
                script=ScriptType.ARABIC,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=420000,
                version="v18",
                supports_languages={
                    "ara_Arab", "arb_Arab", "arz_Arab", "apc_Arab", "acm_Arab",
                    "ajp_Arab", "aeb_Arab", "ary_Arab", "acq_Arab", "fas_Arab",
                    "urd_Arab", "pus_Arab", "ckb_Arab", "kmr_Arab", "azb_Arab"
                }
            ),

            # 天城文字系统
            NotoFontInfo(
                name="Noto Sans Devanagari",
                filename="NotoSansDevanagari-Regular.ttf",
                script=ScriptType.DEVANAGARI,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=380000,
                version="v25",
                supports_languages={
                    "hin_Deva", "nep_Deva", "mar_Deva", "mai_Deva", "bho_Deva",
                    "mag_Deva", "sck_Deva", "new_Deva", "gom_Deva", "kok_Deva"
                }
            ),

            # 泰文字系统
            NotoFontInfo(
                name="Noto Sans Thai",
                filename="NotoSansThai-Regular.ttf",
                script=ScriptType.THAI,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=65000,
                version="v20",
                supports_languages={"tha_Thai"}
            ),

            # 希伯来文字系统
            NotoFontInfo(
                name="Noto Sans Hebrew",
                filename="NotoSansHebrew-Regular.ttf",
                script=ScriptType.HEBREW,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=95000,
                version="v43",
                supports_languages={"heb_Hebr"}
            ),

            # 孟加拉文字系统
            NotoFontInfo(
                name="Noto Sans Bengali",
                filename="NotoSansBengali-Regular.ttf",
                script=ScriptType.BENGALI,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=280000,
                version="v20",
                supports_languages={"ben_Beng", "asm_Beng"}
            ),

            # 泰米尔文字系统
            NotoFontInfo(
                name="Noto Sans Tamil",
                filename="NotoSansTamil-Regular.ttf",
                script=ScriptType.TAMIL,
                download_url="google_fonts_api",  # 标记使用Google Fonts API下载
                file_size=140000,
                version="v27",
                supports_languages={"tam_Taml"}
            )
        ]
        
        # 注册所有字体
        for font in fonts:
            self._register_font(font)
    
    def _register_font(self, font: NotoFontInfo):
        """注册字体"""
        self._fonts[font.name] = font
        
        # 建立语言到字体的映射
        for lang in font.supports_languages:
            if lang not in self._language_to_fonts:
                self._language_to_fonts[lang] = []
            self._language_to_fonts[lang].append(font.name)
        
        # 建立文字系统到字体的映射
        if font.script not in self._script_to_fonts:
            self._script_to_fonts[font.script] = []
        self._script_to_fonts[font.script].append(font.name)
    
    def get_fonts_for_language(self, language_code: str) -> List[NotoFontInfo]:
        """获取支持指定语言的字体列表"""
        font_names = self._language_to_fonts.get(language_code, [])
        return [self._fonts[name] for name in font_names]
    
    def get_fonts_for_script(self, script: ScriptType) -> List[NotoFontInfo]:
        """获取支持指定文字系统的字体列表"""
        font_names = self._script_to_fonts.get(script, [])
        return [self._fonts[name] for name in font_names]
    
    def get_font_info(self, font_name: str) -> Optional[NotoFontInfo]:
        """获取字体信息"""
        return self._fonts.get(font_name)
    
    def get_all_fonts(self) -> List[NotoFontInfo]:
        """获取所有注册的字体"""
        return list(self._fonts.values())
    
    def get_supported_languages(self) -> Set[str]:
        """获取所有支持的语言代码"""
        return set(self._language_to_fonts.keys())
    
    def is_language_supported(self, language_code: str) -> bool:
        """检查是否支持指定语言"""
        return language_code in self._language_to_fonts

# 全局字体注册表实例
_font_registry = None

def get_font_registry() -> NotoFontRegistry:
    """获取全局字体注册表实例"""
    global _font_registry
    if _font_registry is None:
        _font_registry = NotoFontRegistry()
    return _font_registry
