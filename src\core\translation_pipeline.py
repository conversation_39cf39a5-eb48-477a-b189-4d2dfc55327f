#!/usr/bin/env python3
"""
翻译管道模块 - 翻译和压缩工作流

提供完整的翻译管道，包括语言检测、翻译、压缩和后处理
"""

import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.config import get_config
from ..utils.language_mapping import NLLB_LANGUAGES, convert_paddleocr_to_nllb, is_paddleocr_to_nllb_convertible, get_latin_languages
from .text_region import TextRegion
from .text_processor import TextBlock, TextLayout
from .translator import get_translator, TranslationResult
from .text_compressor import get_text_compressor, CompressionResult
from ..utils.config import get_config



@dataclass
class TranslationPipelineConfig:
    """翻译管道配置"""
    source_language: str = 'unknown'  # 源语言，默认为unknown
    target_language: str = 'unknown'  # 目标语言，默认为unknown
    specific_latin_language: Optional[str] = None  # 当源语言为拉丁语时的具体语言代码
    compression_enabled: Optional[bool] = None  # 是否启用压缩，None表示从配置文件获取
    compression_threshold: Optional[float] = None  # 压缩阈值，None表示从配置文件获取
    compression_strategy: Optional[str] = None  # 压缩策略，None表示从配置文件获取
    compression_model: Optional[str] = None  # 压缩模型名称，None表示从配置文件获取
    preserve_formatting: bool = True  # 是否保留格式
    batch_size: Optional[int] = None  # 批处理大小，None表示从配置文件获取
    timeout: Optional[int] = None  # 超时时间（秒），None表示从配置文件获取

    def __post_init__(self):
        """初始化后处理，从配置文件获取默认值"""
        config = get_config()

        # 从配置文件获取默认值
        if self.compression_enabled is None:
            self.compression_enabled = config.get('translation.enable_compression', True)
        if self.compression_threshold is None:
            self.compression_threshold = config.get('translation.compression_threshold', 1.5)
        if self.compression_strategy is None:
            self.compression_strategy = config.get('translation.default_compression_strategy', 'simple')
        if self.compression_model is None:
            self.compression_model = config.get('models.compression.default_model')
        if self.batch_size is None:
            self.batch_size = config.get('translation.batch_size', 10)
        if self.timeout is None:
            self.timeout = config.get('translation.timeout', 30)


@dataclass
class TranslationPipelineResult:
    """翻译管道结果"""
    original_text: str  # 原始文本
    translated_text: str  # 翻译后文本
    compressed_text: Optional[str] = None  # 压缩后文本
    source_language: str = 'unknown'  # 源语言
    target_language: str = 'unknown'  # 目标语言
    translation_confidence: float = 0.0  # 翻译置信度
    compression_ratio: float = 1.0  # 压缩比例
    processing_time: float = 0.0  # 处理时间
    success: bool = True  # 是否成功


class TranslationPipeline:
    """翻译管道类"""
    
    def __init__(self, config: Optional[TranslationPipelineConfig] = None):
        """
        初始化翻译管道
        
        Args:
            config: 翻译管道配置
        """
        self.logger = get_logger()
        self.app_config = get_config()
        
        # 使用默认配置或传入的配置
        self.config = config or TranslationPipelineConfig()
        
        # 初始化组件
        self.translator = get_translator()

        # 只有在启用压缩且指定了模型时才初始化压缩器
        self.compressor = None
        if self.config.compression_enabled and self.config.compression_model:
            try:
                self.compressor = get_text_compressor(self.config.compression_model)
                self.logger.info(f"压缩器初始化成功，模型: {self.config.compression_model}")
            except Exception as e:
                self.logger.warning(f"压缩器初始化失败: {e}")
                self.logger.warning("将禁用文本压缩功能")
                self.config.compression_enabled = False

        # 验证配置
        self._validate_config()

        self.logger.info("翻译管道初始化完成")

    def _validate_config(self):
        """验证配置参数"""
        # 检查源语言配置
        if self.config.source_language == 'unknown':
            self.logger.warning("源语言未指定，翻译时必须提供具体的源语言")
        elif self.config.source_language not in NLLB_LANGUAGES:
            supported_langs = list(NLLB_LANGUAGES.keys())[:10]  # 只显示前10个
            raise ValueError(f"不支持的源语言代码: {self.config.source_language}。支持的语言代码示例: {supported_langs}...")

        # 检查目标语言配置
        if self.config.target_language == 'unknown':
            self.logger.warning("目标语言未指定，翻译时必须提供具体的目标语言")
        elif self.config.target_language not in NLLB_LANGUAGES:
            supported_langs = list(NLLB_LANGUAGES.keys())[:10]  # 只显示前10个
            raise ValueError(f"不支持的目标语言代码: {self.config.target_language}。支持的语言代码示例: {supported_langs}...")

        # 检查压缩策略
        valid_strategies = ['simple', 'concise', 'keywords']
        if self.config.compression_strategy not in valid_strategies:
            self.logger.warning(f"未知的压缩策略: {self.config.compression_strategy}，将使用默认策略 'simple'")
            self.config.compression_strategy = 'simple'

        # 检查压缩模型
        if self.config.compression_enabled and not self.config.compression_model:
            self.logger.warning("启用了压缩功能但未指定压缩模型，压缩功能将被禁用")
            self.config.compression_enabled = False

    def translate_text(self, text: str, source_lang: Optional[str] = None,
                     target_lang: Optional[str] = None) -> TranslationPipelineResult:
        """
        翻译单个文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言，None表示使用配置中的语言
            target_lang: 目标语言，None表示使用配置中的语言
            
        Returns:
            翻译管道结果
        """
        start_time = time.time()
        
        try:
            # 确定源语言和目标语言
            src_lang = source_lang or self.config.source_language
            tgt_lang = target_lang or self.config.target_language

            # 验证语言参数
            if not src_lang or src_lang in ['auto', 'unknown']:
                raise ValueError("源语言必须指定具体的语言代码，不支持自动检测或unknown")

            # 如果源语言是PaddleOCR格式，转换为NLLB格式
            if src_lang not in NLLB_LANGUAGES:
                if is_paddleocr_to_nllb_convertible(src_lang):
                    original_src_lang = src_lang
                    # 特殊处理拉丁语
                    if src_lang == 'la':
                        src_lang = convert_paddleocr_to_nllb(src_lang, self.config.specific_latin_language)
                        if self.config.specific_latin_language:
                            self.logger.info(f"拉丁语代码转换: {original_src_lang} -> {src_lang} (用户指定: {self.config.specific_latin_language})")
                        else:
                            self.logger.error(f"拉丁语转换失败: 必须指定具体的拉丁语种")
                    else:
                        src_lang = convert_paddleocr_to_nllb(src_lang)
                        self.logger.info(f"源语言代码转换: {original_src_lang} -> {src_lang}")
                else:
                    raise ValueError(f"不支持的源语言代码: {src_lang}，必须使用NLLB-200标准语言代码或PaddleOCR语言代码")

            if not tgt_lang or tgt_lang == 'unknown':
                raise ValueError("目标语言必须指定具体的语言代码，不能为unknown")

            if tgt_lang not in NLLB_LANGUAGES:
                raise ValueError(f"不支持的目标语言代码: {tgt_lang}，必须使用NLLB-200标准语言代码")

            # 执行翻译
            translation_result = self.translator.translate(text, src_lang, tgt_lang)

            # 注意：压缩现在由批处理方法统一处理，这里只做翻译
            # 创建结果（不包含压缩）
            result = TranslationPipelineResult(
                original_text=text,
                translated_text=translation_result.translated_text,
                compressed_text=None,  # 单个文本翻译不进行压缩
                source_language=translation_result.source_language,
                target_language=translation_result.target_language,
                translation_confidence=translation_result.confidence,
                compression_ratio=1.0,  # 无压缩
                processing_time=time.time() - start_time,
                success=True
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"翻译失败: {e}")
            
            # 返回失败结果
            return TranslationPipelineResult(
                original_text=text,
                translated_text=text,  # 失败时返回原文
                source_language=source_lang or self.config.source_language,
                target_language=target_lang or self.config.target_language,
                processing_time=time.time() - start_time,
                success=False
            )
    
    def translate_batch(self, texts: List[str], source_lang: Optional[str] = None,
                       target_lang: Optional[str] = None, regions=None) -> List[TranslationPipelineResult]:
        """
        批量翻译文本 - 使用两阶段处理优化GPU内存使用

        Args:
            texts: 要翻译的文本列表
            source_lang: 源语言，None表示使用配置中的语言
            target_lang: 目标语言，None表示使用配置中的语言
            regions: 可选的TextRegion列表，用于上下文感知压缩

        Returns:
            翻译管道结果列表
        """
        # 确定源语言和目标语言
        src_lang = source_lang or self.config.source_language
        tgt_lang = target_lang or self.config.target_language

        # 验证语言参数
        if not src_lang or src_lang in ['auto', 'unknown']:
            raise ValueError("源语言必须指定具体的语言代码，不支持自动检测或unknown")

        # 如果源语言是PaddleOCR格式，转换为NLLB格式
        if src_lang not in NLLB_LANGUAGES:
            if is_paddleocr_to_nllb_convertible(src_lang):
                original_src_lang = src_lang
                # 特殊处理拉丁语
                if src_lang == 'la':
                    src_lang = convert_paddleocr_to_nllb(src_lang, self.config.specific_latin_language)
                    if self.config.specific_latin_language:
                        self.logger.info(f"拉丁语代码转换: {original_src_lang} -> {src_lang} (用户指定: {self.config.specific_latin_language})")
                    else:
                        self.logger.error("拉丁语转换失败: 必须指定具体的拉丁语种")
                else:
                    src_lang = convert_paddleocr_to_nllb(src_lang)
                    self.logger.info(f"源语言代码转换: {original_src_lang} -> {src_lang}")
            else:
                raise ValueError(f"不支持的源语言代码: {src_lang}，必须使用NLLB-200标准语言代码或PaddleOCR语言代码")

        if not tgt_lang or tgt_lang == 'unknown':
            raise ValueError("目标语言必须指定具体的语言代码，不能为unknown")

        if tgt_lang not in NLLB_LANGUAGES:
            raise ValueError(f"不支持的目标语言代码: {tgt_lang}，必须使用NLLB-200标准语言代码")

        # 阶段1: 批量翻译所有文本
        self.logger.info("🔄 阶段1: 批量翻译文本")
        translation_results = []

        batch_size = self.config.batch_size
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i+batch_size]
            self.logger.debug(f"翻译批次 {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")

            # 翻译当前批次
            for text in batch:
                if text.strip():  # 跳过空文本
                    translation_result = self.translator.translate(text, src_lang, tgt_lang)
                    translation_results.append((text, translation_result))
                else:
                    # 空文本直接添加
                    from .translator import TranslationResult
                    empty_result = TranslationResult(
                        source_text=text,
                        translated_text=text,
                        source_language=src_lang,
                        target_language=tgt_lang,
                        confidence=1.0,
                        processing_time=0.0
                    )
                    translation_results.append((text, empty_result))

        # 阶段2: 释放翻译模型，加载压缩模型进行压缩
        if self.config.compression_enabled and translation_results:
            self.logger.info("🔄 阶段2: 释放翻译模型，准备文本压缩")

            # 释放翻译模型GPU内存
            self._release_translator_memory()

            # 批量压缩翻译结果
            self.logger.info("📝 开始批量文本压缩")

            # 检查是否有regions信息用于上下文感知压缩
            if regions and len(regions) == len(translation_results):
                # 使用上下文感知批量压缩
                self.logger.info("使用上下文感知批量压缩模式")

                # 提取成功翻译的文本和对应的regions
                valid_regions = []
                valid_translated_texts = []
                valid_indices = []

                for i, (original_text, translation_result) in enumerate(translation_results):
                    if translation_result.translated_text and translation_result.translated_text.strip():
                        valid_regions.append(regions[i])
                        valid_translated_texts.append(translation_result.translated_text)
                        valid_indices.append(i)

                if valid_regions:
                    # 执行批量上下文压缩
                    compression_results = self.compressor.compress_batch_with_context(
                        valid_regions,
                        valid_translated_texts,
                        strategy=self.config.compression_strategy
                    )

                    # 将压缩结果映射回原始位置
                    for idx, compression_result in zip(valid_indices, compression_results):
                        original_text, translation_result = translation_results[idx]
                        translation_results[idx] = (original_text, translation_result, compression_result)

                    # 为翻译失败的文本添加None压缩结果
                    for i in range(len(translation_results)):
                        if i not in valid_indices:
                            original_text, translation_result = translation_results[i]
                            translation_results[i] = (original_text, translation_result, None)
                else:
                    # 没有有效的翻译结果，所有压缩结果都为None
                    for i in range(len(translation_results)):
                        original_text, translation_result = translation_results[i]
                        translation_results[i] = (original_text, translation_result, None)
            else:
                # 回退到逐个压缩模式
                self.logger.info("使用逐个压缩模式")
                for i in range(len(translation_results)):
                    original_text, translation_result = translation_results[i]
                    # 检查翻译是否成功（有翻译文本且不为空）
                    if translation_result.translated_text and translation_result.translated_text.strip():
                        self.logger.debug(f"压缩进度: {i+1}/{len(translation_results)}")
                        compression_result = self.compressor.compress(
                            translation_result.translated_text,
                            strategy=self.config.compression_strategy
                        )
                        # 更新翻译结果，添加压缩信息
                        translation_results[i] = (original_text, translation_result, compression_result)
                    else:
                        # 翻译失败的文本跳过压缩
                        translation_results[i] = (original_text, translation_result, None)

        # 构建最终结果
        final_results = []
        for item in translation_results:
            if len(item) == 3:  # 包含压缩结果
                original_text, translation_result, compression_result = item
                compressed_text = compression_result.compressed_text if compression_result else None
                compression_ratio = compression_result.compression_ratio if compression_result else 1.0
            else:  # 只有翻译结果
                original_text, translation_result = item
                compressed_text = None
                compression_ratio = 1.0

            # 创建管道结果
            # 判断翻译是否成功（有翻译文本且不为空）
            translation_success = bool(translation_result.translated_text and translation_result.translated_text.strip())

            pipeline_result = TranslationPipelineResult(
                original_text=original_text,
                translated_text=translation_result.translated_text,
                compressed_text=compressed_text,
                source_language=translation_result.source_language,
                target_language=translation_result.target_language,
                translation_confidence=translation_result.confidence,
                compression_ratio=compression_ratio,
                processing_time=translation_result.processing_time,
                success=translation_success
            )
            final_results.append(pipeline_result)

        return final_results

    def _release_translator_memory(self):
        """释放翻译模型的GPU内存"""
        try:
            if hasattr(self.translator, 'model') and self.translator.model is not None:
                # 直接释放模型和分词器，让PyTorch自动处理GPU内存
                self.translator.model = None
                self.translator.tokenizer = None
                # 清理GPU缓存
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                self.logger.info("✅ 翻译模型GPU内存已释放")
        except Exception as e:
            self.logger.warning(f"释放翻译模型内存时出错: {e}")
    
    def translate_text_regions(self, regions: List[TextRegion], 
                             source_lang: Optional[str] = None,
                             target_lang: Optional[str] = None) -> List[Tuple[TextRegion, TranslationPipelineResult]]:
        """
        翻译文本区域
        
        Args:
            regions: 文本区域列表
            source_lang: 源语言，None表示使用配置中的语言
            target_lang: 目标语言，None表示使用配置中的语言
            
        Returns:
            文本区域和翻译结果的元组列表
        """
        if not regions:
            return []
        
        # 提取文本
        texts = [region.text for region in regions]
        
        # 批量翻译
        translation_results = self.translate_batch(texts, source_lang, target_lang)
        
        # 组合结果
        return list(zip(regions, translation_results))
    
    def translate_text_blocks(self, blocks: List[TextBlock],
                            source_lang: Optional[str] = None,
                            target_lang: Optional[str] = None) -> List[Tuple[TextBlock, List[TranslationPipelineResult]]]:
        """
        翻译文本块
        
        Args:
            blocks: 文本块列表
            source_lang: 源语言，None表示使用配置中的语言
            target_lang: 目标语言，None表示使用配置中的语言
            
        Returns:
            文本块和翻译结果列表的元组列表
        """
        results = []
        
        for block in blocks:
            # 提取区域文本
            region_texts = [region.text for region in block.regions]
            
            # 批量翻译
            translation_results = self.translate_batch(region_texts, source_lang, target_lang)
            
            # 添加结果
            results.append((block, translation_results))
        
        return results
    
    def translate_layout(self, layout: TextLayout,
                       source_lang: Optional[str] = None,
                       target_lang: Optional[str] = None) -> Dict[str, Any]:
        """
        翻译文本布局
        
        Args:
            layout: 文本布局
            source_lang: 源语言，None表示使用配置中的语言
            target_lang: 目标语言，None表示使用配置中的语言
            
        Returns:
            翻译结果字典
        """
        if not layout.blocks:
            return {
                'layout': layout,
                'translations': [],
                'success_rate': 0.0,
                'processing_time': 0.0
            }
        
        start_time = time.time()
        
        # 确定源语言和目标语言
        src_lang = source_lang or self.config.source_language
        tgt_lang = target_lang or self.config.target_language

        # 验证源语言不能为空或auto
        if not src_lang or src_lang == 'auto':
            raise ValueError("源语言必须指定具体的语言代码，不支持自动检测")
        
        # 翻译所有块
        block_translations = self.translate_text_blocks(layout.blocks, src_lang, tgt_lang)
        
        # 计算成功率
        total_translations = sum(len(translations) for _, translations in block_translations)
        successful_translations = sum(
            sum(1 for t in translations if t.success)
            for _, translations in block_translations
        )
        
        success_rate = successful_translations / total_translations if total_translations > 0 else 0.0
        
        result = {
            'layout': layout,
            'translations': block_translations,
            'success_rate': success_rate,
            'processing_time': time.time() - start_time
        }
        
        self.logger.info(f"布局翻译完成，成功率: {success_rate:.2%}")
        return result
    
    def get_final_text(self, translation_result: TranslationPipelineResult) -> str:
        """
        获取最终文本
        
        Args:
            translation_result: 翻译结果
            
        Returns:
            最终文本
        """
        if not translation_result.success:
            return translation_result.original_text
        
        if translation_result.compressed_text:
            return translation_result.compressed_text
        
        return translation_result.translated_text
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            new_config: 新配置字典
        """
        for key, value in new_config.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.logger.debug(f"更新配置: {key} = {value}")
            else:
                self.logger.warning(f"未知配置项: {key}")
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return self.translator.get_supported_languages()
    
    def get_compression_strategies(self) -> Dict[str, str]:
        """获取支持的压缩策略"""
        return self.compressor.get_compression_strategies()
    
    def is_available(self) -> bool:
        """检查翻译管道是否可用"""
        return (self.translator.is_available() and
                self.compressor.is_available())
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """获取管道信息"""
        return {
            "translator_available": self.translator.is_available(),
            "compressor_available": self.compressor.is_available(),
            "config": {
                "source_language": self.config.source_language,
                "target_language": self.config.target_language,
                "compression_enabled": self.config.compression_enabled,
                "compression_threshold": self.config.compression_threshold,
                "compression_strategy": self.config.compression_strategy,
                "preserve_formatting": self.config.preserve_formatting,
                "batch_size": self.config.batch_size,
                "timeout": self.config.timeout
            },
            "available": self.is_available()
        }


# 全局翻译管道实例
_translation_pipeline = None


def get_translation_pipeline(config: Optional[TranslationPipelineConfig] = None) -> TranslationPipeline:
    """获取全局翻译管道实例"""
    global _translation_pipeline
    if _translation_pipeline is None:
        _translation_pipeline = TranslationPipeline(config)
    elif config is not None:
        _translation_pipeline.update_config(vars(config))
    return _translation_pipeline
