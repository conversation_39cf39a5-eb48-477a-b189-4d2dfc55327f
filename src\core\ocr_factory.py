#!/usr/bin/env python3
"""
OCR引擎工厂模块 - 统一OCR引擎接口

提供统一的OCR引擎接口
"""

import os
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from .text_region import TextRegion
from .ocr_engine import get_ocr_engine, OCREngine, OCREngineConfig


@dataclass
class OCRFactoryConfig:
    """OCR工厂配置"""
    default_language: str  # 必须指定的默认语言
    device: str = "cpu"
    ocr_version: str = "PP-OCRv5"
    use_doc_orientation_classify: bool = False
    use_doc_unwarping: bool = False
    use_textline_orientation: bool = False
    # 兼容性选项
    allow_default_language: bool = False  # 是否允许使用默认语言
    strict_language_validation: bool = True  # 是否启用严格语言验证

    def __post_init__(self):
        """初始化后验证"""
        if not self.default_language or self.default_language.strip() == "":
            raise ValueError("default_language不能为空，必须指定具体的语言代码")

        if self.default_language.lower() in ['unknown', 'none', 'null']:
            raise ValueError(f"default_language不能是无效值: '{self.default_language}'，必须指定具体的语言代码")


class OCREngineFactory:
    """
    OCR引擎工厂
    提供统一的OCR接口，支持新旧引擎切换
    """
    
    def __init__(self, config: Optional[OCRFactoryConfig] = None):
        """
        初始化OCR引擎工厂

        Args:
            config: 工厂配置，必须指定default_language

        Raises:
            ValueError: 当未提供配置或语言时
        """
        if config is None:
            raise ValueError("必须提供OCRFactoryConfig配置，且必须指定default_language")

        # 验证语言是否指定
        if not hasattr(config, 'default_language') or not config.default_language:
            raise ValueError("必须在OCRFactoryConfig中指定default_language，不能为空")

        self.config = config

        # 初始化OCR引擎
        self._ocr_engine = None
        self._init_engine()

        print("OCR引擎工厂初始化完成")
    
    def _init_engine(self):
        """初始化OCR引擎"""
        # 使用OCR引擎
        engine_config = OCREngineConfig(
            default_language=self.config.default_language,
            device=self.config.device,
            ocr_version=self.config.ocr_version,
            use_doc_orientation_classify=self.config.use_doc_orientation_classify,
            use_doc_unwarping=self.config.use_doc_unwarping,
            use_textline_orientation=self.config.use_textline_orientation
        )
        self._ocr_engine = get_ocr_engine(engine_config)
        print("使用OCR引擎")
    
    def detect_and_recognize(self, image_path: str, source_language: Optional[str] = None) -> List[TextRegion]:
        """
        检测和识别图像中的文本

        Args:
            image_path: 图像文件路径
            source_language: 源语言代码（必须指定）

        Returns:
            文本区域列表
        """
        try:
            # 使用OCR引擎
            if not source_language:
                if self.config.allow_default_language:
                    source_language = self.config.default_language
                    print(f"未指定源语言，使用默认语言: {source_language}")
                else:
                    raise ValueError("OCR引擎要求必须指定源语言！请提供source_language参数")

            return self._ocr_engine.detect_and_recognize(image_path, source_language)

        except Exception as e:
            print(f"OCR识别失败: {e}")
            return []
    
    def get_supported_languages(self) -> Dict[str, str]:
        """
        获取支持的语言列表
        
        Returns:
            语言代码到语言名称的映射字典
        """
        return self._ocr_engine.get_supported_languages()
    
    def is_language_supported(self, lang: str) -> bool:
        """
        检查是否支持指定语言
        
        Args:
            lang: 语言代码
            
        Returns:
            是否支持
        """
        if hasattr(self._ocr_engine, 'is_language_supported'):
            return self._ocr_engine.is_language_supported(lang)
        else:
            # 旧引擎兼容
            supported_langs = self.get_supported_languages()
            return lang in supported_langs
    
    def validate_language(self, lang: str) -> bool:
        """
        验证语言代码是否有效
        
        Args:
            lang: 语言代码
            
        Returns:
            是否有效
        """
        if not self.config.strict_language_validation:
            return True
        
        return self.is_language_supported(lang)
    
    def get_language_suggestions(self, partial_lang: str = "") -> List[str]:
        """
        获取语言建议（用于自动补全）
        
        Args:
            partial_lang: 部分语言代码
            
        Returns:
            匹配的语言代码列表
        """
        if hasattr(self._ocr_engine, 'get_language_suggestions'):
            return self._ocr_engine.get_language_suggestions(partial_lang)
        else:
            # 旧引擎兼容
            all_langs = self.get_supported_languages()
            if not partial_lang:
                return list(all_langs.keys())
            
            matches = []
            partial_lower = partial_lang.lower()
            for code, name in all_langs.items():
                if (partial_lower in code.lower() or 
                    partial_lower in name.lower()):
                    matches.append(code)
            return matches
    
    def print_supported_languages(self):
        """打印支持的语言列表"""
        if hasattr(self._ocr_engine, 'print_supported_languages'):
            self._ocr_engine.print_supported_languages()
        else:
            # 兼容模式
            print("=== 支持的源语言列表 ===")
            languages = self.get_supported_languages()
            sorted_langs = sorted(languages.items())
            for code, name in sorted_langs:
                print(f"  {code:12} - {name}")
            print(f"\n总计支持 {len(languages)} 种语言")
    



# 全局工厂实例
_ocr_factory = None


def get_ocr_factory(config: Optional[OCRFactoryConfig] = None) -> OCREngineFactory:
    """
    获取OCR引擎工厂实例（单例模式）

    Args:
        config: 工厂配置，必须指定且包含default_language

    Returns:
        OCREngineFactory实例

    Raises:
        ValueError: 当未提供配置时
    """
    global _ocr_factory
    if _ocr_factory is None:
        if config is None:
            raise ValueError("首次创建OCR工厂时必须提供OCRFactoryConfig配置")
        _ocr_factory = OCREngineFactory(config)
    return _ocr_factory


def set_ocr_factory_config(config: OCRFactoryConfig):
    """
    设置OCR工厂配置（重新初始化）
    
    Args:
        config: 新的配置
    """
    global _ocr_factory
    _ocr_factory = OCREngineFactory(config)


# 便利函数
def get_ocr_engine_unified(source_language: Optional[str] = None) -> OCREngineFactory:
    """
    获取统一的OCR引擎接口
    
    Args:
        source_language: 可选的默认源语言
        
    Returns:
        OCR引擎工厂实例
    """
    factory = get_ocr_factory()
    if source_language and factory.config.allow_default_language:
        factory.config.default_language = source_language
    return factory
